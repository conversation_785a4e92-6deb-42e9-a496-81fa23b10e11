/**
 * PromptGenerationService.ts
 * APPPrompt 生成服務
 * 職責：處理 BDD 規範和 AA prompt 到 APPPrompt 的轉換
 */

import { BDDSpec, AAPrompt, APPPromptResult, MenuData } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import { GeminiApiClient } from './GeminiApiClient.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 獲取當前文件的目錄路徑
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * APPPrompt 生成服務
 * 負責將 BDD 規範和 AA prompt 轉換為 APPPrompt
 */
export class PromptGenerationService {
  private logger = createLogger('PromptGenerationService');
  private geminiClient: GeminiApiClient;

  constructor(geminiClient?: GeminiApiClient) {
    this.geminiClient = geminiClient || new GeminiApiClient();
    this.logger.info('PromptGenerationService 已初始化');
  }

  /**
   * 從 BDD 規範生成 APPPrompt
   * @param bddSpec BDD 規範
   * @param menuData 可選的菜單數據
   * @param language 語言設定
   */
  async generateFromBDD(bddSpec: BDDSpec, menuData?: MenuData | null, language?: string): Promise<APPPromptResult> {
    this.logger.info('開始從 BDD 規範生成 APPPrompt', {
      hasMenuData: !!menuData,
      language: language || 'default'
    });

    try {
      // 根據語言設定提示詞語言
      const promptLanguage = this.getPromptLanguage(language);
      
      const bddPrompt = this.createBDDPrompt(bddSpec, menuData, promptLanguage);
      
      this.logger.debug('生成 BDD 轉換提示詞', {
        promptLength: bddPrompt.length
      });

      const response = await this.geminiClient.generateText(bddPrompt);
      const result = this.parseAPPPromptResponse(response);      this.logger.info('BDD 規範轉換成功', {
        responseLength: response.length,
        hasPrompt: !!result.prompt
      });

      return result;
    } catch (error) {
      this.logger.error('BDD 規範轉換失敗', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 從 AA Prompt 生成 APPPrompt
   * @param aaPrompt AA Prompt
   * @param menuData 可選的菜單數據
   * @param language 語言設定
   */
  async generateFromAA(aaPrompt: AAPrompt, menuData?: MenuData | null, language?: string): Promise<APPPromptResult> {
    this.logger.info('開始從 AA Prompt 生成 APPPrompt', {
      hasMenuData: !!menuData,
      language: language || 'default'
    });

    try {
      const promptLanguage = this.getPromptLanguage(language);
      
      const aaPromptText = this.createAAPrompt(aaPrompt, menuData, promptLanguage);
      
      this.logger.debug('生成 AA 轉換提示詞', {
        promptLength: aaPromptText.length
      });

      const response = await this.geminiClient.generateText(aaPromptText);
      const result = this.parseAPPPromptResponse(response);      this.logger.info('AA Prompt 轉換成功', {
        responseLength: response.length,
        hasPrompt: !!result.prompt
      });

      return result;
    } catch (error) {
      this.logger.error('AA Prompt 轉換失敗', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 從自然語言解析生成 APPPrompt
   * @param input 自然語言輸入
   * @param menuData 可選的菜單數據
   * @param sessionId 會話 ID
   */
  async parseNaturalLanguageToAppPrompt(input: string, menuData?: MenuData | null, sessionId?: string): Promise<APPPromptResult> {
    this.logger.info('開始從自然語言解析生成 APPPrompt', {
      inputLength: input.length,
      hasMenuData: !!menuData,
      sessionId
    });

    try {
      const parsePrompt = this.createNaturalLanguageParsePrompt(input, menuData);
      
      this.logger.debug('生成自然語言解析提示詞', {
        promptLength: parsePrompt.length
      });

      const response = await this.geminiClient.generateText(parsePrompt);
      const result = this.parseAPPPromptResponse(response);      this.logger.info('自然語言解析成功', {
        responseLength: response.length,
        hasPrompt: !!result.prompt
      });

      return result;
    } catch (error) {
      this.logger.error('自然語言解析失敗', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 獲取提示詞語言設定
   */
  private getPromptLanguage(language?: string): string {
    switch (language) {
      case 'en':
        return 'English';
      case 'ja-JP':
      case 'ja':
        return '日本語';
      case 'zh':
      case 'tw':
      default:
        return '繁體中文';
    }
  }

  /**
   * 創建 BDD 轉換提示詞
   */
  private createBDDPrompt(bddSpec: BDDSpec, menuData?: MenuData | null, language: string = '繁體中文'): string {
    const menuContext = menuData ? `\\n\\n菜單數據參考：\\n${JSON.stringify(menuData, null, 2)}` : '';
    
    return `請根據以下 BDD 規範生成對應的 APPPrompt。使用語言：${language}

BDD 規範：
${JSON.stringify(bddSpec, null, 2)}${menuContext}

請生成一個結構化的 APPPrompt，包含：
1. 系統角色定義
2. 任務描述
3. 輸入格式說明
4. 輸出格式要求
5. 範例對話

輸出格式必須是有效的 JSON，格式如下：
{
  "success": true,
  "appPrompt": "生成的 APPPrompt 內容",
  "metadata": {
    "language": "${language}",
    "source": "BDD",
    "generatedAt": "${new Date().toISOString()}"
  }
}`;
  }

  /**
   * 創建 AA Prompt 轉換提示詞
   */
  private createAAPrompt(aaPrompt: AAPrompt, menuData?: MenuData | null, language: string = '繁體中文'): string {
    const menuContext = menuData ? `\\n\\n菜單數據參考：\\n${JSON.stringify(menuData, null, 2)}` : '';
    
    return `請根據以下 AA Prompt 生成對應的 APPPrompt。使用語言：${language}

AA Prompt：
${JSON.stringify(aaPrompt, null, 2)}${menuContext}

請將 AA Prompt 轉換為更具體的 APPPrompt，優化結構和表達方式。

輸出格式必須是有效的 JSON，格式如下：
{
  "success": true,
  "appPrompt": "生成的 APPPrompt 內容",
  "metadata": {
    "language": "${language}",
    "source": "AA",
    "generatedAt": "${new Date().toISOString()}"
  }
}`;
  }

  /**
   * 創建自然語言解析提示詞
   */
  private createNaturalLanguageParsePrompt(input: string, menuData?: MenuData | null): string {
    const menuContext = menuData ? `\\n\\n可用菜單：\\n${JSON.stringify(menuData, null, 2)}` : '';
    
    return `請分析以下自然語言輸入，生成對應的 APPPrompt：

用戶輸入：${input}${menuContext}

請生成一個能處理此類輸入的 APPPrompt。

輸出格式必須是有效的 JSON，格式如下：
{
  "success": true,
  "appPrompt": "生成的 APPPrompt 內容",
  "metadata": {
    "language": "繁體中文",
    "source": "NaturalLanguage",
    "generatedAt": "${new Date().toISOString()}"
  }
}`;
  }
  /**
   * 解析 APPPrompt 響應
   */
  private parseAPPPromptResponse(response: string): APPPromptResult {
    try {      // 清理響應，移除可能的 markdown 標記（修復正則表達式）
      const cleanResponse = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      
      const parsed = JSON.parse(cleanResponse);
      
      if (parsed.success && parsed.appPrompt) {
        return {
          prompt: parsed.appPrompt,
          parameters: {},
          metadata: {
            source: 'natural',
            generatedAt: new Date(),
            aiGenerated: true,
            ...parsed.metadata
          }
        };
      } else {
        throw new Error('響應格式不正確');
      }
    } catch (error) {
      this.logger.error('解析 APPPrompt 響應失敗', error instanceof Error ? error : new Error(String(error)), {
        responseLength: response.length
      });
        // 返回錯誤格式，使用空的 prompt
      return {
        prompt: `解析失敗: ${error instanceof Error ? error.message : String(error)}`,
        parameters: {},
        metadata: {
          source: 'natural',
          generatedAt: new Date(),
          aiGenerated: true
        }
      };
    }
  }
}

export default new PromptGenerationService();
