/**
 * Android 語音引擎檢測工具
 * 檢測設備是否支援語音識別功能
 */

class AndroidSpeechDetector {
  constructor() {
    this.isAndroid = this.detectAndroid();
    this.isWebView = this.detectWebView();
    this.speechEngines = [];
    this.hasNativeSpeech = false;
    this.hasTTS = false;
  }
  
  /**
   * 檢測是否為 Android 設備
   */
  detectAndroid() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    return /android/i.test(userAgent);
  }
  
  /**
   * 檢測是否為 WebView 環境
   */
  detectWebView() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    return /wv/.test(userAgent) || /Version\/[\d.]+.*Chrome/.test(userAgent);
  }
  
  /**
   * 檢測語音識別支援
   */
  async detectSpeechRecognition() {
    const results = {
      webSpeechAPI: false,
      capacitorPlugin: false,
      androidNative: false,
      fallbackAvailable: true
    };
    
    // 檢測 Web Speech API
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      results.webSpeechAPI = true;
      console.log('✅ Web Speech API 可用');
      
      // 測試是否真的可以使用
      try {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        recognition.lang = 'zh-TW';
        console.log('✅ Web Speech API 實例創建成功');
      } catch (error) {
        console.warn('⚠️ Web Speech API 實例創建失敗:', error);
        results.webSpeechAPI = false;
      }
    } else {
      console.log('❌ Web Speech API 不可用');
    }
    
    // 檢測 Capacitor 插件
    if (typeof window.Capacitor !== 'undefined') {
      try {
        const { SpeechRecognition } = window.Capacitor.Plugins;
        if (SpeechRecognition) {
          // 檢查權限
          const permission = await SpeechRecognition.requestPermissions();
          if (permission.speechRecognition === 'granted') {
            // 檢查可用性
            const available = await SpeechRecognition.available();
            if (available.available) {
              results.capacitorPlugin = true;
              results.androidNative = true;
              console.log('✅ Capacitor 語音識別插件可用');
            } else {
              console.log('❌ 設備不支援語音識別');
            }
          } else {
            console.log('❌ 語音識別權限未授予');
          }
        }
      } catch (error) {
        console.warn('⚠️ Capacitor 語音識別檢測失敗:', error);
      }
    } else {
      console.log('ℹ️ 非 Capacitor 環境');
    }
    
    return results;
  }
  
  /**
   * 檢測文字轉語音支援
   */
  async detectTextToSpeech() {
    const results = {
      webSpeechSynthesis: false,
      capacitorTTS: false,
      androidNative: false
    };
    
    // 檢測 Web Speech Synthesis API
    if ('speechSynthesis' in window) {
      results.webSpeechSynthesis = true;
      console.log('✅ Web Speech Synthesis API 可用');
      
      // 獲取可用語音
      const voices = speechSynthesis.getVoices();
      const chineseVoices = voices.filter(voice => 
        voice.lang.includes('zh') || voice.lang.includes('cmn')
      );
      console.log(`📢 可用中文語音: ${chineseVoices.length} 個`);
      chineseVoices.forEach(voice => {
        console.log(`  - ${voice.name} (${voice.lang})`);
      });
    } else {
      console.log('❌ Web Speech Synthesis API 不可用');
    }
    
    // 檢測 Capacitor TTS 插件
    if (typeof window.Capacitor !== 'undefined') {
      try {
        const { TextToSpeech } = window.Capacitor.Plugins;
        if (TextToSpeech) {
          // 獲取支援的語言
          const languages = await TextToSpeech.getSupportedLanguages();
          const supportsChinese = languages.languages.some(lang => 
            lang.includes('zh') || lang.includes('cmn')
          );
          
          if (supportsChinese) {
            results.capacitorTTS = true;
            results.androidNative = true;
            console.log('✅ Capacitor TTS 插件可用');
            console.log('📢 支援的語言:', languages.languages);
          } else {
            console.log('❌ Capacitor TTS 不支援中文');
          }
        }
      } catch (error) {
        console.warn('⚠️ Capacitor TTS 檢測失敗:', error);
      }
    }
    
    return results;
  }
  
  /**
   * 檢測麥克風權限
   */
  async detectMicrophonePermission() {
    const results = {
      granted: false,
      available: false,
      error: null
    };
    
    try {
      // 檢查權限 API
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'microphone' });
        console.log('🎤 麥克風權限狀態:', permission.state);
        results.granted = permission.state === 'granted';
      }
      
      // 檢查 getUserMedia 是否可用（Android WebView 兼容性）
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.warn('⚠️ getUserMedia 不可用，可能是 Android WebView 限制');
        results.available = false;
        results.error = 'getUserMedia API 不可用於此環境';
        return results;
      }

      // 嘗試獲取媒體流
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      results.available = true;
      results.granted = true;

      // 立即停止流
      stream.getTracks().forEach(track => track.stop());
      console.log('✅ 麥克風可用且權限已授予');

    } catch (error) {
      console.warn('⚠️ 麥克風檢測失敗:', error);
      results.error = error.message;

      if (error.name === 'NotAllowedError') {
        results.granted = false;
        console.log('❌ 麥克風權限被拒絕');
      } else if (error.name === 'NotFoundError') {
        results.available = false;
        console.log('❌ 未找到麥克風設備');
      } else if (error.message.includes('getUserMedia') || error.message.includes('undefined')) {
        results.available = false;
        console.log('❌ Android WebView 環境不支援 getUserMedia');
      }
    }
    
    return results;
  }
  
  /**
   * 完整的設備能力檢測
   */
  async detectCapabilities() {
    console.log('🔍 開始檢測設備語音能力...');
    console.log('📱 設備信息:');
    console.log(`  - User Agent: ${navigator.userAgent}`);
    console.log(`  - 是否為 Android: ${this.isAndroid}`);
    console.log(`  - 是否為 WebView: ${this.isWebView}`);
    console.log(`  - Capacitor 環境: ${typeof window.Capacitor !== 'undefined'}`);
    
    const capabilities = {
      device: {
        isAndroid: this.isAndroid,
        isWebView: this.isWebView,
        isCapacitor: typeof window.Capacitor !== 'undefined',
        userAgent: navigator.userAgent
      },
      microphone: await this.detectMicrophonePermission(),
      speechRecognition: await this.detectSpeechRecognition(),
      textToSpeech: await this.detectTextToSpeech(),
      recommendations: []
    };
    
    // 生成建議
    this.generateRecommendations(capabilities);
    
    console.log('🎯 檢測完成，結果:', capabilities);
    return capabilities;
  }
  
  /**
   * 生成使用建議
   */
  generateRecommendations(capabilities) {
    const recommendations = [];
    
    // 語音識別建議
    if (!capabilities.speechRecognition.webSpeechAPI && 
        !capabilities.speechRecognition.capacitorPlugin) {
      recommendations.push({
        type: 'error',
        message: '設備不支援語音識別，建議使用文字輸入作為替代方案'
      });
    } else if (capabilities.speechRecognition.capacitorPlugin) {
      recommendations.push({
        type: 'success',
        message: '建議使用 Capacitor 語音識別插件，兼容性最佳'
      });
    } else if (capabilities.speechRecognition.webSpeechAPI) {
      recommendations.push({
        type: 'warning',
        message: '可使用 Web Speech API，但在某些 Android 設備上可能不穩定'
      });
    }
    
    // 麥克風建議
    if (!capabilities.microphone.available) {
      recommendations.push({
        type: 'error',
        message: '未檢測到麥克風設備，無法使用語音功能'
      });
    } else if (!capabilities.microphone.granted) {
      recommendations.push({
        type: 'warning',
        message: '需要用戶授予麥克風權限才能使用語音識別'
      });
    }
    
    // TTS 建議
    if (capabilities.textToSpeech.capacitorTTS) {
      recommendations.push({
        type: 'success',
        message: '建議使用 Capacitor TTS 插件進行語音播放'
      });
    } else if (capabilities.textToSpeech.webSpeechSynthesis) {
      recommendations.push({
        type: 'info',
        message: '可使用 Web Speech Synthesis API 進行語音播放'
      });
    }
    
    // Android 特殊建議
    if (capabilities.device.isAndroid && capabilities.device.isWebView) {
      recommendations.push({
        type: 'warning',
        message: 'Android WebView 環境，建議優先使用 Capacitor 插件'
      });
    }
    
    capabilities.recommendations = recommendations;
  }
  
  /**
   * 生成診斷報告
   */
  generateDiagnosticReport(capabilities) {
    let report = '=== 語音功能診斷報告 ===\n\n';
    
    // 設備信息
    report += '📱 設備信息:\n';
    report += `  - 平台: ${capabilities.device.isAndroid ? 'Android' : '其他'}\n`;
    report += `  - 環境: ${capabilities.device.isWebView ? 'WebView' : '瀏覽器'}\n`;
    report += `  - Capacitor: ${capabilities.device.isCapacitor ? '是' : '否'}\n\n`;
    
    // 麥克風狀態
    report += '🎤 麥克風狀態:\n';
    report += `  - 設備可用: ${capabilities.microphone.available ? '是' : '否'}\n`;
    report += `  - 權限授予: ${capabilities.microphone.granted ? '是' : '否'}\n`;
    if (capabilities.microphone.error) {
      report += `  - 錯誤信息: ${capabilities.microphone.error}\n`;
    }
    report += '\n';
    
    // 語音識別
    report += '🎙️ 語音識別支援:\n';
    report += `  - Web Speech API: ${capabilities.speechRecognition.webSpeechAPI ? '是' : '否'}\n`;
    report += `  - Capacitor 插件: ${capabilities.speechRecognition.capacitorPlugin ? '是' : '否'}\n`;
    report += `  - Android 原生: ${capabilities.speechRecognition.androidNative ? '是' : '否'}\n\n`;
    
    // 文字轉語音
    report += '📢 文字轉語音支援:\n';
    report += `  - Web Speech Synthesis: ${capabilities.textToSpeech.webSpeechSynthesis ? '是' : '否'}\n`;
    report += `  - Capacitor TTS: ${capabilities.textToSpeech.capacitorTTS ? '是' : '否'}\n\n`;
    
    // 建議
    report += '💡 使用建議:\n';
    capabilities.recommendations.forEach((rec, index) => {
      const icon = rec.type === 'error' ? '❌' : 
                   rec.type === 'warning' ? '⚠️' : 
                   rec.type === 'success' ? '✅' : 'ℹ️';
      report += `  ${index + 1}. ${icon} ${rec.message}\n`;
    });
    
    return report;
  }
}

// 創建全局實例
window.androidSpeechDetector = new AndroidSpeechDetector();

// 便利函數
window.detectSpeechCapabilities = () => window.androidSpeechDetector.detectCapabilities();
window.generateSpeechReport = async () => {
  const capabilities = await window.androidSpeechDetector.detectCapabilities();
  return window.androidSpeechDetector.generateDiagnosticReport(capabilities);
};
