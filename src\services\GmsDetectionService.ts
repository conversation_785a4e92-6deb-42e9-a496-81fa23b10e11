/**
 * GmsDetectionService.ts
 * 檢測Google Mobile Services (GMS) 和相關服務的可用性
 * 用於決定是否使用Firebase或Mock服務
 */
import { createLogger } from '../utils/Logger.js';

const logger = createLogger('GmsDetectionService');

/**
 * GMS檢測結果接口
 */
export interface GmsDetectionResult {
  /** 是否有Google Services Framework */
  hasGoogleServices: boolean;
  /** Firebase是否可用 */
  firebaseAvailable: boolean;
  /** Web Speech API是否可用 */
  webSpeechAvailable: boolean;
  /** 檢測時間戳 */
  detectedAt: Date;
  /** 檢測詳細信息 */
  details: {
    userAgent: string;
    platform: string;
    firebaseError?: string;
    speechError?: string;
  };
}

/**
 * Google Mobile Services 檢測服務
 */
export class GmsDetectionService {
  private logger = createLogger('GmsDetectionService');
  private cachedResult: GmsDetectionResult | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分鐘緩存
  
  /**
   * 檢測GMS和相關服務的可用性
   * @param forceRefresh 是否強制重新檢測
   * @returns 檢測結果
   */
  async detectGmsAvailability(forceRefresh: boolean = false): Promise<GmsDetectionResult> {
    // 檢查緩存
    if (!forceRefresh && this.cachedResult && this.isCacheValid()) {
      this.logger.debug('使用緩存的GMS檢測結果');
      return this.cachedResult;
    }
    
    this.logger.info('開始檢測Google Services Framework可用性');
    
    const result: GmsDetectionResult = {
      hasGoogleServices: false,
      firebaseAvailable: false,
      webSpeechAvailable: false,
      detectedAt: new Date(),
      details: {
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js Server',
        platform: typeof navigator !== 'undefined' ? navigator.platform : process.platform || 'server'
      }
    };
    
    // 檢測Firebase可用性
    result.firebaseAvailable = await this.testFirebaseAvailability(result.details);
    
    // 檢測Web Speech API可用性
    result.webSpeechAvailable = this.testWebSpeechAvailability(result.details);
    
    // 基於檢測結果判斷是否有Google Services
    result.hasGoogleServices = this.determineGoogleServicesAvailability(result);
    
    // 緩存結果
    this.cachedResult = result;
    
    this.logger.info('GMS檢測完成', {
      hasGoogleServices: result.hasGoogleServices,
      firebaseAvailable: result.firebaseAvailable,
      webSpeechAvailable: result.webSpeechAvailable
    });
    
    return result;
  }
  
  /**
   * 測試Firebase的可用性
   * @param details 詳細信息對象，用於記錄錯誤
   * @returns Firebase是否可用
   */
  private async testFirebaseAvailability(details: GmsDetectionResult['details']): Promise<boolean> {
    try {
      // 嘗試動態導入Firebase
      const { initializeApp, deleteApp } = await import('firebase/app');
      const { getFirestore, connectFirestoreEmulator } = await import('firebase/firestore');
      
      // 嘗試創建一個測試用的Firebase應用
      const testConfig = {
        apiKey: 'test-key',
        authDomain: 'test.firebaseapp.com',
        projectId: 'test-project',
        storageBucket: 'test.appspot.com',
        messagingSenderId: '123456789',
        appId: 'test-app-id'
      };
      
      const testApp = initializeApp(testConfig, 'gms-detection-test');
      const testDb = getFirestore(testApp);
      
      // 如果能成功創建，說明Firebase基本可用
      this.logger.debug('Firebase基本功能測試通過');
      
      // 清理測試應用
      await deleteApp(testApp);
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      details.firebaseError = errorMessage;
      this.logger.debug('Firebase不可用', { error: errorMessage });
      return false;
    }
  }
  
  /**
   * 測試Web Speech API的可用性
   * @param details 詳細信息對象，用於記錄錯誤
   * @returns Web Speech API是否可用
   */
  private testWebSpeechAvailability(details: GmsDetectionResult['details']): boolean {
    try {
      // 檢查是否在瀏覽器環境中
      if (typeof window === 'undefined') {
        details.speechError = '非瀏覽器環境，Web Speech API不可用';
        this.logger.debug('Web Speech API不可用：非瀏覽器環境');
        return false;
      }
      
      // 檢查SpeechRecognition API
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        details.speechError = 'SpeechRecognition API不存在';
        this.logger.debug('Web Speech API不可用：API不存在');
        return false;
      }
      
      // 嘗試創建SpeechRecognition實例
      const recognition = new SpeechRecognition();
      
      if (!recognition) {
        details.speechError = '無法創建SpeechRecognition實例';
        this.logger.debug('Web Speech API不可用：無法創建實例');
        return false;
      }
      
      this.logger.debug('Web Speech API可用');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      details.speechError = errorMessage;
      this.logger.debug('Web Speech API不可用', { error: errorMessage });
      return false;
    }
  }
  
  /**
   * 基於各項檢測結果判斷Google Services的整體可用性
   * @param result 檢測結果
   * @returns 是否有Google Services
   */
  private determineGoogleServicesAvailability(result: GmsDetectionResult): boolean {
    // 檢查User Agent中是否包含GMS相關標識
    const userAgent = result.details.userAgent.toLowerCase();
    const hasGmsInUserAgent = userAgent.includes('gms') || 
                             userAgent.includes('google') ||
                             userAgent.includes('play services');
    
    // 檢查是否在已知的無GMS環境中
    const isKnownNonGmsEnvironment = this.isKnownNonGmsEnvironment(userAgent);
    
    // 綜合判斷
    if (isKnownNonGmsEnvironment) {
      this.logger.debug('檢測到已知的無GMS環境');
      return false;
    }
    
    // 如果Firebase和Web Speech API都可用，很可能有GMS
    if (result.firebaseAvailable && result.webSpeechAvailable) {
      this.logger.debug('Firebase和Web Speech API都可用，判斷為有GMS');
      return true;
    }
    
    // 如果都不可用，很可能沒有GMS
    if (!result.firebaseAvailable && !result.webSpeechAvailable) {
      this.logger.debug('Firebase和Web Speech API都不可用，判斷為無GMS');
      return false;
    }
    
    // 部分可用的情況，基於User Agent判斷
    this.logger.debug('部分服務可用，基於User Agent判斷', { hasGmsInUserAgent });
    return hasGmsInUserAgent;
  }
  
  /**
   * 檢查是否為已知的無GMS環境
   * @param userAgent User Agent字符串
   * @returns 是否為已知的無GMS環境
   */
  private isKnownNonGmsEnvironment(userAgent: string): boolean {
    const nonGmsIndicators = [
      'huawei',
      'harmonyos',
      'emui',
      'lineageos',
      'cyanogenmod',
      'microG',
      'fdroid'
    ];
    
    return nonGmsIndicators.some(indicator => userAgent.includes(indicator));
  }
  
  /**
   * 檢查緩存是否仍然有效
   * @returns 緩存是否有效
   */
  private isCacheValid(): boolean {
    if (!this.cachedResult) {
      return false;
    }
    
    const now = Date.now();
    const cacheTime = this.cachedResult.detectedAt.getTime();
    
    return (now - cacheTime) < this.CACHE_DURATION;
  }
  
  /**
   * 清除緩存的檢測結果
   */
  clearCache(): void {
    this.cachedResult = null;
    this.logger.debug('已清除GMS檢測緩存');
  }
  
  /**
   * 獲取當前緩存的檢測結果（如果有的話）
   * @returns 緩存的檢測結果或null
   */
  getCachedResult(): GmsDetectionResult | null {
    if (this.isCacheValid()) {
      return this.cachedResult;
    }
    return null;
  }
}

export default new GmsDetectionService();