/**
 * OrderServiceV2.ts
 * 使用統一類型系統的新版本訂單服務
 * 作為漸進式重構的一部分，與原 OrderService.ts 並行存在
 */
import { db, collection, doc, getDoc, updateDoc, addDoc, Timestamp } from '../config/firebase.js';
import { createLogger } from '../utils/Logger.js';
import { Order, OrderItem, OrderStatus } from '../types/entities/Order.js';

const logger = createLogger('OrderServiceV2');

export class OrderServiceV2 {
  constructor() {
    // 觸發 Firebase 初始化，如果失敗會拋出異常
    // 這樣 order.ts 中的 try-catch 就能捕獲到異常
    try {
      // 嘗試獲取數據庫實例來觸發初始化
      db();
      logger.info('Firebase 初始化成功');
    } catch (error) {
      logger.error('Firebase 初始化失敗，可能是因為設備沒有 Google 服務框架');
      throw new Error('Firebase 服務不可用，請檢查設備是否支援 Google 服務框架');
    }
  }
  /**
   * 創建新訂單
   * @param items 訂單項目陣列
   * @param userId 用戶ID（可選）
   * @returns 創建的訂單
   */
  async createOrder(items: OrderItem[], userId?: string): Promise<Order> {
    try {
      // 獲取數據庫實例
      const database = db();
      
      // 合併重複項目並過濾無效項目
      const mergedItems = this.mergeOrderItems(items);
      
      logger.debug('合併並過濾訂單項目', {
        originalItemCount: items.length,
        mergedCount: mergedItems.length
      });
      
      // 計算總金額
      const totalAmount = mergedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      
      // 創建訂單數據 - 使用統一的 Order 類型
      const orderData: Order = {
        items: mergedItems,
        totalAmount,
        status: OrderStatus.PENDING,
        timestamp: Timestamp.now().toDate()
      };
      
      // 添加用戶ID（如果提供且有效）
      if (userId && typeof userId === 'string' && userId.trim()) {
        orderData.userId = userId.trim();
        logger.info('添加用戶ID到訂單', { userId: orderData.userId });
      }
      
      // 保存到 Firebase
      const ordersCollection = collection(database, 'orders');
      const docRef = await addDoc(ordersCollection, {
        ...orderData,
        // 將 Date 轉換為 Timestamp 以符合 Firebase 格式
        timestamp: Timestamp.fromDate(orderData.timestamp)
      });
      
      logger.info('訂單創建成功', { 
        orderId: docRef.id, 
        itemCount: mergedItems.length, 
        totalAmount 
      });
      
      return { ...orderData, id: docRef.id };
    } catch (error) {
      logger.error('創建訂單失敗', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 合併相同商品的訂單項目並過濾無效數據
   */
  private mergeOrderItems(items: OrderItem[]): OrderItem[] {
    // 過濾無效項目
    const validItems = items.filter(item => 
      item && 
      item.name && 
      typeof item.name === 'string' &&
      item.name.trim().length > 0 &&
      typeof item.quantity === 'number' && 
      item.quantity > 0 &&
      typeof item.price === 'number' && 
      item.price >= 0 &&
      // 排除純數字名稱（測試數據）
      !/^\d+$/.test(item.name.trim())
    );
    
    logger.debug('項目過濾結果', { 
      原始數量: items.length, 
      有效數量: validItems.length 
    });
    
    // 使用 Map 合併相同名稱的項目
    const itemMap = new Map<string, OrderItem>();
    
    for (const item of validItems) {
      // 清理項目名稱
      const cleanName = item.name
        .replace(/\n\s+/g, ' ')
        .replace(/精確匹配.*$/, '')
        .trim();
      
      if (itemMap.has(cleanName)) {
        // 合併數量
        const existingItem = itemMap.get(cleanName)!;
        existingItem.quantity += item.quantity;
      } else {
        // 添加新項目
        itemMap.set(cleanName, {
          ...item,
          name: cleanName
        });
      }
    }
    
    return Array.from(itemMap.values());
  }

  /**
   * 確認訂單
   */
  async confirmOrder(orderId: string): Promise<Order> {
    try {
      const database = db();
      const orderRef = doc(database, 'orders', orderId);
      
      // 更新狀態為已確認
      await updateDoc(orderRef, {
        status: OrderStatus.CONFIRMED,
        confirmedAt: Timestamp.now()
      });
      
      logger.info('訂單已確認', { orderId });
      
      // 模擬自動進入準備階段
      setTimeout(async () => {
        try {
          await updateDoc(orderRef, { 
            status: OrderStatus.PREPARING,
            preparingStartedAt: Timestamp.now()
          });
          logger.info('訂單進入準備階段', { orderId });
        } catch (error) {
          logger.error('更新訂單準備狀態失敗', error instanceof Error ? error : new Error(String(error)));
        }
      }, 60000); // 1分鐘後自動開始準備
      
      // 回傳更新後的訂單
      const orderSnapshot = await getDoc(orderRef);
      if (!orderSnapshot.exists()) {
        throw new Error(`訂單 ${orderId} 不存在`);
      }
      
      const orderData = orderSnapshot.data() as Omit<Order, 'id'>;
      return { 
        id: orderId, 
        ...orderData,
        // 將 Timestamp 轉換為 Date
        timestamp: orderData.timestamp instanceof Timestamp 
          ? (orderData.timestamp as any).toDate() 
          : orderData.timestamp
      };
    } catch (error) {
      logger.error('確認訂單失敗', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 獲取訂單詳情
   */
  async getOrder(orderId: string): Promise<Order | null> {
    try {
      const database = db();
      const orderRef = doc(database, 'orders', orderId);
      const orderSnapshot = await getDoc(orderRef);
      
      if (!orderSnapshot.exists()) {
        logger.debug('訂單不存在', { orderId });
        return null;
      }
      
      const orderData = orderSnapshot.data() as Omit<Order, 'id'>;
      return { 
        id: orderId, 
        ...orderData,
        // 確保 timestamp 是 Date 類型
        timestamp: orderData.timestamp instanceof Timestamp 
          ? (orderData.timestamp as any).toDate() 
          : orderData.timestamp
      };
    } catch (error) {
      logger.error('獲取訂單失敗', error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * 獲取用戶的訂單列表
   */
  async getUserOrders(userId: string): Promise<Order[]> {
    try {
      // 這裡需要使用 Firebase query，但為了簡化暫時返回空陣列
      // 實際實現需要 import { query, where, getDocs } from 'firebase/firestore'
      logger.info('獲取用戶訂單列表', { userId });
      
      // TODO: 實現實際的查詢邏輯
      return [];
    } catch (error) {
      logger.error('獲取用戶訂單列表失敗', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  /**
   * 更新訂單狀態
   */
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    try {
      const database = db();
      const orderRef = doc(database, 'orders', orderId);
      await updateDoc(orderRef, { status });
      
      logger.info('訂單狀態更新成功', { orderId, status });
    } catch (error) {
      logger.error('更新訂單狀態失敗', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
}
