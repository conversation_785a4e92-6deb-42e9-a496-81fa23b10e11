#!/bin/bash

# Docker 構建和部署腳本
# 適用於 Linux/macOS

set -e

echo "=== 自然語言點餐系統 Docker 部署腳本 ==="

# 檢查 Docker 是否安裝
if ! command -v docker &> /dev/null; then
    echo "錯誤: 未找到 Docker，請先安裝 Docker"
    exit 1
fi

# 檢查 Docker Compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "錯誤: 未找到 Docker Compose，請先安裝 Docker Compose"
    exit 1
fi

# 檢查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "警告: 未找到 .env 文件，正在複製 .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "已創建 .env 文件，請編輯此文件並填入您的 Firebase 配置"
        echo "編輯完成後請重新運行此腳本"
        exit 0
    else
        echo "錯誤: 未找到 .env.example 文件"
        exit 1
    fi
fi

# 創建必要的目錄
echo "創建數據目錄..."
if [ ! -d "uploads" ]; then
    mkdir -p uploads
    echo "已創建 uploads 目錄"
fi

if [ ! -d "appPrompt" ]; then
    mkdir -p appPrompt
    echo "已創建 appPrompt 目錄"
fi

# 設定目錄權限（如果需要）
if [ "$(id -u)" = "0" ]; then
    chown -R 1001:1001 uploads appPrompt 2>/dev/null || true
fi
chmod -R 755 uploads appPrompt 2>/dev/null || true

# 顯示選項菜單
echo ""
echo "請選擇操作:"
echo "1. 構建並啟動服務 (docker-compose up -d --build)"
echo "2. 啟動服務 (docker-compose up -d)"
echo "3. 停止服務 (docker-compose down)"
echo "4. 查看服務狀態 (docker-compose ps)"
echo "5. 查看日誌 (docker-compose logs -f)"
echo "6. 重啟服務 (docker-compose restart)"
echo "7. 完全重建 (停止→清理→無快取重建→啟動)"
echo "8. 清理所有容器和映像 (docker-compose down --rmi all)"
echo "9. 退出"

read -p "請輸入選項 (1-9): " choice

case $choice in
    1)
        echo "正在構建並啟動服務..."
        docker-compose up -d --build
        if [ $? -eq 0 ]; then
            echo "服務已成功啟動!"
            echo "訪問地址: http://localhost:3003"
        else
            echo "服務啟動失敗，請檢查日誌"
            exit 1
        fi
        ;;
    2)
        echo "正在啟動服務..."
        docker-compose up -d
        if [ $? -eq 0 ]; then
            echo "服務已成功啟動!"
            echo "訪問地址: http://localhost:3003"
        else
            echo "服務啟動失敗，請檢查日誌"
            exit 1
        fi
        ;;
    3)
        echo "正在停止服務..."
        docker-compose down
        echo "服務已停止"
        ;;
    4)
        echo "服務狀態:"
        docker-compose ps
        ;;
    5)
        echo "顯示日誌 (按 Ctrl+C 退出):"
        docker-compose logs -f
        ;;
    6)
        echo "正在重啟服務..."
        docker-compose restart
        echo "服務已重啟"
        ;;
    7)
        echo "正在執行完全重建..."

        # 停止現有容器
        echo "1. 停止現有容器..."
        docker-compose down

        # 清理舊映像
        echo "2. 清理舊映像..."
        old_images=$(docker images "*natural-order*" -q)
        if [ ! -z "$old_images" ]; then
            echo "發現舊映像，正在清理..."
            docker rmi $old_images -f
        else
            echo "沒有發現舊映像"
        fi

        # 重新構建映像（無快取）
        echo "3. 重新構建映像（無快取）..."
        docker-compose build --no-cache

        if [ $? -eq 0 ]; then
            # 啟動新服務
            echo "4. 啟動新服務..."
            docker-compose up -d

            if [ $? -eq 0 ]; then
                echo "5. 等待服務啟動..."
                sleep 10

                echo "6. 服務狀態："
                docker-compose ps

                echo "完全重建成功!"
                echo "訪問地址: http://localhost:3003"
            else
                echo "服務啟動失敗，請檢查日誌"
                exit 1
            fi
        else
            echo "映像構建失敗，請檢查錯誤信息"
            exit 1
        fi
        ;;
    8)
        read -p "確定要清理所有容器和映像嗎? (y/N): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            echo "正在清理..."
            docker-compose down --rmi all
            echo "清理完成"
        else
            echo "已取消清理操作"
        fi
        ;;
    9)
        echo "退出腳本"
        exit 0
        ;;
    *)
        echo "無效選項，請重新運行腳本"
        exit 1
        ;;
esac

echo ""
echo "腳本執行完成"