[{"pkg": "@capacitor-community/speech-recognition", "classpath": "com.getcapacitor.community.speechrecognition.SpeechRecognition"}, {"pkg": "@capacitor-community/text-to-speech", "classpath": "com.getcapacitor.community.tts.TextToSpeechPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}]