/**
 * ServiceManager.ts
 * 統一的服務管理器，根據GMS可用性自動選擇合適的服務實現
 * 提供Firebase、訂單服務、語音服務等的統一管理
 */
import { createLogger } from '../utils/Logger.js';
import { GmsDetectionService, GmsDetectionResult } from './GmsDetectionService.js';
import { IOrderService } from '../types/interfaces/IOrderService.js';
import { MenuData } from '../types/menu.js';

const logger = createLogger('ServiceManager');

/**
 * 服務類型枚舉
 */
export enum ServiceType {
  FIREBASE = 'firebase',
  ORDER = 'order',
  SPEECH_RECOGNITION = 'speechRecognition',
  TEXT_TO_SPEECH = 'textToSpeech'
}

/**
 * 服務配置接口
 */
export interface ServiceConfig {
  /** 是否強制使用Mock服務 */
  forceMock?: boolean;
  /** 是否優先使用V2版本 */
  preferV2?: boolean;
  /** Firebase配置 */
  firebaseConfig?: any;
}

/**
 * Firebase服務接口
 */
export interface IFirebaseService {
  saveMenu(menuData: MenuData): Promise<{success: boolean, id?: string, error?: string}>;
  getMenu(restaurantId: string): Promise<MenuData | null>;
  getAllMenus(): Promise<MenuData[]>;
  deleteMenu(restaurantId: string): Promise<{success: boolean, error?: string}>;
  updateMenuItem(restaurantId: string, menuItem: any, categoryId: string): Promise<{success: boolean, error?: string}>;
}

/**
 * 統一服務管理器
 */
export class ServiceManager {
  private gmsDetectionResult: GmsDetectionResult | null = null;
  private services: Map<ServiceType, any> = new Map();
  private config: ServiceConfig = {};
  
  constructor(config: ServiceConfig = {}) {
    this.config = config;
    logger.info('ServiceManager 已初始化', { config: this.config });
  }
  
  /**
   * 初始化服務管理器
   */
  async initialize(): Promise<void> {
    
    logger.info('開始初始化服務管理器', { config: this.config });
    
    // 檢測GMS可用性
    if (!this.config.forceMock) {
      const gmsDetectionService = new GmsDetectionService();
      this.gmsDetectionResult = await gmsDetectionService.detectGmsAvailability();
      logger.info('GMS檢測結果', this.gmsDetectionResult);
    } else {
      logger.info('強制使用Mock服務');
      this.gmsDetectionResult = {
        hasGoogleServices: false,
        firebaseAvailable: false,
        webSpeechAvailable: false,
        detectedAt: new Date(),
        details: {
          userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js',
          platform: typeof navigator !== 'undefined' ? navigator.platform : 'server'
        }
      };
    }
    
    // 預初始化關鍵服務
    await this.initializeFirebaseService();
    await this.initializeOrderService();
    
    logger.info('服務管理器初始化完成');
  }
  
  /**
   * 獲取Firebase服務
   */
  async getFirebaseService(): Promise<IFirebaseService> {
    if (this.services.has(ServiceType.FIREBASE)) {
      return this.services.get(ServiceType.FIREBASE);
    }
    
    return await this.initializeFirebaseService();
  }
  
  /**
   * 獲取訂單服務
   */
  async getOrderService(): Promise<IOrderService> {
    if (this.services.has(ServiceType.ORDER)) {
      return this.services.get(ServiceType.ORDER);
    }
    
    return await this.initializeOrderService();
  }
  
  /**
   * 獲取語音識別服務
   */
  async getSpeechRecognitionService(): Promise<any> {
    if (this.services.has(ServiceType.SPEECH_RECOGNITION)) {
      return this.services.get(ServiceType.SPEECH_RECOGNITION);
    }
    
    return await this.initializeSpeechRecognitionService();
  }
  
  /**
   * 獲取文字轉語音服務
   */
  async getTextToSpeechService(): Promise<any> {
    if (this.services.has(ServiceType.TEXT_TO_SPEECH)) {
      return this.services.get(ServiceType.TEXT_TO_SPEECH);
    }
    
    return await this.initializeTextToSpeechService();
  }
  
  /**
   * 初始化Firebase服務
   */
  private async initializeFirebaseService(): Promise<IFirebaseService> {
    try {
      if (this.config.forceMock || 
          !this.gmsDetectionResult?.hasGoogleServices || 
          !this.gmsDetectionResult?.firebaseAvailable) {
        
        logger.info('使用MockFirebaseService');
        const { default: MockFirebaseService } = await import('./MockFirebaseService.js');
        this.services.set(ServiceType.FIREBASE, MockFirebaseService);
        return MockFirebaseService;
      }
      
      // 嘗試使用真實的Firebase服務
      if (!this.config.firebaseConfig) {
        throw new Error('Firebase配置未提供');
      }
      
      logger.info('使用真實的FirebaseService');
      const { FirebaseService } = await import('./FirebaseService.js');
      const firebaseService = new FirebaseService(this.config.firebaseConfig);
      this.services.set(ServiceType.FIREBASE, firebaseService);
      return firebaseService;
      
    } catch (error) {
      logger.warn('Firebase服務初始化失敗，降級到Mock服務', error);
      const { default: MockFirebaseService } = await import('./MockFirebaseService.js');
      this.services.set(ServiceType.FIREBASE, MockFirebaseService);
      return MockFirebaseService;
    }
  }
  
  /**
   * 初始化訂單服務
   */
  private async initializeOrderService(): Promise<IOrderService> {
    try {
      if (this.config.forceMock || 
          !this.gmsDetectionResult?.hasGoogleServices || 
          !this.gmsDetectionResult?.firebaseAvailable) {
        
        logger.info('使用MockOrderService');
        const { default: MockOrderService } = await import('./MockOrderService.js');
        this.services.set(ServiceType.ORDER, MockOrderService);
        return MockOrderService;
      }
      
      // 嘗試使用Firebase訂單服務
      if (this.config.preferV2) {
        logger.info('使用OrderServiceV2');
        const { OrderServiceV2 } = await import('./OrderServiceV2.js');
        const orderService = new OrderServiceV2();
        this.services.set(ServiceType.ORDER, orderService);
        return orderService;
      } else {
        logger.info('使用OrderService');
        const { default: OrderService } = await import('./OrderService.js');
        this.services.set(ServiceType.ORDER, OrderService);
        return OrderService;
      }
      
    } catch (error) {
      logger.warn('訂單服務初始化失敗，降級到Mock服務', error);
      const { default: MockOrderService } = await import('./MockOrderService.js');
      this.services.set(ServiceType.ORDER, MockOrderService);
      return MockOrderService;
    }
  }
  
  /**
   * 初始化語音識別服務
   */
  private async initializeSpeechRecognitionService(): Promise<any> {
    try {
      logger.info('初始化語音識別服務');
      
      // 使用語音服務工廠自動選擇合適的服務
      const { getSpeechServiceFactory } = await import('./SpeechServiceFactory.js');
      
      // 配置語音服務
      const speechConfig = {
        preferredService: this.gmsDetectionResult?.webSpeechAvailable ? 'native' as const : 'fallback' as const,
        fallbackEnabled: true,
        autoDetect: true,
        forceService: this.config.forceMock ? 'fallback' as const : undefined
      };
      
      const factory = getSpeechServiceFactory(speechConfig);
      const speechService = factory.getCurrentService();
      
      if (speechService) {
        const serviceType = factory.getCurrentServiceType();
        logger.info(`語音識別服務初始化成功，使用: ${serviceType}`);
        
        this.services.set(ServiceType.SPEECH_RECOGNITION, speechService);
        return speechService;
      } else {
        throw new Error('無法創建語音識別服務');
      }
      
    } catch (error) {
      logger.warn('語音識別服務初始化失敗，使用最基本的備用服務', error);
      
      // 創建最基本的備用服務
      const { FallbackSpeechService } = await import('./FallbackSpeechService.js');
      const fallbackService = new FallbackSpeechService();
      
      this.services.set(ServiceType.SPEECH_RECOGNITION, fallbackService);
      return fallbackService;
    }
  }
  
  /**
   * 初始化文字轉語音服務
   */
  private async initializeTextToSpeechService(): Promise<any> {
    try {
      // TTS通常不依賴GMS，使用系統原生TTS
      logger.info('使用NativeSpeechService');
      const { nativeSpeechService } = await import('./NativeSpeechService.js');
      this.services.set(ServiceType.TEXT_TO_SPEECH, nativeSpeechService);
      return nativeSpeechService;
      
    } catch (error) {
      logger.warn('文字轉語音服務初始化失敗', error);
      const fallbackService = {
        isAvailable: false,
        speak: () => Promise.reject(new Error('文字轉語音不可用')),
        stop: () => Promise.resolve()
      };
      this.services.set(ServiceType.TEXT_TO_SPEECH, fallbackService);
      return fallbackService;
    }
  }
  
  /**
   * 獲取GMS檢測結果
   */
  getGmsDetectionResult(): GmsDetectionResult | null {
    return this.gmsDetectionResult;
  }
  
  /**
   * 檢查特定服務是否可用
   */
  isServiceAvailable(serviceType: ServiceType): boolean {
    const service = this.services.get(serviceType);
    if (!service) {
      return false;
    }
    
    // 檢查服務是否有isAvailable屬性
    if (typeof service.isAvailable === 'boolean') {
      return service.isAvailable;
    }
    
    // 如果沒有isAvailable屬性，假設服務可用
    return true;
  }
  
  /**
   * 獲取服務狀態摘要
   */
  getServiceStatus(): Record<string, any> {
    return {
      gmsDetection: this.gmsDetectionResult,
      services: {
        firebase: this.isServiceAvailable(ServiceType.FIREBASE),
        order: this.isServiceAvailable(ServiceType.ORDER),
        speechRecognition: this.isServiceAvailable(ServiceType.SPEECH_RECOGNITION),
        textToSpeech: this.isServiceAvailable(ServiceType.TEXT_TO_SPEECH)
      },
      config: this.config
    };
  }
  
  /**
   * 重置服務管理器（主要用於測試）
   */
  reset(): void {
    this.services.clear();
    this.gmsDetectionResult = null;
    this.config = {};
    logger.info('ServiceManager已重置');
  }
  

}

// ServiceManager需要手動實例化，不再使用單例模式
// 使用方式：const serviceManager = new ServiceManager(config);