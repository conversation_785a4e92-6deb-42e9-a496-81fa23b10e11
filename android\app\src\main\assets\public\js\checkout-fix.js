/**
 * 結帳功能修復腳本
 * 直接從DOM表格元素讀取訂單內容，解決結帳模態窗口顯示"未知餐點"的問題
 * 
 * 增強功能:
 * 1. 從 USI AIOS 回應中提取菜單項目和總金額
 * 2. 支援多種格式的 Gemini 回應
 * 3. 使用增強型提取工具處理複雜格式
 */

// 覆寫原始的結帳功能函數
window.proceedToCheckout = function() {
    console.log('=== 執行修復版結帳功能 V2.1 ===');
    console.log('腳本執行位置:', document.currentScript ? document.currentScript.src : '未知');
    console.log('Gemini 提取工具可用?', typeof window.enhancedGeminiExtraction === 'function' ? '是' : '否');
    
    // 檢查訂單確認對話框是否存在
    const orderConfirmationModal = document.getElementById('order-confirmation-modal');
    console.log('訂單確認對話框存在?', orderConfirmationModal ? '是' : '否');
    
    // 隱藏 AI 分析結果
    const aiAnalysisSection = document.getElementById('ai-analysis-section');
    if (aiAnalysisSection) {
        aiAnalysisSection.style.display = 'none';
        console.log('已隱藏 AI 分析結果區域');
    }
    
    // 隱藏所有可能的分析結果區域
    const analysisInfos = document.querySelectorAll('.analysis-info');
    analysisInfos.forEach(element => {
        element.style.display = 'none';
    });
    console.log(`已隱藏 ${analysisInfos.length} 個分析結果區域`);
    
    
    // 準備訂單摘要內容
    const modalOrderSummary = document.getElementById('modal-order-summary');
    const modalTotalAmount = document.getElementById('modal-total-amount');
    const orderItems = document.querySelectorAll('.preview-table tbody tr');
    
    // 檢查是否有訂單項目
    if (!orderItems || orderItems.length === 0) {
        console.log('購物車是空的');
        showToastMessage('購物車是空的，請先選擇餐點', 'warning');
        return;
    }
      console.log(`找到 ${orderItems.length} 個訂單項目`);    
    
    // 從 USI AIOS 回應中提取菜單項目部分
    let summaryHtml = '';
    let finalTotal = 0;
    let orderData = null; // 將 orderData 定義移到 try 塊外部
      try {
        // 嘗試從多個來源獲取 USI AIOS 的回應數據
        console.log('正在嘗試存取 Gemini 回應數據');
        
        // 調試用：在控制台輸出當前可用的訂單數據來源
        console.log('全局訂單數據檢查:');
        console.log('- window.currentGeminiOrderData 存在?', window.currentGeminiOrderData ? '是' : '否');
        console.log('- currentGeminiOrderData 存在?', typeof currentGeminiOrderData !== 'undefined' ? '是' : '否');
        
        // 1. 優先從全局變數獲取
        orderData = null;
        if (window.currentGeminiOrderData && window.currentGeminiOrderData.response) {
            console.log('從 window.currentGeminiOrderData 獲取數據:', window.currentGeminiOrderData.response);
            orderData = window.currentGeminiOrderData;
        } else if (typeof currentGeminiOrderData !== 'undefined' && currentGeminiOrderData) {
            console.log('從 currentGeminiOrderData 獲取數據');
            orderData = currentGeminiOrderData;
        } else {
            // 2. 嘗試從頁面元素中提取
            console.log('嘗試從頁面元素中提取 Gemini 回應數據');
            const responseContent = document.querySelector('.response-content');
            if (responseContent) {
                const text = responseContent.textContent || responseContent.innerText;
                if (text) {
                    console.log('從頁面元素提取到文本內容:', text);
                    orderData = { response: text };
                }
            }
            
            // 3. 嘗試從 gemini-response 元素獲取
            if (!orderData) {
                const geminiResponse = document.querySelector('.gemini-response');
                if (geminiResponse) {
                    const text = geminiResponse.textContent || geminiResponse.innerText;
                    if (text) {
                        console.log('從 gemini-response 元素提取到文本內容');
                        orderData = { response: text };
                    }
                }
            }
        }        console.log('最終使用的 orderData =', orderData);
        
        if (orderData && orderData.response) {
            console.log('從 Gemini 回應中提取菜單項目');
            const response = orderData.response;
            
            // 嘗試使用增強型提取工具（如果可用）
            if (typeof window.enhancedGeminiExtraction === 'function') {
                console.log('使用增強型提取工具');
                const extractionResult = window.enhancedGeminiExtraction(response);
                
                if (extractionResult && extractionResult.success) {
                    console.log('增強型提取成功');
                    
                    // 設置菜單項目
                    if (extractionResult.menuItems.length > 0) {                        // 轉換為 HTML 列表項並包裝在 ul 元素中 - 增強顯示樣式
                        summaryHtml = `<ul style="list-style-type: disc; padding-left: 20px; margin: 0; display: block;">
                            ${extractionResult.menuItems.map(item => {
                                return `<li style="display: list-item; margin: 5px 0;">${item.fullText}</li>`;
                            }).join('')}
                        </ul>`;
                        
                        // 設置總金額
                        if (extractionResult.totalFound) {
                            finalTotal = extractionResult.totalAmount;
                            console.log('從提取結果獲取總金額:', finalTotal);
                        }
                    }
                } else {
                    console.log('增強型提取失敗，回退到基本提取邏輯');                    // 提取菜單項目：使用更寬鬆的標準
                    const lines = response.split('\n');
                    const menuItemLines = [];
                    
                    for (const line of lines) {
                        const trimmed = line.trim();                
                        // 只要包含 NT$ 或 ¥ 且不是總計/總共行的文字都視為菜單項目
                        if ((trimmed.includes('NT$') || trimmed.includes('¥')) && 
                            !trimmed.match(/總計|總共|總金額|總額|共計|合計|total/i)) {
                            menuItemLines.push(trimmed);
                            console.log('找到菜單項目行:', trimmed);
                        }
                    }
                      if (menuItemLines.length > 0) {                        // 轉換為 HTML 列表項並包裝在 ul 元素中 - 增強顯示樣式
                        summaryHtml = `<ul style="list-style-type: disc; padding-left: 20px; margin: 0; display: block;">
                            ${menuItemLines.map(line => {
                                return `<li style="display: list-item; margin: 5px 0;">${line.trim()}</li>`;
                            }).join('')}
                        </ul>`;
                        
                        // 提取總金額 - 嘗試多種可能的格式（支援 NT$ 和 ¥）
                        const totalPatterns = [
                            /總共是\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總計\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總金額\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總額\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /共\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總共\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /共計\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /共計：\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總計：\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總金額：\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /總額：\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /total\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /total:\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /合計\s*(?:NT\$|¥)\s*([\d,]+)/i,
                            /合計：\s*(?:NT\$|¥)\s*([\d,]+)/i
                        ];                        let totalFound = false;
                        for (const pattern of totalPatterns) {
                            const match = response.match(pattern);
                            if (match && match[1]) {
                                // 移除數字中的逗號再解析
                                finalTotal = parseFloat(match[1].replace(/,/g, ''));
                                console.log('從 Gemini 回應中提取的總金額:', finalTotal);
                                totalFound = true;
                                break;
                            }
                        }
                        
                        if (!totalFound) {
                            // 如果無法從回應中提取總金額，則從訂單項目中計算
                            console.log('無法從 Gemini 回應中提取總金額，改用訂單項目計算');
                            orderItems.forEach(item => {
                                const totalCell = item.querySelector('.item-total');
                                if (totalCell) {
                                    const totalText = totalCell.textContent.replace('NT$', '').trim();
                                    const itemTotal = parseFloat(totalText) || 0;
                                    finalTotal += itemTotal;
                                }
                            });
                        }
                        
                        if (summaryHtml === '') {
                            console.log('從 Gemini 回應中無法提取到有效的菜單項目');
                        } else {
                            console.log('成功從 Gemini 回應中提取菜單項目');
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.error('處理 Gemini 回應時發生錯誤:', error);
    }
      // 如果無法從 Gemini 回應中提取菜單項目，則使用原來的方法
    if (!summaryHtml) {
        console.log('無法從 Gemini 回應中提取菜單項目，使用訂單表格數據');
          // 初始化 HTML 以包含 ul 標籤 - 增強顯示樣式
        summaryHtml = '<ul style="list-style-type: disc; padding-left: 20px; margin: 0; display: block;">';
        
        // 直接從DOM元素讀取訂單項目顯示內容
        orderItems.forEach((item, index) => {
            // 獲取名稱單元格
            const nameCell = item.querySelector('.item-name');
            // 獲取數量單元格
            const qtyCell = item.querySelector('.item-quantity');
            // 獲取總價單元格
            const totalCell = item.querySelector('.item-total');
            
            if (nameCell && qtyCell && totalCell) {
                // 直接從DOM元素中獲取顯示的文本
                const name = nameCell.textContent.trim();
                const quantity = qtyCell.textContent.trim();
                
                // 從總價文本中提取數字
                const totalText = totalCell.textContent.replace('NT$', '').trim();                const itemTotal = parseFloat(totalText) || 0;
                  if (name && name !== '未知餐點' && !isNaN(itemTotal) && itemTotal > 0) {
                    summaryHtml += `<li style="display: list-item; margin: 5px 0;">${name} x ${quantity} - NT$${itemTotal}</li>`;
                    finalTotal += itemTotal;
                    console.log(`項目 ${index+1}: ${name} x ${quantity} = ${itemTotal}`);
                } else {
                    console.log(`跳過無效項目 ${index+1}: ${name}, 價格=${itemTotal}`);
                }
            }
        });
        
        // 關閉 ul 標籤
        summaryHtml += '</ul>';
    }
    
    // 檢查是否有有效的訂單項目
    if (summaryHtml === '') {
        console.log('沒有有效的訂單項目');
        showToastMessage('請先選擇有效的餐點再結帳', 'warning');
        return;
    }    // 在顯示確認對話框之前，將訂單數據保存到全局變量
    try {
        console.log('開始將訂單數據保存到全局變量...');

        // 確保 currentOrder 全局變量存在
        if (typeof window.currentOrder === 'undefined') {
            window.currentOrder = {
                items: [],
                totalAmount: 0,
                id: null
            };
        }

        // 清空現有訂單
        window.currentOrder.items = [];
        window.currentOrder.totalAmount = 0;

        // 從訂單項目中提取數據並保存到 currentOrder
        if (orderItems && orderItems.length > 0) {
            orderItems.forEach((item, index) => {
                const nameCell = item.querySelector('.item-name');
                const qtyCell = item.querySelector('.item-quantity');
                const totalCell = item.querySelector('.item-total');

                if (nameCell && qtyCell && totalCell) {
                    const name = nameCell.textContent.trim();
                    const quantity = parseInt(qtyCell.textContent.trim()) || 1;
                    const totalText = totalCell.textContent.replace(/NT\$|¥/g, '').trim();
                    const itemTotal = parseFloat(totalText) || 0;
                    const price = itemTotal / quantity; // 計算單價

                    if (name && name !== '未知餐點' && itemTotal > 0) {
                        const orderItem = {
                            id: `item_${index}`,
                            name: name,
                            price: price,
                            quantity: quantity,
                            total: itemTotal
                        };

                        window.currentOrder.items.push(orderItem);
                        window.currentOrder.totalAmount += itemTotal;

                        console.log(`已添加訂單項目 ${index + 1}:`, orderItem);
                    }
                }
            });
        }

        console.log('訂單數據保存完成:', {
            itemCount: window.currentOrder.items.length,
            totalAmount: window.currentOrder.totalAmount,
            items: window.currentOrder.items
        });

    } catch (error) {
        console.error('保存訂單數據時發生錯誤:', error);
    }

    // 更新模態窗口內容
    if (modalOrderSummary) {
        console.log('=== 訂單摘要 HTML 內容 ===');
        console.log(summaryHtml);
        console.log('===========================');

        // 確保內容不為空，如果為空，添加一個明確的提示
        if (!summaryHtml || summaryHtml.trim() === '') {
            console.warn('訂單摘要內容為空，添加提示訊息');
            summaryHtml = `<div style="color: #e74c3c; text-align: center; padding: 10px; border: 1px solid #e74c3c; border-radius: 4px; margin-bottom: 10px;">${getTranslation('order_summary_failed') || '訂單摘要生成失敗，請檢查控制台日誌'}</div>`;
        }

        // 添加一個包裝器以確保內容可見
        const wrappedHtml = `<div style="min-height: 50px; border: 1px solid #ddd; padding: 8px; border-radius: 4px;">${summaryHtml}</div>`;
        modalOrderSummary.innerHTML = wrappedHtml;
        console.log('已更新訂單摘要');

        // 確認更新後的內容
        console.log('=== 更新後的 DOM 內容 ===');
        console.log(modalOrderSummary.innerHTML);
        console.log('===========================');
    } else {
        console.error('找不到訂單摘要容器元素');
    }
    
    if (modalTotalAmount) {
        // 根據當前語言設定決定貨幣符號
        function getCurrencySymbolByLanguage() {
            const currentLanguage = getCurrentLanguage();
            switch (currentLanguage) {
                case 'ja-JP':
                    return 'NT$';  // 日文介面也使用 NT$ 統一貨幣顯示
                case 'en-US':
                    return 'NT$';  // 英文介面也使用 NT$ 避免與美元混淆
                case 'zh-TW':
                default:
                    return 'NT$';
            }
        }

        let currencySymbol = getCurrencySymbolByLanguage();
        
        modalTotalAmount.textContent = `${currencySymbol}${finalTotal.toFixed(0)}`;
        console.log(`已更新總金額: ${currencySymbol}${finalTotal}`);
    } else {
        console.error('找不到總金額顯示元素');
    }    // 顯示訂單完成提示框
    const modal = document.getElementById('order-confirmation-modal');
    if (modal) {
        modal.style.display = 'flex'; // 使用 flex 垂直置中
        console.log('訂單完成提示窗口已顯示');
        
        // 播放結帳成功音效或提示
        if (window.speakText) {
            window.speakText(getTranslation('order_complete_message'));
        }
    } else {
        console.error('找不到訂單完成提示窗口元素');
    }
};

// 當頁面載入時，執行初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('結帳功能修復腳本已載入');
});

// 顯示通知訊息的輔助函數，如果原網頁沒有此函數則提供實現
if (typeof showToastMessage !== 'function') {
    window.showToastMessage = function(message, type) {
        console.log(`[${type}] ${message}`);
        alert(message);
    };
}

// 手動同步數據函數 - 作為備用方案
function manualSyncData() {
    console.log('===== 開始手動同步數據 =====');
    const orderItems = document.querySelectorAll('.preview-table tbody tr');
    
    if (!orderItems || orderItems.length === 0) {
        console.log('找不到訂單項目，無法進行手動同步');
        return;
    }
    
    console.log(`找到 ${orderItems.length} 個訂單項目，開始手動同步`);
    
    orderItems.forEach((item, index) => {
        // 從DOM元素獲取數據
        const nameCell = item.querySelector('.item-name');
        const priceCell = item.querySelector('.item-price');
        const qtyElement = item.querySelector('.item-quantity');
        const totalCell = item.querySelector('.item-total');
        
        // 獲取並設置名稱
        if (nameCell) {
            const name = nameCell.textContent.trim();
            if (name && name !== '未知餐點') {
                item.setAttribute('data-name', name);
                console.log(`[手動同步] 項目 ${index + 1} 名稱: ${name}`);
            }
        }
        
        // 獲取並設置價格
        if (priceCell) {
            const priceText = priceCell.textContent.replace('NT$', '').trim();
            const price = parseFloat(priceText);
            if (!isNaN(price) && price > 0) {
                item.setAttribute('data-price', price.toString());
                console.log(`[手動同步] 項目 ${index + 1} 價格: ${price}`);
            }
        }
        
        // 獲取並設置數量
        if (qtyElement) {
            const qty = parseInt(qtyElement.textContent.trim());
            if (!isNaN(qty) && qty > 0) {
                item.setAttribute('data-quantity', qty.toString());
                console.log(`[手動同步] 項目 ${index + 1} 數量: ${qty}`);
            }
        }
        
        // 獲取並設置總價
        if (totalCell) {
            const totalText = totalCell.textContent.replace('NT$', '').trim();
            const total = parseFloat(totalText);
            if (!isNaN(total) && total > 0) {
                item.setAttribute('data-total', total.toString());
                console.log(`[手動同步] 項目 ${index + 1} 總價: ${total}`);
            } else {
                // 如果總價無效，從價格和數量計算
                const price = parseFloat(item.getAttribute('data-price') || '0');
                const qty = parseInt(item.getAttribute('data-quantity') || '1');
                if (!isNaN(price) && price > 0 && !isNaN(qty) && qty > 0) {
                    const calculatedTotal = price * qty;
                    item.setAttribute('data-total', calculatedTotal.toString());
                    console.log(`[手動同步] 項目 ${index + 1} 計算總價: ${calculatedTotal}`);
                }
            }
        }
    });
    
    console.log('===== 手動同步數據完成 =====');
}

// 測試數據屬性同步函數
function testDataAttributes() {
    const items = document.querySelectorAll('.preview-table tbody tr');
    console.log('===== 測試數據屬性同步 =====');
    console.log(`找到 ${items.length} 個訂單項目`);
    
    items.forEach((item, index) => {
        const name = item.getAttribute('data-name');
        const price = item.getAttribute('data-price');
        const quantity = item.getAttribute('data-quantity');
        const total = item.getAttribute('data-total');
        
        console.log(`項目 ${index + 1}:`);
        console.log(`- 名稱: ${name || '未設置'}`);
        console.log(`- 價格: ${price || '未設置'}`);
        console.log(`- 數量: ${quantity || '未設置'}`);
        console.log(`- 總價: ${total || '未設置'}`);
    });
    
    console.log('==========================');
}
