// 這是一個修復路由問題的示例解決方案

// 1. 使用 npm 更新 Express 和 TypeScript 類型定義
// npm install --save express@latest @types/express@latest

// 2. 修改 nlp.ts 文件
import { Router, Request, Response } from 'express';
import { MenuProcessor } from '../services/MenuProcessor.js';
import { NLPService } from '../services/NLPService.js';
import { GeminiServiceV2 } from '../services/GeminiServiceV2.js';
import { NaturalLanguageProcessor } from '../services/NaturalLanguageProcessor.js';
import { MenuItem } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import path from 'node:path';
import fs from 'node:fs';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = Router();
const nlpService = new NLPService();
const menuProcessor = new MenuProcessor();
const logger = createLogger('NLPRoute');

// 服務選擇緩存
let cachedNLPService: any = null;
let lastNLPHealthCheck = 0;
const NLP_HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5分鐘

/**
 * 根據語言載入對應的 appPrompt
 */
async function loadLanguageSpecificAppPrompt(language?: string): Promise<string | null> {
  try {
    const workspaceDir = process.cwd();
    const appPromptDir = path.join(workspaceDir, 'appPrompt');
    
    if (!fs.existsSync(appPromptDir)) {
      logger.debug('appPrompt 目錄不存在');
      return null;
    }
    
    // 根據語言選擇對應的提示詞文件
    let targetFile: string | null = null;
    
    if (language) {
      switch (language.toLowerCase()) {
        case 'en':
        case 'en-us':
        case 'english':
          targetFile = 'appPrompt_English.json';
          break;
        case 'ja':
        case 'ja-jp':
        case 'japanese':
          targetFile = 'appPrompt_Japanese.json';
          break;
        case 'zh':
        case 'zh-tw':
        case 'zh-cn':
        case 'chinese':
        default:
          // 中文或預設情況，使用時間戳版本
          break;
      }
    }
    
    // 如果指定了特定語言文件，先嘗試載入
    if (targetFile) {
      const targetPath = path.join(appPromptDir, targetFile);
      if (fs.existsSync(targetPath)) {
        logger.info('載入指定語言的 appPrompt 文件', { filename: targetFile, language });
        console.log(`[nlp.ts] 成功載入語言檔案: ${targetFile} (語言: ${language})`);
        const content = fs.readFileSync(targetPath, 'utf8');
        const appPromptData = JSON.parse(content);
        console.log(`[nlp.ts] 檔案內容載入完成，prompt 長度: ${appPromptData.prompt ? appPromptData.prompt.length : 0} 字元`);
        return JSON.stringify(appPromptData);
      } else {
        logger.warn('指定語言的 appPrompt 文件不存在，回退到預設文件', { filename: targetFile, language });
        console.log(`[nlp.ts] 警告: 找不到語言檔案 ${targetFile}，將使用預設檔案`);
      }
    }
    
    // 回退邏輯：載入時間戳版本的文件
    const dirFiles = fs.readdirSync(appPromptDir);
    const timestampFiles = dirFiles
      .filter(file => /^appPrompt_\d{12}\.json$/.test(file))
      .sort()
      .reverse(); // 最新的在前
    
    if (timestampFiles.length > 0) {
      const latestFile = timestampFiles[0];
      const filePath = path.join(appPromptDir, latestFile);
      logger.info('載入預設 appPrompt 文件', { filename: latestFile });
      const content = fs.readFileSync(filePath, 'utf8');
      const appPromptData = JSON.parse(content);
      return JSON.stringify(appPromptData);
    }
    
    logger.debug('未找到任何 appPrompt 文件');
    return null;
  } catch (error) {
    logger.error('載入 appPrompt 文件失敗', error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

// 智能 NLP 服務選擇：優先使用 GeminiServiceV2，降級到舊的 NLPService
async function getActiveNLPService() {
  const now = Date.now();
  
  // 如果有緩存且在健康檢查間隔內，直接返回
  if (cachedNLPService && (now - lastNLPHealthCheck) < NLP_HEALTH_CHECK_INTERVAL) {
    return cachedNLPService;
  }
  
  try {
    // 嘗試使用新的 GeminiServiceV2
    const geminiServiceV2 = new GeminiServiceV2();
    
    // 進行簡單的健康檢查
    logger.info('使用 GeminiServiceV2 (模組化架構)');
    cachedNLPService = {
      service: geminiServiceV2,
      type: 'v2',
      processor: new NaturalLanguageProcessor()
    };
    lastNLPHealthCheck = now;
    return cachedNLPService;
  } catch (error) {
    logger.warn('GeminiServiceV2 不可用，降級到舊的 NLPService', error instanceof Error ? error : new Error(String(error)));
    cachedNLPService = {
      service: nlpService,
      type: 'legacy'
    };
    lastNLPHealthCheck = now;
    return cachedNLPService;
  }
}

// 臨時存儲加載的菜單數據
export const loadedMenus: { [key: string]: MenuItem[] } = {};

// 不再自動載入默認菜單，改為由用戶上傳菜單時動態載入
logger.info("系統啟動，不預載菜單，等待用戶上傳");

// 添加菜單項目到 loadedMenus
export function updateLoadedMenu(restaurantId: string, menuData: any) {
  if (menuData && menuData.categories) {
    // 保持原始的菜單結構，不要扁平化
    loadedMenus[restaurantId] = menuData;
    
    // 計算總項目數量用於日誌
    let totalItems = 0;
    menuData.categories.forEach((category: any) => {
      if (category && Array.isArray(category.items)) {
        totalItems += category.items.length;
      }
    });
    
    logger.info(`已更新 ${restaurantId} 的菜單`, {
      restaurantId,
      itemCount: totalItems
    });
    logger.info('當前可用餐廳菜單', { 
      availableRestaurants: Object.keys(loadedMenus)
    });
    return true;
  }
  
  return false;
}

// 定義處理器函數
// 自然語言處理 API
const processHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { text, input, context, strictMode, language } = req.body;

    // 設定預設語言，如果沒有傳遞 language 參數則使用環境變數中的預設值
    const actualLanguage = language || process.env.DEFAULT_LANGUAGE || 'zh-TW';

    // 支援兩種輸入格式：text (直接文本) 和 input (舊版相容)
    const userInput = text || input;

    if (!userInput) {
      res.status(400).json({
        success: false,
        error: '請提供輸入文本'
      });
      return;
    }

    logger.info('接收到自然語言訂單處理請求', { 
      inputLength: userInput.length,
      language: language || 'auto',
      hasContext: !!context 
    });

    // 智能服務選擇：先嘗試 GeminiServiceV2，失敗則降級到舊 NLPService
    let result;
    try {
      const activeService = await getActiveNLPService();
      
      if (activeService.type === 'v2') {
        // 使用新的 GeminiServiceV2 處理
        logger.info('使用 GeminiServiceV2 處理自然語言訂單');
          try {
          // 構建菜單數據 - 優先使用 context 中的 restaurantId，否則使用第一個可用的菜單
          const restaurantId = context?.restaurantId || Object.keys(loadedMenus)[0];
          const menuData = restaurantId && loadedMenus[restaurantId] 
            ? loadedMenus[restaurantId]
            : null;
          
          // 動態載入語言特定的 appPrompt
          const languageSpecificAppPrompt = await loadLanguageSpecificAppPrompt(actualLanguage);
          const appPromptToUse = context?.appPrompt || languageSpecificAppPrompt;
          
          const nlpResultString = await activeService.service.processNaturalLanguageOrder(
            userInput,
            menuData,
            appPromptToUse,
            context?.sessionId,
            actualLanguage
          );
          
          // 解析 JSON 字串為物件
          const nlpResult = JSON.parse(nlpResultString);
          
          logger.debug('NLP 處理結果', {
            nlpResultString,
            nlpResult,
            items: nlpResult.items,
            itemsLength: nlpResult.items?.length || 0
          });
          
          result = {
            success: true,
            data: {
              aiResponse: nlpResult,
              matches: nlpResult.items || [], // 正確映射 items 到 matches
              unidentified: []
            },
            analysis: {
              aiProcessed: true,
              service: 'GeminiServiceV2',
              entities: []
            }
          };
          
          logger.debug('最終回傳結果', {
            matches: result.data.matches,
            matchesLength: result.data.matches.length
          });
        } catch (v2Error) {
          logger.warn('GeminiServiceV2 處理失敗，降級到舊服務', {
            error: v2Error instanceof Error ? v2Error.message : String(v2Error)
          });
          throw v2Error; // 觸發降級
        }
      } else {
        // 使用舊的 NLPService 作為後備
        logger.info('使用舊 NLPService 處理自然語言訂單');
        result = await activeService.service.processOrderWithAppPromptFirst(userInput, actualLanguage);
      }
    } catch (primaryError) {
      logger.warn('主要服務失敗，嘗試降級', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      
      try {
        result = await nlpService.processOrderWithAppPromptFirst(userInput, actualLanguage);
        logger.info('成功降級到舊 NLPService');
      } catch (fallbackError) {
        logger.error('降級服務也失敗', fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError)));
        throw fallbackError;
      }
    }

    res.json({
      success: result.success,
      data: result.data,
      processed: {
        entities: result.analysis?.entities || []
      },
      analysis: result.analysis,
      error: result.error
    });

  } catch (error: any) {
    logger.error('自然語言處理失敗', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: `處理文本時發生錯誤: ${error.message}`,
      data: { matches: [], unidentified: [req.body.text || req.body.input] },
      analysis: {
        aiProcessed: false,
        error: error.message
      }
    });
  }
};

// 處理訂單修改 - 已簡化，不再支援複雜的修改操作
const modifyHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { input, currentItems, context, strictMode, language } = req.body;

    // 設定預設語言，如果沒有傳遞 language 參數則使用環境變數中的預設值
    const actualLanguage = language || process.env.DEFAULT_LANGUAGE || 'zh-TW';

    if (!input || !currentItems || !Array.isArray(currentItems)) {
      res.status(400).json({
        success: false,
        error: '請提供輸入文本和當前餐點列表'
      });
      return;
    }

    logger.info('接收到訂單修改請求', { 
      inputLength: input.length,
      currentItemsCount: currentItems.length,
      language: actualLanguage
    });

    // 智能服務選擇並處理修改請求
    let result;
    try {
      const activeService = await getActiveNLPService();
        if (activeService.type === 'v2') {
        logger.info('使用 GeminiServiceV2 處理訂單修改');
        
        // 構建菜單數據 - 優先使用 context 中的 restaurantId，否則使用第一個可用的菜單
        const restaurantId = context?.restaurantId || Object.keys(loadedMenus)[0];
        const menuData = restaurantId && loadedMenus[restaurantId]
          ? loadedMenus[restaurantId]
          : null;
          
        const nlpResult = await activeService.service.processNaturalLanguageOrder(
          input,
          menuData,
          context?.appPrompt,
          context?.sessionId,
          actualLanguage
        );
        
        result = {
          success: true,
          result: {
            action: "new_order",
            message: "修改請求已通過 GeminiServiceV2 處理",
            aiResponse: nlpResult,
            originalItems: currentItems,
            modificationInput: input,
            service: 'GeminiServiceV2'
          }
        };
      } else {
        // 使用舊服務處理
        logger.info('使用舊 NLPService 處理訂單修改');
        const nlpResult = await activeService.service.processOrderWithAppPromptFirst(input, actualLanguage);
        
        result = {
          success: true,
          result: {
            action: "new_order", 
            message: "修改請求已作為新訂單處理",
            aiResponse: nlpResult.data?.aiResponse,
            originalItems: currentItems,
            modificationInput: input,
            service: 'NLPService'
          }
        };
      }
    } catch (primaryError) {
      logger.warn('主要服務修改處理失敗，嘗試降級', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      
      const fallbackResult = await nlpService.processOrderWithAppPromptFirst(input, actualLanguage);
      result = {
        success: true,
        result: {
          action: "new_order",
          message: "修改請求已通過降級服務處理", 
          aiResponse: fallbackResult.data?.aiResponse,
          originalItems: currentItems,
          modificationInput: input,
          service: 'NLPService (fallback)'
        }
      };
      logger.info('成功降級處理訂單修改');
    }
    
    res.json(result);
  } catch (error: any) {
    logger.error('處理訂單修改失敗', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: `處理修改指令時發生錯誤: ${error.message}`
    });
  }
};

// 獲取指定餐廳的菜單
const getMenuHandler = (req: Request, res: Response) => {
  const { restaurantId } = req.params;
  
  if (!loadedMenus[restaurantId]) {
    res.json({
      success: true,
      menu: [],
      message: "尚未上傳此餐廳的菜單，請先在菜單管理頁面上傳菜單。"
    });
    return;
  }
  
  res.json({
    success: true,
    menu: loadedMenus[restaurantId]
  });
};

// 註冊路由
router.post('/process', function(req, res) {
  processHandler(req, res);
});
router.post('/modify', function(req, res) {
  modifyHandler(req, res);
});
router.get('/menu/:restaurantId', function(req, res) {
  getMenuHandler(req, res);
});

// 添加手動觸發載入默認菜單的路由（可選功能）
router.post('/load-default-menus', async function(req, res) {
  try {
    const result = await loadDefaultMenus();
    res.json({
      success: true,
      message: '默認菜單載入操作完成',
      availableMenus: Object.keys(loadedMenus)
    });
  } catch (error: any) {
    logger.error('載入默認菜單失敗', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: `載入默認菜單失敗: ${error.message}`
    });
  }
});

// 載入默認菜單 - 此功能現已變為可選，不會自動調用
// 可以通過特定API端點觸發，如果將來需要的話
async function loadDefaultMenus() {
  try {
    logger.info('開始載入默認菜單...');
    // 載入麥當勞菜單
    const mcdonaldsMenu: MenuItem[] = [];
    
    // 檢查 document/Mcdonalds_menu 目錄是否存在
    const menuDirPath = path.join(__dirname, '../../document/Mcdonalds_menu');
    if (!fs.existsSync(menuDirPath)) {
      logger.warn('找不到默認菜單目錄', { path: menuDirPath });
      return;
    }    
    // 讀取漢堡菜單
    const burgersPath = path.join(menuDirPath, 'burgers.csv');
    if (fs.existsSync(burgersPath)) {
      const result = await menuProcessor.processMenuFile(
        burgersPath,
        'mcdonalds',
        { autoDetectCategory: true }
      );
      
      if (result.success && result.data) {
        result.data.categories.forEach(category => {
          mcdonaldsMenu.push(...category.items);
        });
      }
    } else {
      logger.warn('找不到漢堡菜單文件', { path: burgersPath });
    }
      
    // 其餘的菜單文件處理代碼保留但不主動執行
    
    // 如果有加載到菜單項目，則添加到已載入菜單中
    if (mcdonaldsMenu.length > 0) {
      loadedMenus['mcdonalds'] = mcdonaldsMenu;
      logger.info('麥當勞菜單載入完成', {
        restaurant: 'mcdonalds',
        itemCount: mcdonaldsMenu.length
      });
    }
    
    logger.info('默認菜單載入完成');
    return true;
  } catch (error: any) {
    logger.error('載入默認菜單時發生錯誤', error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

export default router;
