/* 訂單相關樣式 */

/* 訊息提示動畫 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(30px);
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.order-confirmation {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.order-confirmation h3 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.order-summary {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.en-summary {
  padding: 10px;
  background-color: #f1f1f1;
  border-left: 4px solid #1e87f0;
  font-style: italic;
}

.order-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.btn {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #fc8019;
  color: white;
}

.btn-primary:hover {
  background-color: #e67612;
}

.btn-secondary {
  background-color: #f1f1f1;
  color: #333;
}

.btn-secondary:hover {
  background-color: #ddd;
}

/* 訂單成功樣式 */
.success-dialog {
  background-color: rgba(0, 0, 0, 0.8);
}

.order-success {
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
}

.success-icon {
  font-size: 4rem;
  color: #4CAF50;
  margin-bottom: 10px;
  height: 80px;
  width: 80px;
  line-height: 80px;
  border-radius: 50%;
  background-color: #e8f5e9;
  margin: 0 auto 20px;
}

.order-success h2 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.order-details {
  text-align: left;
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.order-details ul {
  padding-left: 20px;
  margin-bottom: 15px;
}

/* 訂單通知樣式 */
.status-notification {
  position: fixed;
  bottom: -100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #2ecc71;
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: bottom 0.5s ease-in-out;
  z-index: 1001;
}

.notification-content {
  display: flex;
  align-items: center;
}

.notification-icon {
  font-size: 1.5rem;
  margin-right: 15px;
}

.notification-message {
  font-size: 1.1rem;
  font-weight: 500;
}

.notification-slide-in {
  bottom: 30px;
}

.notification-slide-out {
  bottom: -100px;
}

/* 購物車樣式 */
.cart-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.item-details {
  display: flex;
  flex-direction: column;
}

.item-name {
  font-weight: 500;
}

.item-price {
  color: #666;
  font-size: 0.9rem;
}

.item-quantity {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-weight: bold;
  margin: 0 5px;
}

.remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  margin-left: 10px;
  color: #e74c3c;
}

/* 通知樣式 */
.notification {
  position: fixed;
  top: 20px;
  right: -300px;
  background-color: #333;
  color: white;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  transition: right 0.5s ease;
  max-width: 300px;
  z-index: 1002;
}

.notification.info {
  background-color: #3498db;
}

.notification.success {
  background-color: #2ecc71;
}

.notification.error {
  background-color: #e74c3c;
}

.notification.show {
  right: 20px;
}
