# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 環境變數檔案（保留 .env.example）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日誌檔案
logs
*.log

# 運行時數據
pids
*.pid
*.seed
*.pid.lock

# 覆蓋率目錄
coverage
.nyc_output

# 依賴目錄
.npm
.eslintcache

# 可選的 npm 快取目錄
.npm

# 可選的 REPL 歷史
.node_repl_history

# 輸出的編譯二進制檔案
*.tgz

# Yarn 完整性檔案
.yarn-integrity

# dotenv 環境變數檔案
.env

# 下一個.js 建置輸出
.next

# Nuxt.js 建置 / 生成輸出
.nuxt
# 注意：不要忽略 dist 目錄，Docker 構建需要它

# Gatsby 檔案
.cache/
# 注意：不要忽略 public 目錄，Docker 構建需要它

# Storybook 建置輸出
.out
.storybook-out

# 臨時資料夾
tmp/
temp/

# 編輯器和 IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 作業系統生成的檔案
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# 文檔
*.md
!README.md

# 測試檔案
*.test.js
*.spec.js
test/
tests/

# 開發腳本
rebuild-docker.ps1
rebuild-docker.sh
verify-deployment.ps1
start.ps1

# 確保預設檔案不被忽略
!BDD_*.txt
!AAprompt_*.txt