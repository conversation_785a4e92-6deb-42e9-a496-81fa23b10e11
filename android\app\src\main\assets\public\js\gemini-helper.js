/**
 * USI AIOS 協助函數
 * 用於處理 Gemini API 返回的數據，確保與前端顯示邏輯相符
 */

/**
 * 從 Gemini API 返回的數據中提取菜單項目
 * @param {Object} result - API 返回的結果物件
 * @returns {Array} 處理後的菜單項目陣列
 */
function extractMenuItemsFromGemini(result) {
    if (!result || typeof result !== 'object') {
        console.error('無效的 Gemini API 結果:', result);
        return [];
    }
    
    let items = [];
    
    // 嘗試從不同的數據結構中獲取菜單項目
    try {
        // 情況 1: result.data.matches 存在且是陣列
        if (result.data && Array.isArray(result.data.matches)) {
            items = result.data.matches;
            console.log('從 result.data.matches 獲取菜單項目:', items);
        }        // 情況 2: result.data 本身是陣列
        else if (Array.isArray(result.data)) {
            // 確保每個項目都是可用的物件格式
            items = result.data.map(item => {
                // 如果項目是基本類型，將它轉換為物件
                if (typeof item !== 'object' || item === null) {
                    return { name: String(item), price: 0, quantity: 1 };
                }
                return item;
            });
            console.log('從 result.data 陣列獲取菜單項目:', items);
        }
        // 情況 3: 從分析實體中獲取可能的菜單項目
        else if (result.analysis && result.analysis.entities) {
            const entities = result.analysis.entities;
            
            // 檢查是否有 menuItems 陣列
            if (Array.isArray(entities.menuItems)) {
                items = entities.menuItems.map(item => ({
                    name: item,
                    price: 0, // 無法從實體獲取價格
                    quantity: 1
                }));
                console.log('從 entities.menuItems 獲取菜單項目:', items);
            }
            
            // 尋找數量實體，嘗試匹配數量與項目
            if (Array.isArray(entities.quantities)) {
                const quantities = entities.quantities;
                // 如果有數量但沒有項目，可能需要創建一個虛擬項目
                if (items.length === 0 && quantities.length > 0) {
                    items = quantities.map((q, index) => ({
                        name: `項目 ${index + 1}`,
                        price: 0,
                        quantity: parseInt(q.value) || 1
                    }));
                    console.log('從 quantities 創建虛擬菜單項目:', items);
                }
            }
        }
        // 情況 4: result.data 是單一物件而非陣列
        else if (result.data && typeof result.data === 'object') {
            // 如果有 menuItem 或 name 屬性，可能是單一項目
            if (result.data.name || result.data.menuItem) {
                const item = {
                    name: result.data.name || result.data.menuItem,
                    price: parseFloat(result.data.price || 0),
                    quantity: parseInt(result.data.quantity || 1)
                };
                items = [item];
                console.log('從單一物件創建菜單項目:', items);
            }
            // 如果是來自 NLP 服務的格式
            else {
                console.log('嘗試從 NLP 服務數據格式獲取菜單項目');
                // 提取所有可能包含菜單項目的屬性
                for (const key in result.data) {
                    const value = result.data[key];
                    if (Array.isArray(value)) {
                        items = [...items, ...value];
                    }
                }
            }
        }
    } catch (error) {
        console.error('提取菜單項目時發生錯誤:', error);
    }
      // 檢查是否應該使用 unidentified 數組
    if (items.length === 0 && result.data && Array.isArray(result.data.unidentified)) {
        items = result.data.unidentified.map(item => ({
            name: String(item),
            price: 0,
            quantity: 1
        }));
        console.log('從 result.data.unidentified 獲取項目:', items);
    }

    // 如果還沒有找到項目，直接嘗試將整個 result.data 作為項目處理
    if (items.length === 0 && result.data) {
        console.log('嘗試從原始 result.data 構建菜單項目');
        if (typeof result.data === 'object') {
            // 嘗試找出可能的項目和價格
            if (result.data.input && typeof result.data.input === 'string') {
                // 假設 input 包含餐點名稱
                items.push({
                    name: result.data.input,
                    price: 0,
                    quantity: 1
                });
            }
        }
    }
      // 最終處理：確保每個項目都有正確的屬性格式
    const processedItems = items.filter(item => item && (typeof item === 'object' || typeof item === 'string')).map(item => {
        // 如果項目是字符串，將其轉換為對象
        if (typeof item === 'string') {
            return {
                name: item,
                price: 139, // 默認價格
                quantity: 1 // 默認數量
            };
        }        // 確保每個項目都有基本屬性
        // 優先使用name_zh或name_en作為名稱
        let itemName;
        if (item.name_zh) {
            itemName = item.name_zh;
        } else if (item.name_en) {
            itemName = item.name_en;
        } else if (item.name) {
            if (typeof item.name === 'object' && item.name.zh) {
                itemName = item.name.zh;
            } else {
                itemName = item.name;
            }
        } else {
            itemName = item.item || item.title || item.text || String(item).replace("[object Object]", "大麥克套餐") || '未知項目';
        }
        
        return {
            name: itemName,
            price: parseFloat(item.price || 0), // 保留原始價格，不再自動替換為139
            quantity: parseInt(item.quantity || item.count || 1) || 1 // 確保數量至少為1
        };
    });
    
    // 合併相同名稱的菜單項目
    const itemsMap = {};
    processedItems.forEach(item => {
        const itemName = item.name;
        if (itemsMap[itemName]) {
            // 如果已存在相同名稱的項目，合併數量
            itemsMap[itemName].quantity += item.quantity;
        } else {
            // 否則新建一個項目
            itemsMap[itemName] = { ...item };
        }
    });
    
    // 轉換回陣列
    const mergedItems = Object.values(itemsMap);
    
    console.log('最終處理後的菜單項目:', mergedItems);
    return mergedItems;
}
