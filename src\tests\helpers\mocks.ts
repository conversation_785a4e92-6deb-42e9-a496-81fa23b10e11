/**
 * Mock 服務集合
 * 提供測試時使用的模擬服務
 */

// Mock Gemini API Response
export const mockGeminiResponse = {
  success: true,
  data: {
    prompt: "模擬的 APPPrompt 內容",
    parameters: {
      language: "zh-TW",
      temperature: 0.1
    },
    metadata: {
      source: "bdd" as const,
      generatedAt: new Date(),
      aiGenerated: true
    }
  }
};

// Mock Menu Data
export const mockMenuData = {
  restaurant_id: "test-restaurant",
  restaurant_name: "測試餐廳",
  categories: [
    {
      id: "burgers",
      name_zh: "漢堡",
      name_en: "Burgers",
      items: [
        {
          id: "big-mac",
          name_zh: "大麥克漢堡",
          name_en: "Big Mac",
          price: 150,
          availability: true
        },
        {
          id: "quarter-pounder",
          name_zh: "四盎司牛肉堡",
          name_en: "Quarter Pounder",
          price: 135,
          availability: true
        }
      ]
    },
    {
      id: "drinks",
      name_zh: "飲品",
      name_en: "Drinks",
      items: [
        {
          id: "coke",
          name_zh: "可樂",
          name_en: "Coca Cola",
          price: 30,
          availability: true
        }
      ]
    }
  ],
  last_updated: new Date(),
  version: "1.0.0"
};

// Mock Order Data
export const mockOrderData = {
  id: "order-123",
  items: [
    {
      id: "big-mac",
      name: "大麥克漢堡",
      quantity: 2,
      price: 150,
      total: 300
    },
    {
      id: "coke",
      name: "可樂",
      quantity: 1,
      price: 30,
      total: 30
    }
  ],
  total: 330,
  status: "pending",
  timestamp: new Date()
};

// Mock BDD Spec
export const mockBDDSpec = {
  feature: "自然語言點餐功能",
  scenario: "用戶使用自然語言進行點餐",
  given: ["用戶進入點餐系統", "系統已載入菜單數據"],
  when: ["用戶說出點餐需求"],
  then: ["系統識別菜單項目", "系統計算總價", "系統生成訂單"]
};

// Mock Firebase Service
export class MockFirebaseService {
  private isConnected = false;
  public simulateError = false;
  public simulateTimeout = false;
  private mockMenus: Map<string, any> = new Map();
  private mockOrders: any[] = [];

  constructor() {
    // 預設一些測試資料
    this.mockMenus.set('test-menu-123', [
      { id: 'test-001', name_zh: '測試漢堡', price: 120, category: '漢堡' },
      { id: 'test-002', name_zh: '可樂', price: 30, category: '飲品' }
    ]);
  }

  async connect(): Promise<boolean> {
    this.isConnected = true;
    return true;
  }

  async disconnect(): Promise<void> {
    this.isConnected = false;
  }

  isReady(): boolean {
    return this.isConnected;
  }

  // 菜單相關方法
  async saveMenu(menuData: any[]): Promise<{ success: boolean; id?: string; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    if (this.simulateTimeout) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { success: false, error: '操作超時' };
    }

    // 驗證資料
    if (!Array.isArray(menuData) || menuData.some(item => 
      !item || !item.id || !item.name_zh || typeof item.price !== 'number' || item.price < 0
    )) {
      return { success: false, error: '無效的菜單資料' };
    }

    const menuId = `menu-${Date.now()}`;
    this.mockMenus.set(menuId, menuData);
    
    return { success: true, id: menuId };
  }

  async getMenu(menuId: string): Promise<{ success: boolean; data?: any[]; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    if (this.simulateTimeout) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { success: false, error: '操作超時' };
    }

    if (menuId === 'non-existent-menu') {
      return { success: false, error: '菜單不存在' };
    }

    const menuData = this.mockMenus.get(menuId);
    if (!menuData) {
      return { success: false, error: '菜單不存在' };
    }

    return { success: true, data: menuData };
  }

  async updateMenuItem(menuId: string, itemId: string, updateData: any): Promise<{ success: boolean; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    const menuData = this.mockMenus.get(menuId);
    if (!menuData) {
      return { success: false, error: '菜單不存在' };
    }

    const itemIndex = menuData.findIndex((item: any) => item.id === itemId);
    if (itemIndex === -1) {
      return { success: false, error: '菜單項目不存在' };
    }

    menuData[itemIndex] = { ...menuData[itemIndex], ...updateData };
    return { success: true };
  }

  async deleteMenuItem(menuId: string, itemId: string): Promise<{ success: boolean; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    const menuData = this.mockMenus.get(menuId);
    if (!menuData) {
      return { success: false, error: '菜單不存在' };
    }

    const itemIndex = menuData.findIndex((item: any) => item.id === itemId);
    if (itemIndex === -1) {
      return { success: false, error: '菜單項目不存在' };
    }

    menuData.splice(itemIndex, 1);
    return { success: true };
  }

  async listMenus(): Promise<{ success: boolean; data?: any[]; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    const menus = Array.from(this.mockMenus.entries()).map(([id, data]) => ({
      id,
      name: `菜單 ${id}`,
      itemCount: data.length,
      createdAt: new Date()
    }));

    return { success: true, data: menus };
  }

  // 訂單相關方法
  async saveOrder(orderData: any): Promise<{ success: boolean; orderId?: string; id?: string; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    // 驗證訂單資料
    if (!orderData || !orderData.userId || !Array.isArray(orderData.items) || typeof orderData.total !== 'number') {
      return { success: false, error: '無效的訂單資料' };
    }

    const orderId = `order-${Date.now()}`;
    const order = {
      ...orderData,
      id: orderId,
      status: 'pending',
      createdAt: new Date()
    };

    this.mockOrders.push(order);
    
    return { success: true, orderId, id: orderId };
  }

  async getUserOrders(userId: string): Promise<{ success: boolean; data?: any[]; error?: string }> {
    if (this.simulateError) {
      return { success: false, error: '資料庫連接錯誤' };
    }

    const userOrders = this.mockOrders.filter(order => order.userId === userId);
    return { success: true, data: userOrders };
  }

  async getOrder(id: string): Promise<any> {
    const order = this.mockOrders.find(order => order.id === id);
    return order || {
      ...mockOrderData,
      id
    };
  }
}

// Mock Gemini Service
export class MockGeminiService {
  private apiKey: string;

  constructor(apiKey = "mock-api-key") {
    this.apiKey = apiKey;
  }

  async generateFromBDD(bddSpec: any, menuData?: any, language = "zh-TW"): Promise<any> {
    return {
      prompt: `基於 BDD 規範生成的模擬 APPPrompt: ${bddSpec.feature}`,
      parameters: { language },
      metadata: {
        source: "bdd",
        generatedAt: new Date(),
        aiGenerated: true
      }
    };
  }

  async processNaturalLanguage(input: string, context?: any): Promise<any> {
    const mockResult = {
      intent: "order",
      entities: [
        { type: "food", value: "漢堡", confidence: 0.9 },
        { type: "quantity", value: "2", confidence: 0.8 }
      ],
      response: "我理解您想要點 2 個漢堡。"
    };

    return mockResult;
  }
}

// Mock HTTP Response Helper
export function createMockResponse(data: any, status = 200) {
  return {
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    data,
    headers: {
      'content-type': 'application/json'
    }
  };
}

// Mock Error Helper
export function createMockError(message: string, code = 500) {
  const error = new Error(message);
  (error as any).status = code;
  return error;
}

// Mock Async Operation Helper
export function mockAsyncOperation<T>(
  result: T, 
  delay = 100, 
  shouldFail = false
): Promise<T> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (shouldFail) {
        reject(createMockError("Mock operation failed"));
      } else {
        resolve(result);
      }
    }, delay);
  });
}

// Mock Environment Variables
export function mockEnvVars(vars: Record<string, string>) {
  const originalEnv = { ...process.env };
  
  Object.keys(vars).forEach(key => {
    process.env[key] = vars[key];
  });

  return () => {
    // Restore original environment
    Object.keys(vars).forEach(key => {
      if (originalEnv[key] !== undefined) {
        process.env[key] = originalEnv[key];
      } else {
        delete process.env[key];
      }
    });
  };
}

export default {
  mockGeminiResponse,
  mockMenuData,
  mockOrderData,
  mockBDDSpec,
  MockFirebaseService,
  MockGeminiService,
  createMockResponse,
  createMockError,
  mockAsyncOperation,
  mockEnvVars
};
