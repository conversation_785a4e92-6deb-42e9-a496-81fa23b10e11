/**
 * NLP 相關的數據傳輸對象 (DTO)
 */

import { Language } from '../entities/User.js';
import { ApiStatus } from '../enums/AppEnums.js';

// NLP 處理請求 DTO
export interface NLPProcessRequestDto {
  input: string;
  language?: Language;
  sessionId?: string;
  appPrompt?: string;
  context?: NLPContextDto;
  options?: NLPOptionsDto;
}

// NLP 上下文信息
export interface NLPContextDto {
  restaurant_id?: string;
  user_id?: string;
  previous_orders?: string[];
  current_cart?: any[];
  time_of_day?: string;
  location?: string;
}

// NLP 處理選項
export interface NLPOptionsDto {
  strictMode?: boolean;
  enableFuzzyMatch?: boolean;
  maxSuggestions?: number;
  includeAnalysis?: boolean;
  timeout?: number;
}

// NLP 處理響應 DTO
export interface NLPProcessResponseDto {
  status: ApiStatus;
  data: NLPResultDto;
  analysis?: NLPAnalysisDto;
  suggestions?: SuggestionDto[];
  meta: NLPMetaDto;
}

// NLP 結果數據
export interface NLPResultDto {
  matches: MatchedItemDto[];
  unidentified: string[];
  totalAmount: number;
  confidence: number;
  intent: string;
  aiResponse?: string;
}

// 匹配的菜單項
export interface MatchedItemDto {
  id: string;
  name: string;
  quantity: number;
  price: number;
  confidence: number;
  matchType: 'exact' | 'fuzzy' | 'ai_suggested';
  originalInput: string;
  name_en?: string;
  name_zh?: string;
  name_jp?: string;
  image_url?: string;
  category?: string;
}

// NLP 分析結果
export interface NLPAnalysisDto {
  entities: EntityDto[];
  intent: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  language_detected?: Language;
  processing_method: 'ai' | 'rule_based' | 'hybrid';
  confidence_score: number;
  processing_time: number;
}

// 識別的實體
export interface EntityDto {
  type: 'food_item' | 'quantity' | 'modifier' | 'preference';
  value: string;
  confidence: number;
  start_position: number;
  end_position: number;
}

// 建議項目
export interface SuggestionDto {
  type: 'alternative' | 'addition' | 'popular' | 'promotion';
  item: MatchedItemDto;
  reason: string;
  confidence: number;
}

// NLP 元數據
export interface NLPMetaDto {
  requestId: string;
  timestamp: string;
  processingTime: number;
  language: Language;
  model_used?: string;
  appPromptVersion?: string;
  menuVersion?: string;
}

// Prompt 生成請求 DTO
export interface GeneratePromptRequestDto {
  type: 'bdd' | 'aa_prompt';
  content: string;
  language: Language;
  menuData?: any;
  options?: PromptOptionsDto;
}

// Prompt 選項
export interface PromptOptionsDto {
  include_menu?: boolean;
  include_examples?: boolean;
  optimization_level?: 'basic' | 'standard' | 'advanced';
  custom_instructions?: string;
}

// Prompt 生成響應 DTO
export interface GeneratePromptResponseDto {
  status: ApiStatus;
  prompt: string;
  metadata: PromptMetadataDto;
  validation?: PromptValidationDto;
}

// Prompt 元數據
export interface PromptMetadataDto {
  version: string;
  generatedAt: string;
  language: Language;
  type: string;
  tokenCount?: number;
  estimatedCost?: number;
}

// Prompt 驗證結果
export interface PromptValidationDto {
  isValid: boolean;
  score: number;
  issues: ValidationIssueDto[];
  suggestions: string[];
}

// 驗證問題
export interface ValidationIssueDto {
  type: 'syntax' | 'semantic' | 'performance' | 'best_practice';
  severity: 'error' | 'warning' | 'info';
  message: string;
  line?: number;
  suggestion?: string;
}
