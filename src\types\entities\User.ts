/**
 * 統一的用戶和會話實體定義
 */

export interface User {
  id: string;
  // 基本信息
  name?: string;
  email?: string;
  phone?: string;
  // 偏好設置
  language: Language;
  timezone?: string;
  // 地址信息
  addresses?: UserAddress[];
  defaultAddressId?: string;
  // 支付信息
  paymentMethods?: PaymentMethod[];
  defaultPaymentMethodId?: string;
  // 用戶統計
  totalOrders?: number;
  totalSpent?: number;
  // 時間戳
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

export interface UserAddress {
  id: string;
  label: string; // 家、公司等
  street: string;
  city: string;
  state?: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
  deliveryInstructions?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'digital_wallet' | 'cash';
  label: string;
  lastFourDigits?: string;
  expiryDate?: string;
  isDefault: boolean;
}

export interface Session {
  id: string;
  userId?: string;
  // 會話狀態
  language: Language;
  restaurantId?: string;
  // APPprompt 信息
  appPrompt?: string;
  appPromptVersion?: string;
  // 菜單緩存
  menuData?: any; // 將來可以用 Menu 類型替換
  menuVersion?: string;
  // 當前訂單
  currentOrder?: {
    items: any[]; // 將來用 OrderItem[] 替換
    totalAmount: number;
  };
  // 會話時間
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
  // 會話統計
  requestCount: number;
  lastActivityAt: Date;
}

export enum Language {
  ZH_TW = 'zh-TW',
  EN_US = 'en-US', 
  JA_JP = 'ja-JP'
}

// 會話創建請求
export interface CreateSessionRequest {
  userId?: string;
  language: Language;
  restaurantId?: string;
}

// 會話更新請求
export interface UpdateSessionRequest {
  language?: Language;
  restaurantId?: string;
  appPrompt?: string;
  menuData?: any;
}
