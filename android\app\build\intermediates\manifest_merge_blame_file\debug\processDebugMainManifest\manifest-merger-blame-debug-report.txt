1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aios.app.ordering"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- suppress AndroidElementNotAllowed -->
12    <queries>
12-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:5:5-9:15
13        <intent>
13-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:6:9-8:18
14            <action android:name="android.speech.RecognitionService" />
14-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:7:13-72
14-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:7:21-69
15        </intent>
16        <intent>
16-->[:capacitor-community-text-to-speech] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
17            <action android:name="android.intent.action.TTS_SERVICE" />
17-->[:capacitor-community-text-to-speech] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
17-->[:capacitor-community-text-to-speech] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
18        </intent>
19        <intent>
19-->[:capacitor-camera] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
20            <action android:name="android.media.action.IMAGE_CAPTURE" />
20-->[:capacitor-camera] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
20-->[:capacitor-camera] C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
21        </intent>
22    </queries>
23
24    <!-- Permissions -->
25
26    <uses-permission android:name="android.permission.INTERNET" />
26-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:47:5-67
26-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:47:22-64
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:48:5-71
27-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:48:22-68
28    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
28-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:49:5-80
28-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:49:22-77
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:50:5-68
29-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:50:22-65
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:51:5-66
30-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:51:22-63
31
32    <permission
32-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.aios.app.ordering.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.aios.app.ordering.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
37
38    <application
38-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:11:5-43:19
39        android:allowBackup="true"
39-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:12:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:13:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:14:9-41
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:15:9-54
46        android:supportsRtl="true"
46-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:16:9-35
47        android:testOnly="true"
48        android:theme="@style/AppTheme" >
48-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:17:9-40
49        <activity
49-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:19:9-32:20
50            android:name="com.aios.app.ordering.MainActivity"
50-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:21:13-41
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
51-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:20:13-140
52            android:exported="true"
52-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:25:13-36
53            android:label="@string/title_activity_main"
53-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:22:13-56
54            android:launchMode="singleTask"
54-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:24:13-44
55            android:theme="@style/AppTheme.NoActionBarLaunch" >
55-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:23:13-62
56            <intent-filter>
56-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:27:13-30:29
57                <action android:name="android.intent.action.MAIN" />
57-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:28:17-69
57-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:28:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:29:17-77
59-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:29:27-74
60            </intent-filter>
61        </activity>
62
63        <provider
64            android:name="androidx.core.content.FileProvider"
64-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:35:13-62
65            android:authorities="com.aios.app.ordering.fileprovider"
65-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:36:13-64
66            android:exported="false"
66-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:37:13-37
67            android:grantUriPermissions="true" >
67-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:38:13-47
68            <meta-data
68-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:39:13-41:64
69                android:name="android.support.FILE_PROVIDER_PATHS"
69-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:40:17-67
70                android:resource="@xml/file_paths" />
70-->C:\Users\<USER>\AndroidStudioProjects\New Natural Order - Android\android\app\src\main\AndroidManifest.xml:41:17-51
71        </provider>
72        <provider
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
73            android:name="androidx.startup.InitializationProvider"
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
74            android:authorities="com.aios.app.ordering.androidx-startup"
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
75            android:exported="false" >
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
76            <meta-data
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.emoji2.text.EmojiCompatInitializer"
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
78                android:value="androidx.startup" />
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
80-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
81                android:value="androidx.startup" />
81-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
82            <meta-data
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
84                android:value="androidx.startup" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
85        </provider>
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106    </application>
107
108</manifest>
