/**
 * GeminiService Jest 測試
 * 使用 Mock 進行完整的單元測試
 */

import { MockGeminiService, mockBDDSpec, mockMenuData, mockEnvVars } from '../../helpers/mocks.js';

describe('GeminiService (Mocked)', () => {
  let mockService: MockGeminiService;
  let restoreEnv: () => void;

  beforeEach(() => {
    mockService = new MockGeminiService();
    // 設定測試環境變數
    restoreEnv = mockEnvVars({
      'GEMINI_API_KEY': 'test-api-key-123',
      'NODE_ENV': 'test'
    });
  });

  afterEach(() => {
    restoreEnv();
  });

  describe('initialization', () => {
    it('should initialize with API key', () => {
      expect(mockService).toBeDefined();
      expect(mockService).toBeInstanceOf(MockGeminiService);
    });

    it('should handle missing API key gracefully', () => {
      const restoreNoKey = mockEnvVars({ 'GEMINI_API_KEY': '' });
      
      // 模擬沒有 API key 的情況
      expect(() => new MockGeminiService('')).not.toThrow();
      
      restoreNoKey();
    });
  });

  describe('generateFromBDD', () => {
    it('should generate APPPrompt from BDD specification', async () => {
      const result = await mockService.generateFromBDD(mockBDDSpec, mockMenuData, 'zh-TW');

      expect(result).toBeDefined();
      expect(result.prompt).toContain('自然語言點餐功能');
      expect(result.metadata.source).toBe('bdd');
      expect(result.metadata.aiGenerated).toBe(true);
      expect(result.parameters.language).toBe('zh-TW');
    });

    it('should handle different languages', async () => {
      const languages = ['zh-TW', 'en', 'ja'];

      for (const lang of languages) {
        const result = await mockService.generateFromBDD(mockBDDSpec, mockMenuData, lang);
        expect(result.parameters.language).toBe(lang);
      }
    });

    it('should work without menu data', async () => {
      const result = await mockService.generateFromBDD(mockBDDSpec, null, 'zh-TW');

      expect(result).toBeDefined();
      expect(result.prompt).toBeDefined();
      expect(result.metadata.source).toBe('bdd');
    });

    it('should handle complex BDD specifications', async () => {
      const complexBDD = {
        feature: "複雜的多步驟點餐流程",
        scenario: "用戶進行多項目點餐並修改訂單",
        given: [
          "用戶已登入系統",
          "系統載入完整菜單",
          "購物車為空"
        ],
        when: [
          "用戶選擇多個菜單項目",
          "用戶修改數量",
          "用戶確認訂單"
        ],
        then: [
          "系統計算正確總價",
          "系統生成訂單號",
          "系統發送確認訊息"
        ]
      };

      const result = await mockService.generateFromBDD(complexBDD, mockMenuData);
      expect(result.prompt).toContain('複雜的多步驟點餐流程');
    });
  });

  describe('processNaturalLanguage', () => {
    it('should process simple food order', async () => {
      const input = "我要兩個漢堡";
      const result = await mockService.processNaturalLanguage(input);

      expect(result).toBeDefined();
      expect(result.intent).toBe('order');
      expect(result.entities).toHaveLength(2);
      
      const foodEntity = result.entities.find((e: any) => e.type === 'food');
      const quantityEntity = result.entities.find((e: any) => e.type === 'quantity');
      
      expect(foodEntity.value).toBe('漢堡');
      expect(quantityEntity.value).toBe('2');
    });

    it('should handle complex natural language input', async () => {
      const complexInputs = [
        "我想要一個大麥克套餐，不要洋蔥，飲料換成可樂",
        "幫我點三份薯條和兩杯咖啡",
        "今天的特餐有什麼？我要最便宜的那個"
      ];

      for (const input of complexInputs) {
        const result = await mockService.processNaturalLanguage(input);
        expect(result).toBeDefined();
        expect(result.intent).toBeDefined();
        expect(result.response).toBeDefined();
      }
    });

    it('should provide context-aware responses', async () => {
      const context = {
        currentOrder: mockMenuData.categories[0].items[0],
        userPreferences: { spicyLevel: 'mild' }
      };

      const result = await mockService.processNaturalLanguage(
        "加辣一點", 
        context
      );

      expect(result).toBeDefined();
      expect(result.response).toBeDefined();
    });
  });

  describe('error handling', () => {
    it('should handle malformed BDD specifications', async () => {
      const malformedBDD = {
        feature: "",
        scenario: null,
        given: [],
        when: undefined,
        then: ["some result"]
      };

      // Mock service 應該能處理錯誤輸入
      await expect(mockService.generateFromBDD(malformedBDD)).resolves.toBeDefined();
    });

    it('should handle empty natural language input', async () => {
      const emptyInputs = ["", "   ", null, undefined];

      for (const input of emptyInputs) {
        const result = await mockService.processNaturalLanguage(input as string);
        expect(result).toBeDefined();
      }
    });
  });

  describe('performance', () => {
    it('should respond within reasonable time', async () => {
      const startTime = Date.now();
      
      await mockService.generateFromBDD(mockBDDSpec, mockMenuData);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // 應該在 1 秒內完成
    });

    it('should handle concurrent requests', async () => {
      const promises = Array.from({ length: 5 }, (_, i) => 
        mockService.processNaturalLanguage(`測試請求 ${i}`)
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.response).toBeDefined();
      });
    });
  });

  describe('integration scenarios', () => {
    it('should work in end-to-end ordering scenario', async () => {
      // Step 1: 生成系統提示
      const systemPrompt = await mockService.generateFromBDD(mockBDDSpec, mockMenuData);
      expect(systemPrompt).toBeDefined();

      // Step 2: 處理用戶輸入
      const userInput = "我要一個大麥克和一杯可樂";
      const nlpResult = await mockService.processNaturalLanguage(userInput);
      expect(nlpResult.intent).toBe('order');

      // Step 3: 驗證完整流程
      expect(systemPrompt.prompt).toBeDefined();
      expect(nlpResult.entities.length).toBeGreaterThan(0);
    });

    it('should maintain consistency across multiple interactions', async () => {
      const interactions = [
        "我要看菜單",
        "大麥克多少錢？",
        "好，我要一個大麥克",
        "再加一杯可樂",
        "總共多少錢？"
      ];

      const responses = [];
      for (const interaction of interactions) {
        const result = await mockService.processNaturalLanguage(interaction);
        responses.push(result);
      }

      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        expect(response).toBeDefined();
        expect(response.response).toBeDefined();
      });
    });
  });
});
