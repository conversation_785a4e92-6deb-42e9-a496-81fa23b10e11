/**
 * 統一語音識別管理器
 * 解決多平台兼容性和狀態管理問題
 */

class SpeechManager {
  constructor() {
    this.isRecognizing = false;
    this.recognition = null;
    this.currentLanguage = 'zh-TW';
    this.fallbackMode = false;
    this.lastSpeechTime = null;
    this.silenceTimer = null;
    this.SILENCE_TIMEOUT = 15000; // 15秒靜默超時
    this.capacitorInitialized = false; // 追蹤Capacitor是否已成功初始化

    // 回調函數
    this.onResult = null;
    this.onError = null;
    this.onStart = null;
    this.onEnd = null;
    this.onStatusUpdate = null;

    this.init();

    // 監聽應用恢復事件，重新檢查權限
    if (window.Capacitor) {
      document.addEventListener('resume', () => {
        console.log('📱 應用恢復，重新檢查語音權限...');
        this.recheckPermissions();
      });
    }
  }
  
  /**
   * 初始化語音識別
   */
  async init() {
    console.log('🎤 初始化語音識別管理器...');
    
    // 檢測環境
    const environment = this.detectEnvironment();
    console.log('🔍 環境檢測結果:', environment);
    
    if (environment.isCapacitor) {
      // Capacitor 環境
      await this.initCapacitorSpeech();
    } else if (environment.supportsBrowserSpeech) {
      // 瀏覽器環境
      this.initBrowserSpeech();
    } else {
      // 備用模式
      this.initFallbackMode();
    }
  }
  
  /**
   * 檢測運行環境
   */
  detectEnvironment() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const isAndroidWebView = /Android/.test(userAgent) && /wv/.test(userAgent);
    const isCapacitor = typeof window.Capacitor !== 'undefined';
    const supportsBrowserSpeech = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
    
    return {
      isAndroidWebView,
      isCapacitor,
      supportsBrowserSpeech,
      userAgent: userAgent.substring(0, 100)
    };
  }
  
  /**
   * 初始化 Capacitor 語音識別
   */
  async initCapacitorSpeech() {
    try {
      console.log('🎤 初始化 Capacitor 語音識別...');
      
      // 檢查插件是否可用
      if (!window.Capacitor || !window.Capacitor.Plugins.SpeechRecognition) {
        throw new Error('Capacitor 語音識別插件不可用');
      }
      
      const { SpeechRecognition } = window.Capacitor.Plugins;
      
      // 檢查權限
      const permission = await SpeechRecognition.requestPermissions();
      if (permission.speechRecognition !== 'granted') {
        throw new Error('語音識別權限未授予');
      }
      
      // 檢查可用性
      const available = await SpeechRecognition.available();
      if (!available.available) {
        throw new Error('設備不支援語音識別');
      }
      
      this.recognition = SpeechRecognition;
      this.capacitorInitialized = true; // 標記Capacitor已成功初始化

      // 設置 Capacitor 事件監聽器
      try {
        console.log('🔧 設置 Capacitor 事件監聽器...');

        // 監聽語音識別狀態變化
        SpeechRecognition.addListener('listeningState', (data) => {
          console.log('🎤 Capacitor 語音狀態變化:', data);
          if (data.status === 'listening') {
            this.isRecognizing = true;
            if (this.onStart) this.onStart();
          } else if (data.status === 'stopped') {
            this.isRecognizing = false;
            if (this.onEnd) this.onEnd();
          }
        });

        // 監聽語音識別結果
        SpeechRecognition.addListener('partialResults', (data) => {
          console.log('📝 Capacitor 部分結果:', data);
          if (data.matches && data.matches.length > 0) {
            const transcript = data.matches[0];
            if (this.onResult) this.onResult(transcript, false);
          }
        });

        console.log('✅ Capacitor 事件監聽器設置完成');
      } catch (listenerError) {
        console.warn('⚠️ 設置 Capacitor 事件監聽器失敗:', listenerError);
      }

      console.log('✅ Capacitor 語音識別初始化成功');
      
    } catch (error) {
      console.warn('❌ Capacitor 語音識別初始化失敗:', error);
      // 回退到瀏覽器語音識別
      if (this.detectEnvironment().supportsBrowserSpeech) {
        this.initBrowserSpeech();
      } else {
        this.initFallbackMode();
      }
    }
  }
  
  /**
   * 初始化瀏覽器語音識別
   */
  initBrowserSpeech() {
    try {
      console.log('🌐 初始化瀏覽器語音識別...');
      
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) {
        throw new Error('瀏覽器不支援語音識別');
      }
      
      this.recognition = new SpeechRecognition();
      this.setupBrowserSpeechEvents();
      
      console.log('✅ 瀏覽器語音識別初始化成功');
      
    } catch (error) {
      console.warn('❌ 瀏覽器語音識別初始化失敗:', error);
      this.initFallbackMode();
    }
  }
  
  /**
   * 設置瀏覽器語音識別事件
   */
  setupBrowserSpeechEvents() {
    if (!this.recognition) return;
    
    // 配置語音識別
    this.recognition.continuous = false; // 改為 false 避免狀態問題
    this.recognition.interimResults = true;
    this.recognition.maxAlternatives = 1;
    this.recognition.lang = this.currentLanguage;
    
    // 事件處理
    this.recognition.onstart = () => {
      console.log('🎤 語音識別開始');
      this.isRecognizing = true;
      this.lastSpeechTime = Date.now();
      if (this.onStart) this.onStart();
      if (this.onStatusUpdate) this.onStatusUpdate('listening');
    };
    
    this.recognition.onresult = (event) => {
      this.handleSpeechResult(event);
    };
    
    this.recognition.onerror = (event) => {
      console.error('🚫 語音識別錯誤:', event.error);
      this.handleSpeechError(event);
    };
    
    this.recognition.onend = () => {
      console.log('🔚 語音識別結束');
      this.isRecognizing = false;
      if (this.onEnd) this.onEnd();
      if (this.onStatusUpdate) this.onStatusUpdate('stopped');
    };
  }
  
  /**
   * 初始化備用模式
   */
  initFallbackMode() {
    console.log('📝 初始化備用模式（文字輸入）');
    this.fallbackMode = true;
    this.recognition = null;
  }
  
  /**
   * 開始語音識別
   */
  async start() {
    console.log('🚀 開始語音識別...');
    
    if (this.isRecognizing) {
      console.warn('⚠️ 語音識別已在進行中');
      return false;
    }
    
    if (this.fallbackMode) {
      console.log('📝 使用備用模式');
      this.showFallbackInterface();
      return true;
    }
    
    if (!this.recognition) {
      console.error('❌ 語音識別未初始化');
      if (this.onError) this.onError(new Error('語音識別不可用'));
      return false;
    }
    
    try {
      // 檢查是否為 Capacitor 環境
      if (typeof this.recognition.start === 'function') {
        // 瀏覽器語音識別
        await this.startBrowserSpeech();
      } else {
        // Capacitor 語音識別
        await this.startCapacitorSpeech();
      }
      
      return true;
      
    } catch (error) {
      console.error('❌ 啟動語音識別失敗:', error);
      if (this.onError) this.onError(error);
      return false;
    }
  }
  
  /**
   * 啟動瀏覽器語音識別
   */
  async startBrowserSpeech() {
    // 確保先停止任何現有的識別
    try {
      this.recognition.abort();
    } catch (e) {
      // 忽略停止錯誤
    }
    
    // 等待一小段時間確保完全停止
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 啟動語音識別
    this.recognition.start();
  }
  
  /**
   * 啟動 Capacitor 語音識別
   */
  async startCapacitorSpeech() {
    const result = await this.recognition.start({
      language: this.currentLanguage,
      maxResults: 1,
      prompt: '請說話...',
      partialResults: true,
      popup: false
    });
    
    if (result.matches && result.matches.length > 0) {
      const transcript = result.matches[0];
      if (this.onResult) this.onResult(transcript, true);
    }
  }
  
  /**
   * 停止語音識別
   */
  stop() {
    console.log('🛑 停止語音識別...');
    
    if (!this.isRecognizing) {
      console.log('ℹ️ 語音識別未在運行');
      return;
    }
    
    if (this.recognition && typeof this.recognition.stop === 'function') {
      this.recognition.stop();
    }
    
    this.isRecognizing = false;
    this.clearSilenceTimer();
    
    if (this.onStatusUpdate) this.onStatusUpdate('stopped');
  }
  
  /**
   * 處理語音識別結果
   */
  handleSpeechResult(event) {
    let interimTranscript = '';
    let finalTranscript = '';
    
    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript;
      if (event.results[i].isFinal) {
        finalTranscript += transcript;
      } else {
        interimTranscript += transcript;
      }
    }
    
    // 更新最後語音時間
    this.lastSpeechTime = Date.now();
    this.resetSilenceTimer();
    
    if (this.onResult) {
      if (finalTranscript) {
        this.onResult(finalTranscript, true);
      } else if (interimTranscript) {
        this.onResult(interimTranscript, false);
      }
    }
  }
  
  /**
   * 處理語音識別錯誤
   */
  handleSpeechError(event) {
    this.isRecognizing = false;
    
    let errorMessage = '語音識別發生錯誤';
    let shouldShowFallback = false;
    
    switch(event.error) {
      case 'not-allowed':
        errorMessage = '麥克風權限被拒絕';
        shouldShowFallback = true;
        break;
      case 'no-speech':
        errorMessage = '未檢測到語音';
        break;
      case 'network':
        errorMessage = '網路連接錯誤';
        shouldShowFallback = true;
        break;
      case 'audio-capture':
        errorMessage = '音頻捕獲失敗';
        shouldShowFallback = true;
        break;
      default:
        errorMessage = `語音識別錯誤: ${event.error}`;
        shouldShowFallback = true;
    }
    
    if (this.onError) this.onError(new Error(errorMessage));
    
    // 在特定錯誤情況下，自動切換到備用模式
    if (shouldShowFallback) {
      setTimeout(() => {
        this.showFallbackInterface();
      }, 1000);
    }
  }
  
  /**
   * 顯示備用界面
   */
  showFallbackInterface() {
    if (window.fallbackSpeechUI) {
      window.fallbackSpeechUI.showInterface();
    } else {
      // 簡單的備用方案：聚焦到文字輸入框
      const textInput = document.getElementById('userInput') || document.querySelector('textarea');
      if (textInput) {
        textInput.focus();
        if (this.onStatusUpdate) this.onStatusUpdate('fallback');
      }
    }
  }
  
  /**
   * 重置靜默計時器
   */
  resetSilenceTimer() {
    this.clearSilenceTimer();
    
    this.silenceTimer = setTimeout(() => {
      if (this.isRecognizing && this.lastSpeechTime && 
          (Date.now() - this.lastSpeechTime) >= this.SILENCE_TIMEOUT) {
        console.log('⏰ 檢測到長時間靜默，停止語音識別');
        this.stop();
      }
    }, this.SILENCE_TIMEOUT);
  }
  
  /**
   * 清除靜默計時器
   */
  clearSilenceTimer() {
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }
  }
  
  /**
   * 設置語言
   */
  setLanguage(language) {
    this.currentLanguage = language;
    if (this.recognition && this.recognition.lang !== undefined) {
      this.recognition.lang = language;
    }
  }
  
  /**
   * 獲取當前狀態
   */
  getStatus() {
    return {
      isRecognizing: this.isRecognizing,
      fallbackMode: this.fallbackMode,
      currentLanguage: this.currentLanguage,
      hasRecognition: !!this.recognition
    };
  }
  
  /**
   * 設置回調函數
   */
  setCallbacks(callbacks) {
    this.onResult = callbacks.onResult || null;
    this.onError = callbacks.onError || null;
    this.onStart = callbacks.onStart || null;
    this.onEnd = callbacks.onEnd || null;
    this.onStatusUpdate = callbacks.onStatusUpdate || null;
  }

  /**
   * 重新檢查權限並嘗試初始化Capacitor語音識別
   */
  async recheckPermissions() {
    // 如果已經使用Capacitor，不需要重新檢查
    if (this.capacitorInitialized) {
      console.log('✅ Capacitor 語音識別已初始化，無需重新檢查');
      return;
    }

    // 檢查是否為Capacitor環境
    const environment = this.detectEnvironment();
    if (!environment.isCapacitor) {
      console.log('ℹ️ 非Capacitor環境，無需重新檢查權限');
      return;
    }

    console.log('🔄 重新檢查Capacitor語音權限...');

    try {
      const { SpeechRecognition } = window.Capacitor.Plugins;

      // 檢查權限狀態
      const permission = await SpeechRecognition.requestPermissions();
      if (permission.speechRecognition === 'granted') {
        console.log('✅ 權限已授予，重新初始化Capacitor語音識別...');

        // 停止當前的語音識別
        if (this.isRecognizing) {
          await this.stop();
        }

        // 重新初始化Capacitor語音識別
        await this.initCapacitorSpeech();

        console.log('🎉 成功切換到Capacitor語音識別！');
      } else {
        console.log('❌ 權限仍未授予');
      }
    } catch (error) {
      console.warn('⚠️ 重新檢查權限失敗:', error);
    }
  }
}

// 創建全局實例
window.speechManager = new SpeechManager();

// 導出便利函數
window.startSpeechRecognition = () => window.speechManager.start();
window.stopSpeechRecognition = () => window.speechManager.stop();
window.getSpeechStatus = () => window.speechManager.getStatus();
