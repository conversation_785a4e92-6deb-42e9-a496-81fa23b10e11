/**
 * 備用語音 UI 適配器
 * 為備用語音服務提供用戶界面支持
 */

class FallbackSpeechUI {
  constructor() {
    this.isVisible = false;
    this.currentCommands = [];
    this.currentOptions = {};
    this.onTextInputCallback = null;
    this.onCommandSelectCallback = null;
    
    this.initializeEventListeners();
    this.createUIElements();
  }
  
  /**
   * 獲取翻譯文本
   */
  getTranslation(key) {
    if (typeof getTranslation === 'function') {
      return getTranslation(key);
    }
    // 備用翻譯
    const fallbackTranslations = {
      'fallback_speech_title': '語音輸入',
      'fallback_speech_input_label': '請輸入您的指令：',
      'fallback_speech_send': '發送',
      'fallback_speech_commands': '常用指令',
      'fallback_speech_tip': '提示：您可以直接輸入文字或選擇常用指令'
    };
    return fallbackTranslations[key] || key;
  }
  
  /**
   * 初始化事件監聽器
   */
  initializeEventListeners() {
    // 監聽備用語音服務事件
    window.addEventListener('fallback-speech-show', (event) => {
      this.showInterface(event.detail);
    });
    
    window.addEventListener('fallback-speech-hide', () => {
      this.hideInterface();
    });
    
    // 監聽鍵盤事件
    document.addEventListener('keydown', (event) => {
      if (this.isVisible) {
        this.handleKeyboardInput(event);
      }
    });
  }
  
  /**
   * 創建 UI 元素
   */
  createUIElements() {
    // 創建主容器
    this.container = document.createElement('div');
    this.container.id = 'fallback-speech-container';
    this.container.className = 'fallback-speech-container hidden';
    
    // 創建內容
    this.container.innerHTML = `
      <div class="fallback-speech-overlay">
        <div class="fallback-speech-modal">
          <div class="fallback-speech-header">
            <h3>${this.getTranslation('fallback_speech_title')}</h3>
            <button class="fallback-speech-close" type="button">×</button>
          </div>
          
          <div class="fallback-speech-content">
            <!-- 文字輸入區域 -->
            <div class="fallback-speech-input-section">
              <label for="fallback-speech-input">${this.getTranslation('fallback_speech_input_label')}</label>
              <div class="fallback-speech-input-group">
                <input 
                  type="text" 
                  id="fallback-speech-input" 
                  class="fallback-speech-input"
                  placeholder="例如：我要一杯咖啡"
                  autocomplete="off"
                />
                <button class="fallback-speech-send" type="button">${this.getTranslation('fallback_speech_send')}</button>
              </div>
            </div>
            
            <!-- 預設指令區域 -->
            <div class="fallback-speech-commands-section">
              <h4>${this.getTranslation('fallback_speech_commands')}</h4>
              <div class="fallback-speech-commands-grid" id="fallback-speech-commands">
                <!-- 動態生成指令按鈕 -->
              </div>
            </div>
            
            <!-- 分類標籤 -->
            <div class="fallback-speech-categories">
              <button class="fallback-speech-category active" data-category="all">全部</button>
              <button class="fallback-speech-category" data-category="order">點餐</button>
              <button class="fallback-speech-category" data-category="navigation">導航</button>
              <button class="fallback-speech-category" data-category="control">控制</button>
            </div>
          </div>
          
          <div class="fallback-speech-footer">
            <p class="fallback-speech-tip">
              ${this.getTranslation('fallback_speech_tip')}
            </p>
          </div>
        </div>
      </div>
    `;
    
    // 添加到頁面
    document.body.appendChild(this.container);
    
    // 綁定事件
    this.bindEvents();
  }
  
  /**
   * 綁定事件處理器
   */
  bindEvents() {
    const input = this.container.querySelector('#fallback-speech-input');
    const sendBtn = this.container.querySelector('.fallback-speech-send');
    const closeBtn = this.container.querySelector('.fallback-speech-close');
    const overlay = this.container.querySelector('.fallback-speech-overlay');
    
    // 文字輸入事件
    input.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        this.handleTextInput();
      }
    });
    
    sendBtn.addEventListener('click', () => {
      this.handleTextInput();
    });
    
    // 關閉事件
    closeBtn.addEventListener('click', () => {
      this.hideInterface();
    });
    
    overlay.addEventListener('click', (event) => {
      if (event.target === overlay) {
        this.hideInterface();
      }
    });
    
    // 分類切換事件
    this.container.addEventListener('click', (event) => {
      if (event.target.classList.contains('fallback-speech-category')) {
        this.handleCategorySwitch(event.target);
      }
      
      if (event.target.classList.contains('fallback-speech-command-btn')) {
        this.handleCommandSelect(event.target);
      }
    });
  }
  
  /**
   * 顯示界面
   */
  showInterface(detail = {}) {
    this.currentCommands = detail.commands || [];
    this.currentOptions = detail.options || {};
    
    // 更新指令列表
    this.updateCommandsList('all');
    
    // 顯示界面
    this.container.classList.remove('hidden');
    this.isVisible = true;
    
    // 聚焦輸入框
    setTimeout(() => {
      const input = this.container.querySelector('#fallback-speech-input');
      input.focus();
    }, 100);
    
    console.log('備用語音界面已顯示');
  }
  
  /**
   * 隱藏界面
   */
  hideInterface() {
    this.container.classList.add('hidden');
    this.isVisible = false;
    
    // 清空輸入
    const input = this.container.querySelector('#fallback-speech-input');
    input.value = '';
    
    console.log('備用語音界面已隱藏');
  }
  
  /**
   * 更新指令列表
   */
  updateCommandsList(category = 'all') {
    const commandsContainer = this.container.querySelector('#fallback-speech-commands');
    
    // 過濾指令
    const filteredCommands = category === 'all' 
      ? this.currentCommands 
      : this.currentCommands.filter(cmd => cmd.category === category);
    
    // 生成指令按鈕
    commandsContainer.innerHTML = filteredCommands.map(command => `
      <button 
        class="fallback-speech-command-btn" 
        data-command="${encodeURIComponent(JSON.stringify(command))}"
        title="${command.description}"
      >
        ${command.text}
      </button>
    `).join('');
  }
  
  /**
   * 處理分類切換
   */
  handleCategorySwitch(categoryBtn) {
    // 更新活動狀態
    this.container.querySelectorAll('.fallback-speech-category').forEach(btn => {
      btn.classList.remove('active');
    });
    categoryBtn.classList.add('active');
    
    // 更新指令列表
    const category = categoryBtn.dataset.category;
    this.updateCommandsList(category);
  }
  
  /**
   * 處理文字輸入
   */
  handleTextInput() {
    const input = this.container.querySelector('#fallback-speech-input');
    const text = input.value.trim();
    
    if (text) {
      console.log('處理文字輸入:', text);
      
      // 觸發文字輸入事件
      const event = new CustomEvent('fallback-speech-text-input', {
        detail: { text }
      });
      window.dispatchEvent(event);
      
      // 清空輸入並隱藏界面
      input.value = '';
      this.hideInterface();
    }
  }
  
  /**
   * 處理指令選擇
   */
  handleCommandSelect(commandBtn) {
    try {
      const commandData = JSON.parse(decodeURIComponent(commandBtn.dataset.command));
      console.log('處理指令選擇:', commandData);
      
      // 觸發指令選擇事件
      const event = new CustomEvent('fallback-speech-command-select', {
        detail: { command: commandData }
      });
      window.dispatchEvent(event);
      
      // 隱藏界面
      this.hideInterface();
    } catch (error) {
      console.error('處理指令選擇時發生錯誤:', error);
    }
  }
  
  /**
   * 處理鍵盤輸入
   */
  handleKeyboardInput(event) {
    // ESC 鍵關閉界面
    if (event.key === 'Escape') {
      this.hideInterface();
      event.preventDefault();
    }
  }
  
  /**
   * 設置回調函數
   */
  setCallbacks(onTextInput, onCommandSelect) {
    this.onTextInputCallback = onTextInput;
    this.onCommandSelectCallback = onCommandSelect;
  }
  
  /**
   * 獲取當前狀態
   */
  getState() {
    return {
      isVisible: this.isVisible,
      commandCount: this.currentCommands.length,
      currentOptions: this.currentOptions
    };
  }
}

// 等待 DOM 載入完成後再初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFallbackSpeechUI);
} else {
  initializeFallbackSpeechUI();
}

function initializeFallbackSpeechUI() {
  // 創建全局實例
  window.fallbackSpeechUI = new FallbackSpeechUI();
  
  // 導出便利函數
  window.showFallbackSpeech = (commands, options) => {
    window.fallbackSpeechUI.showInterface({ commands, options });
  };
  
  window.hideFallbackSpeech = () => {
    window.fallbackSpeechUI.hideInterface();
  };
  
  console.log('備用語音 UI 適配器已初始化');
}