/**
 * 重構後的 NLP 路由
 * 解決重複代碼和錯誤處理不統一的問題
 */

import { Router, Request, Response, NextFunction } from 'express';
// import { MenuProcessor } from '../services/MenuProcessor.js'; // 未使用
import { NLPService } from '../services/NLPService.js';
import { MenuItem } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import { ErrorHandler, createError } from '../utils/ErrorHandler.js';
// import path from 'node:path'; // 未使用
// import fs from 'node:fs';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

const router = Router();
const logger = createLogger('NLPRoute');
const nlpService = new NLPService();
// const menuProcessor = new MenuProcessor();

// 菜單存儲（後續可以移到 Redis 等緩存系統）
export const loadedMenus: { [key: string]: MenuItem[] } = {};

/**
 * 輸入驗證中間件
 */
function validateNLPInput(req: Request, _res: Response, next: NextFunction) {
  try {
    const { text, input } = req.body;
    const userInput = text || input;

    if (!userInput || typeof userInput !== 'string' || userInput.trim().length === 0) {
      throw createError.validation('請提供有效的輸入文本');
    }

    if (userInput.length > 1000) {
      throw createError.validation('輸入文本長度不能超過 1000 字符');
    }

    // 將處理後的輸入添加到 request 對象
    req.body.processedInput = userInput.trim();
    next();
  } catch (error) {
    next(error);
  }
}

/**
 * 菜單項目更新工具函數
 */
export function updateLoadedMenu(restaurantId: string, menuData: any): boolean {
  try {
    const items: MenuItem[] = [];
    
    if (menuData && menuData.categories) {
      menuData.categories.forEach((category: any) => {
        if (category && Array.isArray(category.items)) {
          items.push(...category.items);
        }
      });
    }
    
    if (items.length > 0) {
      loadedMenus[restaurantId] = items;
      logger.info('Menu updated successfully', {
        restaurantId,
        itemCount: items.length,
        availableRestaurants: Object.keys(loadedMenus)
      });
      return true;
    }
    
    logger.warn('Failed to update menu - no valid items found', { restaurantId });
    return false;
  } catch (error) {
    logger.error('Error updating menu', error as Error, { restaurantId });
    return false;
  }
}

/**
 * 自然語言處理 API
 */
async function processHandler(req: Request, res: Response, next: NextFunction): Promise<void> {
  const startTime = Date.now();
  
  try {
    const { processedInput, context, strictMode, language } = req.body;
    const restaurantId = context?.restaurant_id || 'default';

    logger.info('Processing NLP request', {
      inputLength: processedInput.length,
      language,
      restaurantId,
      strictMode
    });

    // 檢查菜單可用性（可選，因為 NLP 服務現在完全依賴 Gemini AI）
    const menuItems = loadedMenus[restaurantId] || [];
    if (menuItems.length === 0) {
      logger.warn('No menu data available', { restaurantId });
    }

    // 處理訂單
    const result = await nlpService.processOrderWithAppPromptFirst(processedInput, language);
    
    // 記錄處理時間
    const duration = Date.now() - startTime;
    logger.performance('NLP processing completed', duration, {
      success: result.success,
      language,
      restaurantId
    });

    // 業務事件記錄
    if (result.success) {
      logger.business('Order processed successfully', {
        itemCount: result.data?.matches?.length || 0,
        totalAmount: result.data?.totalAmount,
        language
      });
    }

    res.json({
      success: result.success,
      data: result.data,
      processed: {
        entities: result.analysis?.entities || []
      },
      analysis: result.analysis,
      error: result.error,
      meta: {
        processingTime: duration,
        language: language || 'auto-detect'
      }
    });

  } catch (error) {
    next(ErrorHandler.handle(error as Error, 'NLP processing'));
  }
}

/**
 * 訂單修改處理 API
 */
async function modifyHandler(req: Request, res: Response, next: NextFunction): Promise<void> {
  const startTime = Date.now();
  
  try {
    const { processedInput, currentItems, context, language } = req.body;

    // 驗證當前餐點列表
    if (!currentItems || !Array.isArray(currentItems)) {
      throw createError.validation('請提供有效的當前餐點列表');
    }

    const restaurantId = context?.restaurant_id || 'default';

    logger.info('Processing order modification', {
      inputLength: processedInput.length,
      currentItemCount: currentItems.length,
      language,
      restaurantId
    });

    // 檢查菜單可用性
    const menuItems = loadedMenus[restaurantId] || [];
    if (menuItems.length === 0) {
      logger.warn('No menu data available for modification', { restaurantId });
    }

    // 簡化的修改處理：將修改請求當作新訂單處理
    // 實際項目中應該實現更複雜的修改邏輯
    const result = await nlpService.processOrderWithAppPromptFirst(processedInput, language);
    
    const duration = Date.now() - startTime;
    logger.performance('Order modification completed', duration, {
      success: result.success,
      language,
      restaurantId
    });

    res.json({
      success: true,
      data: {
        type: 'modification',
        original: currentItems,
        modification: result.data,
        suggested: result.data // 這裡應該包含合併後的結果
      },
      analysis: result.analysis,
      meta: {
        processingTime: duration,
        language: language || 'auto-detect'
      }
    });

  } catch (error) {
    next(ErrorHandler.handle(error as Error, 'Order modification'));
  }
}

/**
 * 獲取可用菜單列表 API
 */
async function getAvailableMenusHandler(_req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const menus = Object.keys(loadedMenus).map(restaurantId => ({
      restaurantId,
      itemCount: loadedMenus[restaurantId].length,
      lastUpdated: new Date().toISOString() // 實際應該記錄真實的更新時間
    }));

    res.json({
      success: true,
      data: {
        availableMenus: menus,
        totalRestaurants: menus.length
      }
    });

  } catch (error) {
    next(ErrorHandler.handle(error as Error, 'Get available menus'));
  }
}

// 路由定義
router.post('/process', validateNLPInput, processHandler);
router.post('/modify', validateNLPInput, modifyHandler);
router.get('/menus', getAvailableMenusHandler);

// 健康檢查端點
router.get('/health', (_req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      service: 'NLP Route',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      availableMenus: Object.keys(loadedMenus).length
    }
  });
});

export default router;
