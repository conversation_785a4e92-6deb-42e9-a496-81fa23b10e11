/**
 * GeminiApiClient.ts
 * 純粹的 Gemini API 交互服務
 * 職責：封裝與 Google Gemini AI API 的通信邏輯
 */

import { GoogleGenerativeAI, GenerationConfig } from '@google/generative-ai';
import { createLogger } from '../utils/Logger.js';
import dotenv from 'dotenv';

// 加載環境變數
dotenv.config();

/**
 * Gemini API 客戶端
 * 負責與 Google Gemini AI API 的底層交互
 */
export class GeminiApiClient {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private defaultConfig: GenerationConfig;
  private logger = createLogger('GeminiApiClient');

  constructor(apiKey?: string) {
    const key = apiKey || process.env.GEMINI_API_KEY || '';    if (!key) {
      const error = new Error('未設置 GEMINI_API_KEY 環境變數');
      this.logger.error('初始化失敗：缺少 API 金鑰', error);
      throw error;
    }
    
    this.genAI = new GoogleGenerativeAI(key);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });
    
    this.defaultConfig = {
      temperature: 0.1,  // 降低創造性，提高準確性
      topP: 0.8,         // 降低隨機性
      topK: 20,          // 減少候選詞數量
      maxOutputTokens: 2048,
    };

    this.logger.info('Gemini API 客戶端已初始化', {
      model: 'gemini-1.5-pro',
      config: this.defaultConfig
    });
  }

  /**
   * 使用 Gemini 生成文本
   * @param prompt 輸入提示詞
   * @param config 可選的生成配置
   * @returns 生成的文本結果
   */
  async generateText(prompt: string, config: GenerationConfig = this.defaultConfig): Promise<string> {
    try {
      this.logger.debug('開始 Gemini 文本生成', {
        promptLength: prompt.length,
        config: config
      });

      const result = await this.model.generateContent({
        contents: [{
          role: 'user',
          parts: [{ text: prompt }]
        }],
        generationConfig: config
      });

      const response = await result.response;
      const text = response.text();

      this.logger.info('Gemini 文本生成成功', {
        inputLength: prompt.length,
        outputLength: text.length
      });

      return text;    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Gemini 文本生成失敗', error instanceof Error ? error : new Error(errorMessage), {
        promptLength: prompt.length,
        config: config
      });
      throw new Error(`Gemini API 調用失敗: ${errorMessage}`);
    }
  }

  /**
   * 檢查 API 連接狀態
   * @returns 是否可以正常連接
   */
  async healthCheck(): Promise<boolean> {
    try {
      this.logger.debug('執行 Gemini API 健康檢查');
      
      const testPrompt = "Hello, this is a connection test.";
      await this.generateText(testPrompt, {
        ...this.defaultConfig,
        maxOutputTokens: 10
      });

      this.logger.info('Gemini API 健康檢查通過');
      return true;    } catch (error) {
      this.logger.error('Gemini API 健康檢查失敗', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 獲取預設配置
   * @returns 預設的生成配置
   */
  getDefaultConfig(): GenerationConfig {
    return { ...this.defaultConfig };
  }

  /**
   * 更新預設配置
   * @param newConfig 新的配置
   */
  updateDefaultConfig(newConfig: Partial<GenerationConfig>): void {
    this.defaultConfig = {
      ...this.defaultConfig,
      ...newConfig
    };

    this.logger.info('更新 Gemini API 預設配置', {
      newConfig: this.defaultConfig
    });
  }
}

// 導出單例實例
export default new GeminiApiClient();
