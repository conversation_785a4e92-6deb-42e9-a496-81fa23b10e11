services:
  android-natural-order-app:
    build: .
    container_name: android-natural-order-app
    ports:
      - "4003:4003"
    environment:
      - NODE_ENV=production
      - PORT=4003
      - LOG_LEVEL=INFO
      # Firebase 配置 - 請在 .env 文件中設定實際值
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_AUTH_DOMAIN=${FIREBASE_AUTH_DOMAIN}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - FIREBASE_MESSAGING_SENDER_ID=${FIREBASE_MESSAGING_SENDER_ID}
      - FIREBASE_APP_ID=${FIREBASE_APP_ID}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - DEFAULT_LANGUAGE=${DEFAULT_LANGUAGE:-zh-TW}
      # 重要：請設定您的真實 Gemini API Key
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - USE_GEMINI_AI=true
      - GEMINI_MODEL=gemini-1.5-pro
      # 強制使用 MockOrderService 避免 Firebase 權限問題
      - USE_MOCK_ORDER_SERVICE=true
    volumes:
      # 掛載數據目錄到主機，確保數據持久化
      - ./uploads:/app/uploads
      - ./appPrompt:/app/appPrompt
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4003"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - natural-order-network

networks:
  natural-order-network:
    driver: bridge

volumes:
  uploads:
  appPrompt: