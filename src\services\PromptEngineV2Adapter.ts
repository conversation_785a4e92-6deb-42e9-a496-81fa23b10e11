/**
 * PromptEngineV2Adapter.ts
 * PromptEngine 的 V2 適配器
 * 將 PromptEngine 介面適配到 GeminiServiceV2
 */

import { BDDSpec, AAPrompt, APPPromptResult, MenuData } from '../types/menu.js';
import { GeminiServiceV2 } from './GeminiServiceV2.js';
import { PromptEngine } from './PromptEngine.js';
import { createLogger } from '../utils/Logger.js';

const logger = createLogger('PromptEngineV2Adapter');

/**
 * PromptEngine V2 適配器
 * 提供與 PromptEngine 相同的介面，但內部使用 GeminiServiceV2
 */
export class PromptEngineV2Adapter {
  private geminiServiceV2: GeminiServiceV2;
  private fallbackEngine: PromptEngine;
  private menuData: MenuData | null = null;

  constructor(geminiServiceV2?: GeminiServiceV2, fallbackEngine?: PromptEngine) {
    this.geminiServiceV2 = geminiServiceV2 || new GeminiServiceV2();
    this.fallbackEngine = fallbackEngine || new PromptEngine();
    
    logger.info('PromptEngineV2Adapter 已初始化', {
      hasV2Service: !!geminiServiceV2,
      hasFallback: !!fallbackEngine
    });
  }

  /**
   * 設置菜單數據
   */
  setMenuData(menuData: MenuData | null): void {
    this.menuData = menuData;
    logger.debug('菜單數據已更新', { 
      hasMenu: !!menuData,
      categories: menuData?.categories?.length || 0
    });
  }

  /**
   * 獲取菜單數據
   */
  getMenuData(): MenuData | null {
    return this.menuData;
  }

  /**
   * 驗證 BDD 語法
   */
  validateBDDSyntax(bddText: string): any {
    // 委託給傳統 PromptEngine，因為 GeminiServiceV2 沒有驗證功能
    logger.debug('委託 BDD 語法驗證給傳統 PromptEngine');
    return this.fallbackEngine.validateBDDSyntax(bddText);
  }

  /**
   * 驗證 AA Prompt 語法
   */
  validateAAPromptSyntax(aaText: string): any {
    // 委託給傳統 PromptEngine
    logger.debug('委託 AA Prompt 語法驗證給傳統 PromptEngine');
    return this.fallbackEngine.validateAAPromptSyntax(aaText);
  }

  /**
   * 從 BDD 生成 APPprompt - 使用 GeminiServiceV2
   */
  async generateFromBDD(bddSpec: BDDSpec, language?: string): Promise<APPPromptResult> {
    try {
      logger.info('使用 GeminiServiceV2 生成 BDD APPprompt');
      return await this.geminiServiceV2.generateFromBDD(bddSpec, this.menuData, language);
    } catch (error) {
      logger.warn('GeminiServiceV2 BDD 生成失敗，降級到傳統引擎', {
        error: error instanceof Error ? error.message : String(error)
      });
      return await this.fallbackEngine.generateFromBDD(bddSpec, language);
    }
  }

  /**
   * 從 AA Prompt 生成 APPprompt - 使用 GeminiServiceV2
   */
  async generateFromAA(aaPrompt: AAPrompt, language?: string): Promise<APPPromptResult> {
    try {
      logger.info('使用 GeminiServiceV2 生成 AA APPprompt');
      return await this.geminiServiceV2.generateFromAA(aaPrompt, this.menuData, language);
    } catch (error) {
      logger.warn('GeminiServiceV2 AA 生成失敗，降級到傳統引擎', {
        error: error instanceof Error ? error.message : String(error)
      });
      return await this.fallbackEngine.generateFromAA(aaPrompt, language);
    }
  }

  /**
   * 從自然語言生成 APPprompt - 使用 GeminiServiceV2
   */
  async generateFromNaturalLanguage(input: string, sessionId?: string): Promise<APPPromptResult> {
    try {
      logger.info('使用 GeminiServiceV2 生成自然語言 APPprompt');
      return await this.geminiServiceV2.parseNaturalLanguageToAppPrompt(input, this.menuData, sessionId);
    } catch (error) {
      logger.warn('GeminiServiceV2 自然語言生成失敗，降級到傳統引擎', {
        error: error instanceof Error ? error.message : String(error)
      });
      return await this.fallbackEngine.generateFromNaturalLanguage(input, sessionId);
    }
  }

  /**
   * 處理訂單動態 Prompt - 使用 GeminiServiceV2
   */
  async processOrderWithDynamicPrompt(
    customerInput: string, 
    generatedAppPrompt?: string, 
    language: string = 'zh-TW', 
    sessionId?: string
  ): Promise<string> {
    try {
      logger.info('使用 GeminiServiceV2 處理動態訂單', {
        hasAppPrompt: !!generatedAppPrompt,
        language,
        sessionId
      });
      
      // 使用 GeminiServiceV2 的自然語言處理
      return await this.geminiServiceV2.processNaturalLanguageOrder(
        customerInput,
        this.menuData,
        generatedAppPrompt,
        sessionId,
        language
      );
    } catch (error) {
      logger.warn('GeminiServiceV2 動態訂單處理失敗，降級到傳統引擎', {
        error: error instanceof Error ? error.message : String(error)
      });
      return await this.fallbackEngine.processOrderWithDynamicPrompt(
        customerInput,
        generatedAppPrompt,
        language,
        sessionId
      );
    }
  }

  /**
   * 健康檢查
   */
  async healthCheck(): Promise<{ v2Available: boolean; fallbackAvailable: boolean }> {
    let v2Available = false;
    let fallbackAvailable = false;

    try {
      // 簡單測試 GeminiServiceV2
      await this.geminiServiceV2.processNaturalLanguageOrder('健康檢查', null, undefined, 'health-check');
      v2Available = true;
    } catch (error) {
      logger.debug('GeminiServiceV2 健康檢查失敗', {
        error: error instanceof Error ? error.message : String(error)
      });
    }

    try {
      // 測試傳統引擎
      this.fallbackEngine.validateBDDSyntax('Given 健康檢查');
      fallbackAvailable = true;
    } catch (error) {
      logger.debug('傳統 PromptEngine 健康檢查失敗', {
        error: error instanceof Error ? error.message : String(error)
      });
    }

    logger.info('PromptEngine 適配器健康檢查完成', {
      v2Available,
      fallbackAvailable
    });

    return { v2Available, fallbackAvailable };
  }
}
