/* 語言切換選單樣式 */
.language-switcher {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.language-btn {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px;
}

.language-btn:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.language-btn .globe-icon {
    margin-right: 5px;
    font-size: 16px;
}

.language-dropdown {
    position: absolute;
    top: 40px;
    right: 0;
    background-color: #333;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: none;
    min-width: 120px;
    overflow: hidden;
}

.language-dropdown.show {
    display: block;
}

.language-option {
    color: white;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    text-align: center;
}

.language-option:hover {
    background-color: #444;
}

.language-option:not(:last-child) {
    border-bottom: 1px solid #444;
}