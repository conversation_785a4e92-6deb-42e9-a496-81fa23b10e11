import { Router } from 'express';
import { RequestHandler } from '../types/express-route-types.js';
import { createLogger } from '../utils/Logger.js';
import { ServiceManager } from '../services/ServiceManager.js';

const logger = createLogger('OrderRoute');

// 智能服務選擇：使用ServiceManager自動選擇合適的服務
async function getActiveOrderService() {
  try {
    // 創建並初始化ServiceManager實例
    const serviceManager = new ServiceManager({});
    await serviceManager.initialize();
    return await serviceManager.getOrderService();
  } catch (error) {
    logger.error('獲取訂單服務失敗');
    // 作為最後的備用方案，直接導入MockOrderService
    const { default: MockOrderService } = await import('../services/MockOrderService.js');
    return MockOrderService;
  }
}

const router = Router();

// 創建訂單
router.post('/create', (async (req, res): Promise<void> => {
  try {
    const { items, userId } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      res.status(400).json({ 
        success: false,
        message: '訂單必須包含至少一項商品' 
      });
      return;
    }
    
    // 記錄收到的請求數據
    logger.info('接收到訂單創建請求', { 
      itemCount: items.length,
      hasUserId: !!userId,
      itemDetails: items.map(item => ({ 
        name: item.name || item.name_en || item.name_jp || item.name_zh, 
        quantity: item.quantity 
      }))
    });
      // 確保所有項目都有名稱、數量和價格（支援多語言名稱欄位）
    const validItems = items.filter(item => {
      const hasValidName = !!(item.name || item.name_en || item.name_jp || item.name_zh);
      return item && hasValidName && 
        typeof item.quantity === 'number' && item.quantity > 0 &&
        typeof item.price === 'number' && item.price >= 0;
    });
    
    if (validItems.length === 0) {
      res.status(400).json({ 
        success: false,
        message: '訂單中沒有有效的商品項目' 
      });
      return;
    }      // 嚴格驗證 userId，只有當它是非空有效字串時才傳遞
    let validUserId: string | undefined = undefined;
    if (userId && typeof userId === 'string' && userId.trim() !== '') {
      validUserId = userId.trim();  // 確保移除前後空格
      logger.info('有效的 userId', { validUserId });
    } else {
      logger.debug('無效的 userId，將使用 undefined', { userId });
    }
      // 使用ServiceManager自動選擇合適的服務
    const orderService = await getActiveOrderService();
    const order = await orderService.createOrder(validItems, validUserId);
    
    logger.info('訂單創建成功', {
      id: order.id,
      itemCount: order.items.length,
      totalAmount: order.totalAmount
    });
    
    res.status(201).json({
      success: true,
      message: '訂單已成功創建',
      data: order
    });
  } catch (error) {
    logger.error('創建訂單失敗', error instanceof Error ? error : new Error(String(error)));
    // 提供更詳細的錯誤信息
    const errorMessage = error instanceof Error ? error.message : '未知錯誤';
    res.status(500).json({ 
      success: false,
      message: '創建訂單時發生錯誤',
      error: errorMessage 
    });
  }
}) as RequestHandler);

// 確認訂單
router.put('/:id/confirm', (async (req, res): Promise<void> => {
  try {
    const { id } = req.params;
    
    const orderService = await getActiveOrderService();
    const order = await orderService.confirmOrder(id);
    
    logger.info('訂單確認成功', { orderId: id });
    res.status(200).json(order);
  } catch (error) {
    logger.error('確認訂單失敗', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({ error: '確認訂單時發生錯誤' });
  }
}) as RequestHandler);

// 獲取訂單資訊
router.get('/:id', (async (req, res): Promise<void> => {
  try {
    const { id } = req.params;
    
    const orderService = await getActiveOrderService();
    const order = await orderService.getOrder(id);
    
    if (!order) {
      res.status(404).json({ error: '找不到該訂單' });
      return;
    }
    
    logger.info('獲取訂單資訊成功', { orderId: id });
    res.status(200).json(order);
  } catch (error) {
    logger.error('獲取訂單資訊失敗', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({ error: '獲取訂單資訊時發生錯誤' });
  }
}) as RequestHandler);

// 更新訂單狀態
router.put('/:id/status', (async (req, res): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'].includes(status)) {
      res.status(400).json({ error: '無效的訂單狀態' });
      return;
    }
      
    const orderService = await getActiveOrderService();
    await orderService.updateOrderStatus(id, status);
    
    logger.info('訂單狀態更新成功', { orderId: id, status });
    res.status(204).send();
  } catch (error) {
    logger.error('更新訂單狀態失敗', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({ error: '更新訂單狀態時發生錯誤' });
  }
}) as RequestHandler);

export default router;
