/**
 * NaturalLanguageProcessor.ts
 * 自然語言處理服務
 * 職責：處理自然語言訂單理解和處理
 */

import { MenuData } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import { GeminiApiClient } from './GeminiApiClient.js';

/**
 * 自然語言處理器
 * 負責將自然語言輸入轉換為結構化的訂單數據
 */
export class NaturalLanguageProcessor {
  private logger = createLogger('NaturalLanguageProcessor');
  private geminiClient: GeminiApiClient;

  constructor(geminiClient?: GeminiApiClient) {
    this.geminiClient = geminiClient || new GeminiApiClient();
    this.logger.info('NaturalLanguageProcessor 已初始化');
  }

  /**
   * 處理自然語言訂單
   * @param input 自然語言輸入
   * @param menuData 菜單數據
   * @param appPrompt 應用提示詞
   * @param sessionId 會話 ID
   * @param language 語言設定
   * @returns 處理後的訂單 JSON 字符串
   */
  async processNaturalLanguageOrder(
    input: string, 
    menuData?: MenuData | null, 
    appPrompt?: string, 
    sessionId?: string, 
    language?: string
  ): Promise<string> {
    this.logger.info('開始處理自然語言訂單', {
      inputLength: input.length,
      hasMenuData: !!menuData,
      hasAppPrompt: !!appPrompt,
      sessionId,
      language: language || 'default'
    });

    try {
      const prompt = this.buildOrderProcessingPrompt(input, menuData, appPrompt, language);
      
      this.logger.debug('生成訂單處理提示詞', {
        promptLength: prompt.length
      });

      const response = await this.geminiClient.generateText(prompt);
      
      // 清理和驗證響應
      const cleanedResponse = this.cleanAndValidateResponse(response);
      
      this.logger.info('自然語言訂單處理成功', {
        inputLength: input.length,
        responseLength: cleanedResponse.length
      });

      return cleanedResponse;
    } catch (error) {
      this.logger.error('自然語言訂單處理失敗', error instanceof Error ? error : new Error(String(error)), {
        inputLength: input.length,
        sessionId
      });
      throw error;
    }
  }

  /**
   * 構建訂單處理提示詞
   */
  private buildOrderProcessingPrompt(
    input: string, 
    menuData?: MenuData | null, 
    appPrompt?: string, 
    language?: string
  ): string {
    const lang = this.getLanguageSettings(language);
    
    let prompt = '';

    // 添加系統角色
    prompt += `你是一個專業的點餐助手。請根據用戶的自然語言輸入，生成對應的訂單數據。\\n\\n`;

    // 添加 appPrompt（如果提供）
    if (appPrompt) {
      prompt += `系統指令：\\n${appPrompt}\\n\\n`;
    }

    // 添加菜單信息
    if (menuData) {
      prompt += `可用菜單：\\n${JSON.stringify(menuData, null, 2)}\\n\\n`;
    }

    // 添加語言設定和餐點名稱要求
    const nameField = (language === 'ja-JP' || language === 'ja') ? 'name_jp' : (language === 'en' ? 'name_en' : 'name_zh');
    prompt += `語言設定：請使用${lang.displayName}回應\n`;
    prompt += `餐點名稱要求：請使用菜單中的 "${nameField}" 欄位作為餐點名稱\n\n`;    // 添加輸出格式要求
    prompt += `輸出格式要求：
請返回有效的 JSON 格式，包含以下字段：
{
  "items": [
    {
      "${nameField}": "商品名稱",
      "quantity": 數量,
      "price": 價格,
      "image_url": "圖片網址"
    }
  ],
  "totalAmount": 總金額,
  "language": "${lang.code}",
  "confidence": 信心度(0-1),
  "notes": "額外備註"
}

🔥 重要：image_url 字段要求：
- 必須從菜單數據中的 "image_url" 或 "image" 欄位複製完整的 URL
- 如果菜單中沒有圖片 URL，則使用空字符串 ""
- 確保 URL 完整且可訪問
\\n\\n`;

    // 添加用戶輸入
    prompt += `用戶輸入：${input}\\n\\n`;

    // 添加處理指示
    prompt += `請分析用戶輸入，識別所需的商品和數量，並生成對應的訂單數據。如果有不確定的地方，請在 notes 欄位中說明。`;

    return prompt;
  }
  /**
   * 清理和驗證響應
   */
  private cleanAndValidateResponse(response: string): string {
    try {
      // 首先嘗試提取 ORDER_JSON_START 和 ORDER_JSON_END 之間的內容
      const startMarker = 'ORDER_JSON_START';
      const endMarker = 'ORDER_JSON_END';
      
      let cleaned = response;
      
      const startIndex = response.indexOf(startMarker);
      const endIndex = response.indexOf(endMarker);
      
      if (startIndex !== -1 && endIndex !== -1) {
        // 提取標記之間的內容
        const jsonStart = startIndex + startMarker.length;
        const jsonContent = response.substring(jsonStart, endIndex).trim();
        // 移除可能的 markdown 標記
        cleaned = jsonContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      } else {
        // 如果沒有找到標記，移除可能的 markdown 標記
        cleaned = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      }
      
      // 添加調試信息
      this.logger.debug('清理後的響應內容', {
        original: response,
        cleaned: cleaned,
        hasMarkers: startIndex !== -1 && endIndex !== -1
      });
      
      // 嘗試解析 JSON 以驗證格式
      const parsed = JSON.parse(cleaned);
      
      // 基本驗證
      if (!parsed.items || !Array.isArray(parsed.items)) {
        throw new Error('響應格式不正確：缺少 items 陣列');
      }      // 驗證每個項目
      for (const item of parsed.items) {
        // 檢查是否有任何有效的名稱欄位
        const hasValidName = !!(item.name || item.name_en || item.name_jp || item.name_zh);
        
        this.logger.debug('驗證項目', {
          item: item,
          hasName: !!item.name,
          hasNameEn: !!item.name_en,
          hasNameJp: !!item.name_jp,
          hasNameZh: !!item.name_zh,
          hasValidName: hasValidName,
          quantityType: typeof item.quantity,
          priceType: typeof item.price
        });
          if (!hasValidName || typeof item.quantity !== 'number' || (item.price !== null && typeof item.price !== 'number')) {
          throw new Error('響應格式不正確：項目格式無效');
        }
      }

      this.logger.debug('響應驗證通過', {
        itemCount: parsed.items.length,
        totalAmount: parsed.totalAmount
      });

      return JSON.stringify(parsed, null, 2);
    } catch (error) {
      this.logger.error('響應清理和驗證失敗', error instanceof Error ? error : new Error(String(error)), {
        responseLength: response.length
      });
      
      // 返回錯誤響應格式
      return JSON.stringify({
        items: [],
        totalAmount: 0,
        language: 'zh',
        confidence: 0,
        notes: `處理失敗: ${error instanceof Error ? error.message : String(error)}`
      }, null, 2);
    }
  }

  /**
   * 獲取語言設定
   */
  private getLanguageSettings(language?: string): { code: string; displayName: string } {
    switch (language) {
      case 'en':
        return { code: 'en', displayName: 'English' };
      case 'ja-JP':
      case 'ja':
        return { code: 'ja-JP', displayName: '日本語' };
      case 'zh':
      case 'tw':
      default:
        return { code: 'zh', displayName: '繁體中文' };
    }
  }

  /**
   * 批量處理多個自然語言輸入
   * @param inputs 輸入陣列
   * @param menuData 菜單數據
   * @param appPrompt 應用提示詞
   * @param sessionId 會話 ID
   * @param language 語言設定
   * @returns 處理結果陣列
   */
  async processBatchNaturalLanguageOrders(
    inputs: string[], 
    menuData?: MenuData | null, 
    appPrompt?: string, 
    sessionId?: string, 
    language?: string
  ): Promise<string[]> {
    this.logger.info('開始批量處理自然語言訂單', {
      batchSize: inputs.length,
      sessionId
    });

    const results: string[] = [];
    
    for (let i = 0; i < inputs.length; i++) {
      try {
        const result = await this.processNaturalLanguageOrder(
          inputs[i], 
          menuData, 
          appPrompt, 
          `${sessionId}-batch-${i}`, 
          language
        );
        results.push(result);
      } catch (error) {
        this.logger.error(`批量處理第 ${i + 1} 項失敗`, error instanceof Error ? error : new Error(String(error)));
        
        // 添加錯誤響應
        results.push(JSON.stringify({
          items: [],
          totalAmount: 0,
          language: language || 'zh',
          confidence: 0,
          notes: `處理失敗: ${error instanceof Error ? error.message : String(error)}`
        }, null, 2));
      }
    }

    this.logger.info('批量處理完成', {
      batchSize: inputs.length,
      successCount: results.length
    });

    return results;
  }
}

export default new NaturalLanguageProcessor();
