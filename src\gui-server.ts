import express from 'express';
import cors from 'cors';
import multer from 'multer';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';
import fs from 'node:fs';
import { MenuProcessor } from './services/MenuProcessor.js';
import { FirebaseService as FirebaseServiceClass } from './services/FirebaseService.js';

let FirebaseService: FirebaseServiceClass | null = null;
import nlpRoutes from './routes/nlp.js';
import { updateLoadedMenu } from './routes/nlp.js';
import promptRoutes from './routes/prompt.js';
// import { PromptEngine } from './services/PromptEngine.js'; // 未使用
import orderRoutes from './routes/order.js';
import filesRoutes from './routes/files.js';
import { jsonParserFix } from './middleware/json-parser-fix.js';

// 載入環境變數
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 確保 uploads 目錄存在
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  try {
    fs.mkdirSync(uploadsDir, { recursive: true, mode: 0o755 });
    console.log('已成功創建 uploads 目錄');
  } catch (error) {
    console.error('創建 uploads 目錄失敗:', error);
    process.exit(1);
  }
}

// 確保 appPrompt 目錄存在
const appPromptDir = path.join(__dirname, '../appPrompt');
if (!fs.existsSync(appPromptDir)) {
  try {
    fs.mkdirSync(appPromptDir, { recursive: true, mode: 0o755 });
    console.log('已成功創建 appPrompt 目錄');
  } catch (error) {
    console.error('創建 appPrompt 目錄失敗:', error);
    process.exit(1);
  }
}

const app = express();
// GUI 測試環境使用環境變數 PORT 或預設 4003 端口
const port = process.env.PORT || 4003;
const menuProcessor = new MenuProcessor();
// const promptEngine = new PromptEngine();

// 初始化 Firebase (如果配置了環境變數)
// let firebaseService: FirebaseService | null = null;

if (process.env.FIREBASE_API_KEY && process.env.FIREBASE_PROJECT_ID) {
  try {
    FirebaseService = new FirebaseServiceClass({
      apiKey: process.env.FIREBASE_API_KEY,
      authDomain: `${process.env.FIREBASE_PROJECT_ID}.firebaseapp.com`,
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET || `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
      appId: process.env.FIREBASE_APP_ID || ''
    });
    
    console.log('已成功初始化 Firebase');
  } catch (error) {
    console.error('Firebase 初始化失敗:', error);
  }
}

// 中間件
app.use(cors());
app.use(express.json());
app.use(jsonParserFix);

// 配置文件上傳
const storage = multer.diskStorage({
  destination: function(_req, _file, cb) {
    cb(null, path.join(__dirname, '../uploads/'));
  },
  filename: function(_req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function(_req, file, cb) {
    const filetypes = /csv|xlsx|json/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只支援 CSV, Excel 和 JSON 格式的檔案'));
    }
  }
});

// GUI 主頁路由 - 提供 index.html (必須在靜態文件中間件之前)
app.get('/', (_req, res) => {
  console.log('根路由被訪問，返回 index.html');
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 靜態文件服務
app.use(express.static(path.join(__dirname, '../public')));
// 提供 appPrompt 目錄的靜態檔案服務
app.use('/appPrompt', express.static(path.join(__dirname, '../appPrompt')));

// API 路由
app.use('/api/nlp', nlpRoutes);
app.use('/api/prompt', promptRoutes);
app.use('/api/order', orderRoutes);
app.use('/api/files', filesRoutes);

// 菜單上傳路由
app.post('/upload-menu', upload.single('menu'), async (req, res): Promise<void> => {
  try {
    if (!req.file) {
      res.status(400).json({ 
        success: false, 
        error: '沒有上傳文件' 
      });
      return;
    }

    console.log('收到菜單上傳請求:', req.file.originalname);
    
    const result = await menuProcessor.processMenuFile(req.file.path, req.file.originalname);
    
    if (result.success) {
      // 同步更新 NLP 服務的菜單
      await updateLoadedMenu(result.data?.restaurant_id || 'default', result.data);
      
      res.json({
        success: true,
        message: '菜單上傳成功',
        data: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        message: '菜單處理失敗',
        errors: result.errors || []
      });
    }
  } catch (error) {
    console.error('菜單上傳錯誤:', error);
    res.status(500).json({
      success: false,
      message: '伺服器錯誤: ' + (error as Error).message
    });
  }
});

// 獲取當前菜單
app.get('/api/menu', async (_req, res): Promise<void> => {
  try {
    // 返回空菜單，實際應該從數據庫或文件系統獲取
    res.json({
      success: true,
      data: {
        restaurant_id: 'default',
        restaurant_name: '預設餐廳',
        categories: [],
        last_updated: new Date(),
        version: '1.0.0'
      }
    });
  } catch (error) {
    console.error('獲取菜單錯誤:', error);
    res.status(500).json({
      success: false,
      message: '獲取菜單失敗'
    });
  }
});

// 健康檢查
app.get('/health', (_req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    port: port,
    environment: 'GUI Test Environment'
  });
});

// 錯誤處理中間件
app.use((error: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('伺服器錯誤:', error);
  res.status(500).json({
    success: false,
    message: '伺服器內部錯誤'
  });
});

// 404 處理
app.use((_req, res) => {
  res.status(404).json({
    success: false,
    message: '找不到請求的資源'
  });
});

// 啟動伺服器
app.listen(port, async () => {
  console.log(`\n🚀 GUI 測試伺服器已啟動`);
  console.log(`📍 本地地址: http://localhost:${port}`);
  console.log(`🎨 GUI 介面: 使用 index.html`);
  console.log(`🔧 環境: GUI 測試環境`);
  console.log(`📁 靜態文件目錄: ${path.join(__dirname, '../public')}`);
  console.log(`📤 上傳目錄: ${uploadsDir}`);
  console.log(`📝 提示詞目錄: ${appPromptDir}`);
  
  // 自動載入最新的 appPrompt 檔案
  try {
    const appPromptFiles = fs.readdirSync(appPromptDir)
      .filter(file => file.startsWith('appPrompt_') && file.endsWith('.json'))
      .sort()
      .reverse(); // 按時間排序，最新的在前

    if (appPromptFiles.length > 0) {
      const latestAppPrompt = appPromptFiles[0];
      const appPromptPath = path.join(appPromptDir, latestAppPrompt);
      
      console.log(`📋 正在載入 appPrompt: ${latestAppPrompt}`);
      
      const appPromptContent = JSON.parse(fs.readFileSync(appPromptPath, 'utf-8'));
        // 如果 appPrompt 包含菜單數據，同步到 NLP 服務
      if (appPromptContent.parameters && appPromptContent.parameters.menu) {
        const menuCategories = appPromptContent.parameters.menu;
        console.log(`📊 發現菜單數據，包含 ${menuCategories.length} 個類別`);
        
        // 轉換菜單格式來匹配 updateLoadedMenu 期望的格式
        const formattedMenuData = {
          categories: menuCategories
        };
        
        updateLoadedMenu('appPrompt-auto-loaded', formattedMenuData);
        console.log(`✅ 已自動載入菜單數據到 NLP 服務`);
      }
    } else {
      console.log(`⚠️  未找到 appPrompt 檔案`);
    }
  } catch (error) {
    console.error(`❌ 載入 appPrompt 失敗:`, error);
  }
  
  console.log('\n準備接受請求...');
});

export default app;