<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>語音識別測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.info { background-color: #e3f2fd; color: #1976d2; }
        .status.success { background-color: #e8f5e8; color: #2e7d32; }
        .status.error { background-color: #ffebee; color: #c62828; }
        .status.warning { background-color: #fff3e0; color: #f57c00; }
        
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        
        .button:hover {
            background-color: #45a049;
        }
        
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .button.recording {
            background-color: #f44336;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .result-box {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            min-height: 100px;
        }
        
        .interim {
            color: #666;
            font-style: italic;
        }
        
        .final {
            color: #000;
            font-weight: bold;
        }
        
        .debug-info {
            background-color: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 語音識別測試頁面</h1>
        
        <div id="status" class="status info">
            正在初始化語音管理器...
        </div>
        
        <div>
            <button id="startBtn" class="button">開始語音識別</button>
            <button id="stopBtn" class="button" disabled>停止語音識別</button>
            <button id="testBtn" class="button">測試環境檢測</button>
            <button id="diagnosisBtn" class="button">完整診斷</button>
        </div>
        
        <div>
            <h3>識別結果：</h3>
            <div id="resultBox" class="result-box">
                <div id="interimResult" class="interim"></div>
                <div id="finalResult" class="final"></div>
            </div>
        </div>
        
        <div>
            <h3>調試信息：</h3>
            <div id="debugInfo" class="debug-info"></div>
        </div>
    </div>

    <!-- 載入語音管理器 -->
    <script src="js/android-speech-detector.js?v=1"></script>
    <script src="js/speech-manager.js?v=2"></script>
    
    <script>
        let isRecording = false;
        
        // DOM 元素
        const statusDiv = document.getElementById('status');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const testBtn = document.getElementById('testBtn');
        const diagnosisBtn = document.getElementById('diagnosisBtn');
        const interimResult = document.getElementById('interimResult');
        const finalResult = document.getElementById('finalResult');
        const debugInfo = document.getElementById('debugInfo');
        
        // 日誌函數
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            debugInfo.textContent += logEntry;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            
            console.log(message);
        }
        
        // 更新狀態顯示
        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            log(`狀態: ${message}`, type);
        }
        
        // 更新按鈕狀態
        function updateButtons() {
            startBtn.disabled = isRecording;
            stopBtn.disabled = !isRecording;
            
            if (isRecording) {
                startBtn.classList.add('recording');
            } else {
                startBtn.classList.remove('recording');
            }
        }
        
        // 等待語音管理器初始化
        function waitForSpeechManager() {
            return new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    if (window.speechManager) {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
            });
        }
        
        // 初始化
        async function init() {
            try {
                updateStatus('等待語音管理器初始化...', 'info');
                await waitForSpeechManager();
                
                updateStatus('語音管理器初始化完成', 'success');
                log('語音管理器已準備就緒');
                
                // 設置回調函數
                window.speechManager.setCallbacks({
                    onResult: (transcript, isFinal) => {
                        log(`語音結果: "${transcript}" (最終: ${isFinal})`);
                        
                        if (isFinal) {
                            finalResult.textContent = transcript;
                            interimResult.textContent = '';
                            updateStatus(`識別完成: ${transcript}`, 'success');
                        } else {
                            interimResult.textContent = transcript;
                        }
                    },
                    
                    onError: (error) => {
                        log(`語音錯誤: ${error.message}`, 'error');
                        updateStatus(`錯誤: ${error.message}`, 'error');
                        isRecording = false;
                        updateButtons();
                    },
                    
                    onStart: () => {
                        log('語音識別開始');
                        isRecording = true;
                        updateButtons();
                        updateStatus('正在聆聽...', 'info');
                    },
                    
                    onEnd: () => {
                        log('語音識別結束');
                        isRecording = false;
                        updateButtons();
                        updateStatus('語音識別已停止', 'info');
                    },
                    
                    onStatusUpdate: (status) => {
                        log(`狀態更新: ${status}`);
                        switch(status) {
                            case 'listening':
                                updateStatus('正在聆聽...', 'info');
                                break;
                            case 'stopped':
                                updateStatus('已停止', 'info');
                                break;
                            case 'fallback':
                                updateStatus('已切換到備用模式', 'warning');
                                break;
                        }
                    }
                });
                
                // 顯示環境信息
                const status = window.speechManager.getStatus();
                log(`環境狀態: ${JSON.stringify(status, null, 2)}`);
                
            } catch (error) {
                log(`初始化失敗: ${error.message}`, 'error');
                updateStatus(`初始化失敗: ${error.message}`, 'error');
            }
        }
        
        // 事件處理
        startBtn.addEventListener('click', async () => {
            if (!window.speechManager) {
                updateStatus('語音管理器未初始化', 'error');
                return;
            }
            
            log('用戶點擊開始按鈕');
            updateStatus('正在啟動語音識別...', 'info');
            
            const success = await window.speechManager.start();
            if (!success) {
                updateStatus('啟動失敗', 'error');
                log('語音識別啟動失敗');
            }
        });
        
        stopBtn.addEventListener('click', () => {
            if (!window.speechManager) {
                updateStatus('語音管理器未初始化', 'error');
                return;
            }
            
            log('用戶點擊停止按鈕');
            window.speechManager.stop();
        });
        
        testBtn.addEventListener('click', () => {
            if (!window.speechManager) {
                updateStatus('語音管理器未初始化', 'error');
                return;
            }

            const status = window.speechManager.getStatus();
            const environment = window.speechManager.detectEnvironment();

            log('=== 環境檢測結果 ===');
            log(`語音管理器狀態: ${JSON.stringify(status, null, 2)}`);
            log(`環境信息: ${JSON.stringify(environment, null, 2)}`);
            log(`瀏覽器: ${navigator.userAgent}`);
            log(`Capacitor: ${typeof window.Capacitor !== 'undefined'}`);
            log(`Web Speech API: ${('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window)}`);

            updateStatus('環境檢測完成，請查看調試信息', 'success');
        });

        diagnosisBtn.addEventListener('click', async () => {
            updateStatus('正在進行完整診斷...', 'info');
            log('=== 開始完整診斷 ===');

            try {
                if (window.androidSpeechDetector) {
                    const capabilities = await window.androidSpeechDetector.detectCapabilities();
                    const report = window.androidSpeechDetector.generateDiagnosticReport(capabilities);

                    log('=== 診斷報告 ===');
                    log(report);

                    // 根據診斷結果更新狀態
                    const hasAnySupport = capabilities.speechRecognition.webSpeechAPI ||
                                         capabilities.speechRecognition.capacitorPlugin;

                    if (hasAnySupport && capabilities.microphone.available) {
                        updateStatus('設備支援語音識別功能', 'success');
                    } else if (!capabilities.microphone.available) {
                        updateStatus('設備無麥克風，無法使用語音功能', 'error');
                    } else {
                        updateStatus('設備不支援語音識別，建議使用文字輸入', 'warning');
                    }
                } else {
                    log('Android 語音檢測器未初始化');
                    updateStatus('診斷工具未初始化', 'error');
                }
            } catch (error) {
                log(`診斷失敗: ${error.message}`);
                updateStatus(`診斷失敗: ${error.message}`, 'error');
            }
        });
        
        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
