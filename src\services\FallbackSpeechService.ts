/**
 * 備用語音服務
 * 為不支援語音識別的環境提供替代方案
 * 包括文字輸入、預設語音指令等功能
 */
import { createLogger } from '../utils/Logger.js';

const logger = createLogger('FallbackSpeechService');

// 語音識別結果類型（與 SpeechRecognitionService 保持一致）
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

// 語音識別選項
export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
}

// 預設語音指令
interface VoiceCommand {
  text: string;
  description: string;
  category: 'order' | 'navigation' | 'control';
}

/**
 * 備用語音服務類
 * 提供文字輸入和預設指令作為語音識別的替代方案
 */
export class FallbackSpeechService {
  private isListening: boolean = false;
  private currentOptions: SpeechRecognitionOptions = {};
  private onResultCallback?: (result: SpeechRecognitionResult) => void;
  private onErrorCallback?: (error: Error) => void;
  
  // 預設語音指令庫
  private voiceCommands: VoiceCommand[] = [
    // 點餐相關指令
    { text: '我要點餐', description: '開始點餐', category: 'order' },
    { text: '我要一份牛肉麵', description: '點牛肉麵', category: 'order' },
    { text: '我要一杯珍珠奶茶', description: '點珍珠奶茶', category: 'order' },
    { text: '我要兩份炒飯', description: '點兩份炒飯', category: 'order' },
    { text: '加辣', description: '加辣調味', category: 'order' },
    { text: '不要辣', description: '不加辣', category: 'order' },
    { text: '確認訂單', description: '確認當前訂單', category: 'order' },
    { text: '取消訂單', description: '取消當前訂單', category: 'order' },
    
    // 導航相關指令
    { text: '回到首頁', description: '返回主頁面', category: 'navigation' },
    { text: '查看菜單', description: '顯示完整菜單', category: 'navigation' },
    { text: '查看訂單', description: '查看當前訂單', category: 'navigation' },
    
    // 控制相關指令
    { text: '重新開始', description: '重新開始點餐', category: 'control' },
    { text: '幫助', description: '顯示幫助信息', category: 'control' },
    { text: '停止', description: '停止當前操作', category: 'control' }
  ];
  
  constructor() {
    logger.info('FallbackSpeechService 初始化完成');
  }
  
  /**
   * 開始語音識別（備用模式）
   * 提供文字輸入界面作為替代
   */
  startListening(
    onResult: (result: SpeechRecognitionResult) => void,
    onError: (error: Error) => void,
    options: SpeechRecognitionOptions = {}
  ): boolean {
    if (this.isListening) {
      logger.warn('備用語音服務已在運行中');
      return false;
    }
    
    this.currentOptions = options;
    this.onResultCallback = onResult;
    this.onErrorCallback = onError;
    this.isListening = true;
    
    logger.info('啟動備用語音服務 - 文字輸入模式');
    
    // 模擬語音識別開始
    this.showTextInputInterface();
    
    return true;
  }
  
  /**
   * 停止語音識別
   */
  stopListening(): void {
    if (this.isListening) {
      this.isListening = false;
      this.hideTextInputInterface();
      logger.info('備用語音服務已停止');
    }
  }
  
  /**
   * 檢查是否支援（備用服務總是可用）
   */
  isSupported(): boolean {
    return true;
  }
  
  /**
   * 獲取當前是否正在進行語音識別
   */
  isCurrentlyListening(): boolean {
    return this.isListening;
  }
  
  /**
   * 獲取預設語音指令
   */
  getVoiceCommands(category?: string): VoiceCommand[] {
    if (category) {
      return this.voiceCommands.filter(cmd => cmd.category === category);
    }
    return this.voiceCommands;
  }
  
  /**
   * 添加自定義語音指令
   */
  addVoiceCommand(command: VoiceCommand): void {
    this.voiceCommands.push(command);
    logger.info(`添加自定義語音指令: ${command.text}`);
  }
  
  /**
   * 處理文字輸入
   */
  processTextInput(text: string): void {
    if (!this.isListening || !this.onResultCallback) {
      return;
    }
    
    // 清理和處理輸入文字
    const cleanedText = this.cleanTextInput(text);
    
    if (cleanedText.trim()) {
      // 創建語音識別結果
      const result: SpeechRecognitionResult = {
        transcript: cleanedText,
        confidence: 0.95, // 文字輸入給予高信心度
        isFinal: true
      };
      
      logger.info(`處理文字輸入: ${cleanedText}`);
      this.onResultCallback(result);
    }
  }
  
  /**
   * 處理預設指令選擇
   */
  processCommandSelection(command: VoiceCommand): void {
    if (!this.isListening || !this.onResultCallback) {
      return;
    }
    
    const result: SpeechRecognitionResult = {
      transcript: command.text,
      confidence: 1.0, // 預設指令給予最高信心度
      isFinal: true
    };
    
    logger.info(`處理預設指令: ${command.text}`);
    this.onResultCallback(result);
  }
  
  /**
   * 顯示文字輸入界面
   */
  private showTextInputInterface(): void {
    // 這裡可以觸發前端顯示文字輸入界面
    // 實際實現會在 UI 層面處理
    logger.info('顯示文字輸入界面');
    
    // 發送界面顯示事件
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      const event = new CustomEvent('fallback-speech-show', {
        detail: {
          commands: this.voiceCommands,
          options: this.currentOptions
        }
      });
      window.dispatchEvent(event);
    }
  }
  
  /**
   * 隱藏文字輸入界面
   */
  private hideTextInputInterface(): void {
    logger.info('隱藏文字輸入界面');
    
    // 發送界面隱藏事件
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      const event = new CustomEvent('fallback-speech-hide');
      window.dispatchEvent(event);
    }
  }
  
  /**
   * 清理文字輸入
   */
  private cleanTextInput(text: string): string {
    if (!text) return '';
    
    // 移除多餘空白
    let cleaned = text.trim().replace(/\s+/g, ' ');
    
    // 移除特殊字符（保留中文、英文、數字和基本標點）
    cleaned = cleaned.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？、]/g, '');
    
    return cleaned;
  }
  
  /**
   * 模擬語音識別錯誤
   */
  simulateError(message: string): void {
    if (this.onErrorCallback) {
      this.onErrorCallback(new Error(`備用語音服務錯誤: ${message}`));
    }
  }
  
  /**
   * 獲取服務狀態信息
   */
  getServiceInfo(): object {
    return {
      type: 'fallback',
      isListening: this.isListening,
      commandCount: this.voiceCommands.length,
      currentOptions: this.currentOptions,
      supported: true
    };
  }
}

// 導出單例實例
export const fallbackSpeechService = new FallbackSpeechService();