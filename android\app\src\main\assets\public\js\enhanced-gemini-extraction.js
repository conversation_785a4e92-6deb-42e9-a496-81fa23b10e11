/**
 * 改進版 Gemini 回應提取工具
 * 專注於提高對各種 Gemini 回應格式的相容性
 */

// 確保在全局範圍可用
console.log('增強型 Gemini 提取工具載入中...');

// 在全局範圍保存當前的 Gemini 回應數據
window.enhancedGeminiExtraction = function(response) {
    console.group('增強型 Gemini 回應提取');
    try {
        if (!response) {
            console.warn('沒有提供 Gemini 回應數據');
            console.groupEnd();
            return null;
        }
        
        console.log('原始回應:', response);
        
        // 分割回應行
        const lines = response.split('\n');
        console.log(`共 ${lines.length} 行`);
        
        // 階段 1: 提取菜單項目
        const menuItemLines = [];
        const menuItemResults = [];
        
        // 價格模式: 匹配 NT$ 或 ¥ 後跟數字，可能有逗號
        const pricePattern = /(?:NT\$|¥)\s*([\d,]+)/i;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 跳過空行
            if (!line) continue;
            
            // 跳過總計行
            if (line.match(/總計|總共|總金額|總額|共計|合計|total/i) && (line.includes('NT$') || line.includes('¥'))) {
                console.log(`行 ${i+1} 識別為總計行，跳過: ${line}`);
                continue;
            }
            
            // 檢查此行是否含有價格
            const priceMatch = line.match(pricePattern);
            
            if (priceMatch) {
                // 基本情況: 同一行包含項目名稱和價格
                menuItemLines.push(line);
                
                const price = parseFloat(priceMatch[1].replace(/,/g, ''));
                const namePart = line.split(/(?:NT\$|¥)/i)[0].trim();
                
                // 移除可能的項目符號或數字編號
                const cleanName = namePart.replace(/^[*\-•#\d.]+\s*/, '');
                
                menuItemResults.push({
                    fullText: line,
                    name: cleanName,
                    price: price
                });
                
                console.log(`行 ${i+1} 識別為菜單項目: ${line} -> ${cleanName} / ${price}`);
            } 
            // 檢查多行項目格式，例如:
            // - 項目名稱
            //   價格：NT$xxx
            else if (line.match(/^[*\-•#\d.]+\s+.+/) && i < lines.length - 1) {
                const nextLine = lines[i+1].trim();
                if (nextLine.includes('NT$') || nextLine.includes('¥')) {
                    const combinedLine = `${line} ${nextLine}`;
                    menuItemLines.push(combinedLine);
                    
                    const nextLineMatch = nextLine.match(pricePattern);
                    if (nextLineMatch) {
                        const price = parseFloat(nextLineMatch[1].replace(/,/g, ''));
                        let namePart = line.trim();
                        
                        // 移除可能的項目符號或數字編號
                        const cleanName = namePart.replace(/^[*\-•#\d.]+\s*/, '');
                        
                        menuItemResults.push({
                            fullText: combinedLine,
                            name: cleanName,
                            price: price
                        });
                        
                        console.log(`行 ${i+1}和${i+2} 識別為多行菜單項目: ${combinedLine} -> ${cleanName} / ${price}`);
                    }
                }
            }
        }
        
        // 階段 2: 提取總金額（支援 NT$ 和 ¥）
        const totalPatterns = [
            /總共是\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總計\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總金額\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總額\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /共\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總共\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /共計\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /共計：\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總計：\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總金額：\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /總額：\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /total\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /total:\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /合計\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /合計：\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /合計で.*=\s*(?:NT\$|¥)\s*([\d,]+)/i,
            /合計で.*¥\s*([\d,]+)\s*です/i
        ];
        
        let totalAmount = 0;
        let totalFound = false;
        
        for (const pattern of totalPatterns) {
            const match = response.match(pattern);
            if (match && match[1]) {
                totalAmount = parseFloat(match[1].replace(/,/g, ''));
                totalFound = true;
                // 根據當前語言設定決定貨幣符號
                function getCurrencySymbolByLanguage() {
                    const currentLanguage = getCurrentLanguage();
                    switch (currentLanguage) {
                        case 'ja-JP':
                            return 'NT$';  // 日文介面也使用 NT$ 統一貨幣顯示
                        case 'en-US':
                            return 'NT$';  // 英文介面也使用 NT$ 避免與美元混淆
                        case 'zh-TW':
                        default:
                            return 'NT$';
                    }
                }

                const currencySymbol = getCurrencySymbolByLanguage();
                console.log(`找到總金額: ${currencySymbol}${totalAmount}`);
                break;
            }
        }
        
        // 如果沒有找到總金額，嘗試從菜單項目計算
        if (!totalFound && menuItemResults.length > 0) {
            totalAmount = menuItemResults.reduce((sum, item) => sum + item.price, 0);
            totalFound = true;
            // 根據當前語言設定決定貨幣符號
            const currencySymbol = getCurrencySymbolByLanguage();
            console.log(`根據菜單項目計算總金額: ${currencySymbol}${totalAmount}`);
        }
        
        const result = {
            menuItems: menuItemResults,
            menuItemLines: menuItemLines,
            totalAmount: totalAmount,
            totalFound: totalFound,
            success: menuItemResults.length > 0
        };
        
        console.log('提取結果:', result);
        console.groupEnd();
        return result;
    } catch (error) {
        console.error('增強型提取過程出錯:', error);
        console.groupEnd();
        return null;
    }
};

// 將結果轉換為 HTML 列表
window.geminiExtractToHtml = function(extractionResult) {
    if (!extractionResult || !extractionResult.menuItems || extractionResult.menuItems.length === 0) {
        return '';
    }
    
    return `<ul style="list-style-type: disc; padding-left: 20px; margin: 0; display: block;">
        ${extractionResult.menuItems.map(item => {
            return `<li style="display: list-item; margin: 5px 0;">${item.fullText}</li>`;
        }).join('')}
    </ul>`;
};

// 將檢測函數添加到全局範圍
window.testEnhancedExtraction = function() {
    console.log('測試增強型提取工具');
    const testResponse = 
    `好的，您點了以下餐點：
    * 培根生菜番茄烤雞堡套餐 NT$187
    * 培根生菜番茄烤雞堡套餐 NT$187
    * 安格斯蘑菇牛肉堡套餐 NT$197
    總共是 NT$571。`;
    
    const result = window.enhancedGeminiExtraction(testResponse);
    console.log('測試提取結果:', result);
    return result;
};

// 在頁面載入時註冊
console.log('增強型 Gemini 提取工具已載入');

// 確保已經載入完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('檢查增強型提取工具是否可用:', typeof window.enhancedGeminiExtraction === 'function' ? '可用' : '不可用');
    
    // 創建一個隱藏的按鈕用於測試
    const testButton = document.createElement('button');
    testButton.id = 'test-extraction-button';
    testButton.style.display = 'none';
    testButton.textContent = getTranslation('test_extraction_tool');
    testButton.onclick = window.testEnhancedExtraction;
    document.body.appendChild(testButton);
});
