/**
 * 統一的錯誤代碼枚舉
 * 擴展了 ErrorHandler.ts 中的錯誤代碼
 */

export enum ErrorCode {
  // 通用錯誤 (1000-1999)
  UNKNOWN_ERROR = 'ERR_1000_UNKNOWN',
  VALIDATION_ERROR = 'ERR_1001_VALIDATION',
  AUTHENTICATION_ERROR = 'ERR_1002_AUTHENTICATION',
  AUTHORIZATION_ERROR = 'ERR_1003_AUTHORIZATION',
  NOT_FOUND_ERROR = 'ERR_1004_NOT_FOUND',
  CONFLICT_ERROR = 'ERR_1005_CONFLICT',
  RATE_LIMIT_ERROR = 'ERR_1006_RATE_LIMIT',

  // 業務邏輯錯誤 (2000-2999)
  MENU_NOT_FOUND = 'ERR_2001_MENU_NOT_FOUND',
  MENU_INVALID = 'ERR_2002_MENU_INVALID',
  MENU_ITEM_NOT_FOUND = 'ERR_2003_MENU_ITEM_NOT_FOUND',
  INVALID_ORDER = 'ERR_2004_INVALID_ORDER',
  ORDER_NOT_FOUND = 'ERR_2005_ORDER_NOT_FOUND',
  ORDER_ALREADY_CONFIRMED = 'ERR_2006_ORDER_ALREADY_CONFIRMED',
  PROCESSING_FAILED = 'ERR_2007_PROCESSING_FAILED',
  INVALID_QUANTITY = 'ERR_2008_INVALID_QUANTITY',
  INSUFFICIENT_STOCK = 'ERR_2009_INSUFFICIENT_STOCK',

  // NLP 相關錯誤 (3000-3999)
  NLP_PROCESSING_FAILED = 'ERR_3001_NLP_PROCESSING_FAILED',
  PROMPT_NOT_FOUND = 'ERR_3002_PROMPT_NOT_FOUND',
  PROMPT_INVALID = 'ERR_3003_PROMPT_INVALID',
  LANGUAGE_NOT_SUPPORTED = 'ERR_3004_LANGUAGE_NOT_SUPPORTED',
  INPUT_TOO_LONG = 'ERR_3005_INPUT_TOO_LONG',
  INPUT_INVALID = 'ERR_3006_INPUT_INVALID',

  // 會話相關錯誤 (4000-4999)
  SESSION_NOT_FOUND = 'ERR_4001_SESSION_NOT_FOUND',
  SESSION_EXPIRED = 'ERR_4002_SESSION_EXPIRED',
  SESSION_INVALID = 'ERR_4003_SESSION_INVALID',
  SESSION_CONFLICT = 'ERR_4004_SESSION_CONFLICT',

  // 外部服務錯誤 (5000-5999)
  GEMINI_API_ERROR = 'ERR_5001_GEMINI_API',
  GEMINI_RATE_LIMIT = 'ERR_5002_GEMINI_RATE_LIMIT',
  GEMINI_QUOTA_EXCEEDED = 'ERR_5003_GEMINI_QUOTA_EXCEEDED',
  FIREBASE_ERROR = 'ERR_5004_FIREBASE',
  FIREBASE_PERMISSION_DENIED = 'ERR_5005_FIREBASE_PERMISSION_DENIED',
  SPEECH_API_ERROR = 'ERR_5006_SPEECH_API',

  // 系統錯誤 (6000-6999)
  CONFIG_ERROR = 'ERR_6001_CONFIG',
  NETWORK_ERROR = 'ERR_6002_NETWORK',
  DATABASE_ERROR = 'ERR_6003_DATABASE',
  FILE_SYSTEM_ERROR = 'ERR_6004_FILE_SYSTEM',
  MEMORY_ERROR = 'ERR_6005_MEMORY',
  TIMEOUT_ERROR = 'ERR_6006_TIMEOUT',

  // 支付相關錯誤 (7000-7999)
  PAYMENT_FAILED = 'ERR_7001_PAYMENT_FAILED',
  PAYMENT_DECLINED = 'ERR_7002_PAYMENT_DECLINED',
  PAYMENT_METHOD_INVALID = 'ERR_7003_PAYMENT_METHOD_INVALID',
}

// 錯誤嚴重程度
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 錯誤類別
export enum ErrorCategory {
  VALIDATION = 'validation',
  BUSINESS_LOGIC = 'business_logic',
  EXTERNAL_SERVICE = 'external_service',
  SYSTEM = 'system',
  SECURITY = 'security',
  PERFORMANCE = 'performance'
}
