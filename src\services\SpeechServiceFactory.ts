/**
 * 語音服務工廠
 * 根據環境和支援情況自動選擇合適的語音服務
 */
import { createLogger } from '../utils/Logger.js';
import { SpeechRecognitionService } from './SpeechRecognitionService.js';
import { FallbackSpeechService, SpeechRecognitionResult, SpeechRecognitionOptions } from './FallbackSpeechService.js';

const logger = createLogger('SpeechServiceFactory');

// 語音服務類型
export type SpeechServiceType = 'native' | 'fallback';

// 統一的語音服務接口
export interface ISpeechService {
  startListening(
    onResult: (result: SpeechRecognitionResult) => void,
    onError: (error: Error) => void,
    options?: SpeechRecognitionOptions
  ): boolean;
  stopListening(): void;
  isSupported(): boolean;
  isCurrentlyListening(): boolean;
}

// 語音服務配置
export interface SpeechServiceConfig {
  preferredService?: SpeechServiceType;
  fallbackEnabled?: boolean;
  autoDetect?: boolean;
  forceService?: SpeechServiceType;
}

/**
 * 語音服務工廠類
 * 負責創建和管理不同類型的語音服務
 */
export class SpeechServiceFactory {
  private static instance: SpeechServiceFactory;
  private currentService: ISpeechService | null = null;
  private currentServiceType: SpeechServiceType | null = null;
  private config: SpeechServiceConfig;
  
  private constructor(config: SpeechServiceConfig = {}) {
    this.config = {
      preferredService: 'native',
      fallbackEnabled: true,
      autoDetect: true,
      ...config
    };
    
    logger.info('SpeechServiceFactory 初始化完成', this.config);
  }
  
  /**
   * 獲取工廠單例實例
   */
  static getInstance(config?: SpeechServiceConfig): SpeechServiceFactory {
    if (!SpeechServiceFactory.instance) {
      SpeechServiceFactory.instance = new SpeechServiceFactory(config);
    }
    return SpeechServiceFactory.instance;
  }
  
  /**
   * 創建語音服務
   */
  createSpeechService(): ISpeechService {
    // 如果強制指定服務類型
    if (this.config.forceService) {
      return this.createSpecificService(this.config.forceService);
    }
    
    // 自動檢測最佳服務
    if (this.config.autoDetect) {
      return this.autoDetectService();
    }
    
    // 使用偏好服務
    return this.createPreferredService();
  }
  
  /**
   * 獲取當前語音服務
   */
  getCurrentService(): ISpeechService | null {
    if (!this.currentService) {
      this.currentService = this.createSpeechService();
    }
    return this.currentService;
  }
  
  /**
   * 獲取當前服務類型
   */
  getCurrentServiceType(): SpeechServiceType | null {
    return this.currentServiceType;
  }
  
  /**
   * 切換到備用服務
   */
  switchToFallback(): ISpeechService {
    logger.info('切換到備用語音服務');
    this.currentService = this.createSpecificService('fallback');
    return this.currentService;
  }
  
  /**
   * 嘗試切換到原生服務
   */
  tryNativeService(): ISpeechService | null {
    const nativeService = this.createSpecificService('native');
    if (nativeService.isSupported()) {
      logger.info('切換到原生語音服務');
      this.currentService = nativeService;
      return this.currentService;
    }
    
    logger.warn('原生語音服務不可用');
    return null;
  }
  
  /**
   * 重置服務（重新檢測）
   */
  resetService(): ISpeechService {
    logger.info('重置語音服務');
    this.currentService = null;
    this.currentServiceType = null;
    return this.createSpeechService();
  }
  
  /**
   * 檢查服務可用性
   */
  checkServiceAvailability(): {
    native: boolean;
    fallback: boolean;
    recommended: SpeechServiceType;
  } {
    const nativeAvailable = this.isNativeServiceAvailable();
    const fallbackAvailable = this.isFallbackServiceAvailable();
    
    const recommended: SpeechServiceType = nativeAvailable ? 'native' : 'fallback';
    
    return {
      native: nativeAvailable,
      fallback: fallbackAvailable,
      recommended
    };
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SpeechServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('語音服務配置已更新', this.config);
    
    // 如果配置變更影響服務選擇，重置當前服務
    if (newConfig.preferredService || newConfig.forceService || newConfig.autoDetect !== undefined) {
      this.resetService();
    }
  }
  
  /**
   * 獲取當前配置
   */
  getConfig(): SpeechServiceConfig {
    return { ...this.config };
  }
  
  /**
   * 自動檢測最佳服務
   */
  private autoDetectService(): ISpeechService {
    logger.info('自動檢測語音服務');
    
    // 檢查原生服務可用性
    if (this.isNativeServiceAvailable()) {
      logger.info('檢測到原生語音服務可用');
      return this.createSpecificService('native');
    }
    
    // 回退到備用服務
    if (this.config.fallbackEnabled && this.isFallbackServiceAvailable()) {
      logger.info('使用備用語音服務');
      return this.createSpecificService('fallback');
    }
    
    // 如果都不可用，仍然返回備用服務（總是可用）
    logger.warn('沒有可用的語音服務，使用備用服務');
    return this.createSpecificService('fallback');
  }
  
  /**
   * 創建偏好服務
   */
  private createPreferredService(): ISpeechService {
    const preferred = this.config.preferredService!;
    
    // 嘗試創建偏好服務
    const service = this.createSpecificService(preferred);
    if (service.isSupported()) {
      return service;
    }
    
    // 如果偏好服務不可用且啟用備用
    if (this.config.fallbackEnabled && preferred !== 'fallback') {
      logger.warn(`偏好服務 ${preferred} 不可用，切換到備用服務`);
      return this.createSpecificService('fallback');
    }
    
    // 返回偏好服務（即使不支援）
    return service;
  }
  
  /**
   * 創建特定類型的服務
   */
  private createSpecificService(type: SpeechServiceType): ISpeechService {
    this.currentServiceType = type;
    
    switch (type) {
      case 'native':
        logger.debug('創建原生語音服務');
        return new SpeechRecognitionService();
      
      case 'fallback':
        logger.debug('創建備用語音服務');
        return new FallbackSpeechService();
      
      default:
        logger.error(`未知的服務類型: ${type}`);
        return new FallbackSpeechService();
    }
  }
  
  /**
   * 檢查原生服務是否可用
   */
  private isNativeServiceAvailable(): boolean {
    try {
      // 檢查瀏覽器環境
      if (typeof window === 'undefined') {
        return false;
      }
      
      // 檢查 Web Speech API
      return !!(window.SpeechRecognition || (window as any).webkitSpeechRecognition);
    } catch (error) {
      logger.debug('檢查原生語音服務時發生錯誤', error);
      return false;
    }
  }
  
  /**
   * 檢查備用服務是否可用
   */
  private isFallbackServiceAvailable(): boolean {
    // 備用服務總是可用
    return true;
  }
  
  /**
   * 獲取服務統計信息
   */
  getServiceStats(): object {
    const availability = this.checkServiceAvailability();
    
    return {
      currentServiceType: this.currentServiceType,
      availability,
      config: this.config,
      isServiceActive: this.currentService?.isCurrentlyListening() || false
    };
  }
}

// 導出便利函數
export function createSpeechService(config?: SpeechServiceConfig): ISpeechService {
  const factory = SpeechServiceFactory.getInstance(config);
  return factory.createSpeechService();
}

export function getSpeechServiceFactory(config?: SpeechServiceConfig): SpeechServiceFactory {
  return SpeechServiceFactory.getInstance(config);
}