/**
 * 菜單價格校驗工具
 * 用於確保USI AIOS回應中的價格與實際菜單價格一致
 */

// 菜單數據緩存
let menuDataCache = null;

/**
 * 獲取最新的 appPrompt 檔案路徑
 * @returns {Promise<string>} 最新的 appPrompt 檔案路徑
 */
async function getLatestAppPromptFile() {
    try {
        // 檢查 appPrompt 目錄中的檔案
        const appPromptResponse = await fetch('/api/files/get-latest-appprompt');
        
        if (appPromptResponse.ok) {
            const data = await appPromptResponse.json();
            if (data && data.success && data.filePath) {
                console.log('找到最新的 appPrompt 檔案:', data.filePath);
                return data.filePath;
            }
        }
        
        // 如果 API 調用失敗，嘗試直接列出 appPrompt 目錄中的檔案
        // 這是一個備用方案，若伺服器未實現 API
        console.log('嘗試搜尋 appPrompt 目錄...');
        
        // 嘗試在根目錄查找 appPrompt 檔案
        const rootFiles = await fetch('/api/files/list-files?path=.');
        if (rootFiles.ok) {
            const filesData = await rootFiles.json();
            if (filesData.success && filesData.files) {
                const appPromptFiles = filesData.files.filter(file => 
                    file.startsWith('appPrompt_') && file.endsWith('.json')
                );
                
                if (appPromptFiles.length > 0) {
                    // 按照檔案名稱排序（假設檔案名包含日期時間）
                    appPromptFiles.sort().reverse();
                    console.log('在根目錄找到 appPrompt 檔案:', appPromptFiles[0]);
                    return appPromptFiles[0];
                }
            }
        }
        
        // 最後的備用方案：使用硬編碼的檔案名
        console.log('無法找到最新的 appPrompt 檔案，使用預設檔案');
        return 'appPrompt_202505301519.json';
    } catch (error) {
        console.error('獲取最新 appPrompt 檔案時出錯:', error);
        // 使用預設檔案作為備用
        return 'appPrompt_202505301519.json';
    }
}

/**
 * 加載菜單數據
 * @returns {Promise<Object>} 菜單數據對象
 */
async function loadMenuData() {
    // 如果已有緩存數據，直接返回
    if (menuDataCache) {
        return menuDataCache;
    }
    
    try {
        // 獲取最新的 appPrompt 檔案路徑
        const appPromptFile = await getLatestAppPromptFile();
        console.log('使用 appPrompt 檔案:', appPromptFile);
        
        // 從appPrompt文件加載菜單數據
        const response = await fetch(appPromptFile);
        if (!response.ok) {
            throw new Error('無法加載菜單數據');
        }
        
        const data = await response.json();
        
        // 構建一個快速查詢的菜單映射
        const menuMap = {};
        if (data && data.parameters && Array.isArray(data.parameters.menu)) {
            data.parameters.menu.forEach(category => {
                if (category.items && Array.isArray(category.items)) {
                    category.items.forEach(item => {
                        // 使用餐點名稱作為鍵
                        menuMap[item.name_zh] = {
                            id: item.id,
                            price: item.price,
                            category: item.category
                        };
                    });
                }
            });
        }
        
        // 緩存菜單數據
        menuDataCache = menuMap;
        console.log('菜單數據已加載完成，共有', Object.keys(menuMap).length, '個項目');
        return menuMap;
    } catch (error) {
        console.error('加載菜單數據時出錯:', error);
        return {};
    }
}

/**
 * 校驗並修正USI AIOS回應中的價格
 * @param {string} responseText - USI AIOS的回應文本
 * @returns {Promise<{correctedText: string, priceCorrections: Array}>} 修正後的文本和價格修正信息
 */
async function validateAndCorrectPrices(responseText) {
    if (!responseText) {
        return { correctedText: responseText, priceCorrections: [] };
    }
    
    // 加載菜單數據
    const menuData = await loadMenuData();
    
    // 用於存儲發現的價格問題
    const priceCorrections = [];
    let correctedText = responseText;
    
    // 日誌記錄原始回應
    console.log('驗證價格的原始回應:', responseText);
    
    // 從回應中提取餐點名稱和價格
    // 增強匹配模式，考慮更多可能的文本格式
    const menuItemPricePatterns = [
        // 標準格式：菜品名稱，價格是 NT$價格
        /([^，。;；]*?)[，。;；].*?價格.*?NT\$(\d+)/gi,
        // 冒號格式：菜品名稱：NT$價格
        /([^：:]*?)[:：].*?NT\$(\d+)/gi,
        // 直接標示格式：菜品名稱 NT$價格
        /([^，。;；\d]+)(?:\s+|\s*[-—]\s*)NT\$(\d+)/gi
    ];
    
    // 處理所有可能的匹配模式
    let allMatches = [];
    for (const pattern of menuItemPricePatterns) {
        const matches = [...responseText.matchAll(pattern)];
        allMatches = [...allMatches, ...matches];
    }
    
    console.log('找到的所有價格匹配:', allMatches.map(m => `${m[1]}: NT$${m[2]}`));
    
    // 處理每個匹配項
    for (const match of allMatches) {
        const [fullMatch, itemName, priceStr] = match;
        const aiPrice = parseInt(priceStr, 10);
        
        // 清理並標準化餐點名稱（移除常見的干擾詞）
        const cleanedItemName = itemName.trim()
            .replace(/[的是這個您您的]|選擇了|點購|餐點|為|包括/g, '')
            .replace(/^\s+|\s+$/g, '');
        
        if (!cleanedItemName || cleanedItemName.length < 2) {
            console.log('跳過無效的餐點名稱:', itemName);
            continue;
        }
        
        console.log(`分析餐點「${cleanedItemName}」，AI報價: NT$${aiPrice}`);
        
        // 查找菜單中的真實價格
        let realMenuItem = null;
        let realPrice = 0;
        
        // 嘗試直接匹配
        if (menuData[cleanedItemName]) {
            realMenuItem = cleanedItemName;
            realPrice = menuData[cleanedItemName].price;
            console.log(`直接匹配到菜單項目「${realMenuItem}」，實際價格: NT$${realPrice}`);
        } else {
            // 嘗試模糊匹配，按相似度排序
            const potentialMatches = [];
            
            for (const key in menuData) {
                // 計算簡單的相似度分數
                let score = 0;
                if (key.includes(cleanedItemName)) score += 3;
                if (cleanedItemName.includes(key)) score += 2;
                
                // 檢查關鍵詞重疊
                const keyWords = key.split(/[的有和與]/).filter(w => w.length > 1);
                const itemWords = cleanedItemName.split(/[的有和與]/).filter(w => w.length > 1);
                
                for (const word of itemWords) {
                    if (keyWords.some(kw => kw.includes(word) || word.includes(kw))) {
                        score += 1;
                    }
                }
                
                if (score > 0) {
                    potentialMatches.push({
                        name: key,
                        price: menuData[key].price,
                        score: score
                    });
                }
            }
              // 按相似度分數排序
            potentialMatches.sort((a, b) => b.score - a.score);
            
            if (potentialMatches.length > 0) {
                realMenuItem = potentialMatches[0].name;
                realPrice = potentialMatches[0].price;
                console.log(`模糊匹配到菜單項目「${realMenuItem}」，相似度分數: ${potentialMatches[0].score}，實際價格: NT$${realPrice}`);
            }
        }
        
        // 如果找到真實價格且與AI回應價格不同
        if (realMenuItem && realPrice !== aiPrice) {
            console.log(`發現價格不符: ${cleanedItemName}, AI價格: NT$${aiPrice}, 真實價格: NT$${realPrice}`);
            
            // 替換原文本中的錯誤價格，使用更精確的正則表達式確保匹配正確的項目
            let replacement = correctedText;
            
            try {
                // 創建正則表達式來匹配該餐點的價格，包含周圍的上下文
                const escapedItemName = cleanedItemName.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
                const pricePattern = new RegExp(`(${escapedItemName}[^\\d]*?)NT\\$${aiPrice}(\\D|$)`, 'g');
                
                // 檢查匹配並替換
                const matchResult = [...correctedText.matchAll(pricePattern)];
                if (matchResult.length > 0) {
                    console.log('找到價格匹配:', matchResult);
                    replacement = correctedText.replace(pricePattern, `$1NT$${realPrice}$2`);
                } else {
                    console.log('未找到精確匹配，嘗試寬鬆匹配');
                    
                    // 嘗試更寬鬆的匹配
                    const loosePattern = new RegExp(`(${cleanedItemName.substring(0, Math.min(8, cleanedItemName.length))}[^\\d]*?)NT\\$${aiPrice}(\\D|$)`, 'gi');
                    replacement = correctedText.replace(loosePattern, `$1NT$${realPrice}$2`);
                }
            } catch (error) {
                console.error('替換價格時出錯:', error);
            }
            
            // 檢查替換是否成功
            if (replacement !== correctedText) {
                correctedText = replacement;
                console.log('已成功替換價格');
            } else {
                console.log('價格替換失敗，嘗試更廣泛的匹配');
                // 使用更廣泛的匹配策略（可能較不精確）
                correctedText = correctedText.replace(
                    new RegExp(`(${cleanedItemName.substring(0, 5)}[^，。]*?)NT\\$${aiPrice}`, 'gi'),
                    `$1NT$${realPrice}`
                );
            }
            
            // 記錄修正信息
            priceCorrections.push({
                item: realMenuItem,
                aiPrice: aiPrice,
                realPrice: realPrice
            });
        }
    }
    
    // 修正總金額
    if (priceCorrections.length > 0) {
        // 查找總金額表達式 - 支持多種格式
        const totalPatterns = [
            /總共是\s*NT\$(\d+)/i,
            /總金額[是為:：]\s*NT\$(\d+)/i,
            /合計[是為:：]\s*NT\$(\d+)/i,
            /總計[是為:：]\s*NT\$(\d+)/i,
            /總共[是為:：]\s*NT\$(\d+)/i,
            /總共\s*NT\$(\d+)/i,
            /總計\s*NT\$(\d+)/i,
            /合計\s*NT\$(\d+)/i,
            /總金額\s*NT\$(\d+)/i,
            /=\s*NT\$(\d+)/i // 計算式中的總金額
        ];
        
        let aiTotal = 0;
        let totalMatchPattern = null;
        
        // 尋找匹配的總金額模式
        for (const pattern of totalPatterns) {
            const match = correctedText.match(pattern);
            if (match && match[1]) {
                aiTotal = parseInt(match[1], 10);
                totalMatchPattern = pattern;
                console.log(`找到總金額模式 [${pattern}]，原始總金額: NT$${aiTotal}`);
                break;
            }
        }
        
        if (aiTotal > 0) {
            // 計算正確的總金額
            const correctTotal = priceCorrections.reduce((sum, correction) => {
                return sum - correction.aiPrice + correction.realPrice;
            }, aiTotal);
            
            // 替換總金額
            if (totalMatchPattern) {
                correctedText = correctedText.replace(
                    totalMatchPattern,
                    (match) => match.replace(`NT$${aiTotal}`, `NT$${correctTotal}`)
                );
                
                console.log(`修正總金額: 從 NT$${aiTotal} 到 NT$${correctTotal}`);
            } else {
                console.log('未找到可替換的總金額模式');
            }
        } else {
            console.log('未找到總金額或無法解析總金額');
            
            // 嘗試計算全部項目的總金額
            try {
                // 提取所有菜單項目，用於計算總金額
                const menuItems = await extractMenuItemsWithCorrectPrices(correctedText);
                if (menuItems.length > 0) {
                    const calculatedTotal = calculateOrderTotal(menuItems);
                    console.log('根據正確價格計算的總金額:', calculatedTotal);
                    
                    // 在文本末尾添加總金額說明
                    if (calculatedTotal > 0 && !correctedText.includes(`總共是 NT$${calculatedTotal}`)) {
                        // 檢查文本是否已經包含總金額的說明
                        if (!/總共是\s*NT\$\d+/.test(correctedText)) {
                            correctedText += `\n\n總共是 NT$${calculatedTotal}`;
                            console.log('在文本末尾添加了總金額說明');
                        }
                    }
                }
            } catch (error) {
                console.error('計算總金額時出錯:', error);
            }
        }
    }
    
    return { correctedText, priceCorrections };
}

/**
 * 提取菜單項目並添加正確的價格
 * @param {string} responseText - USI AIOS的回應文本
 * @returns {Promise<Array>} 菜單項目數組，每項包含名稱、價格和數量
 */
async function extractMenuItemsWithCorrectPrices(responseText) {
    if (!responseText) {
        return [];
    }
    
    // 加載菜單數據
    const menuData = await loadMenuData();
    const items = [];
    const processedItems = new Set(); // 用於追踪已處理的項目
    
    // 從回應中提取餐點名稱
    const lines = responseText.split('\n');
    
    // 使用多種匹配模式來提高識別率
    const menuItemPatterns = [
        // 餐點 * 數量格式 (最優先)
        /([^*\d]+)\s*\*\s*(\d+)/g,
        // 餐點 x 數量格式
        /([^x\d]+)\s*x\s*(\d+)/gi,
        // 項目：數量格式
        /([^：:]+)[：:]\s*(\d+)個/g,
        // 數量 + 餐點格式
        /(\d+)\s*個?\s*([^，。;；\n]+)/g,
        // 直接標示餐點格式
        /^((?!總共|總計|合計|總金額).{2,25}?)(?:\s+|\s*[-—]\s*)NT\$\d+/gm,
        // 餐點，價格格式
        /^((?!總共|總計|合計|總金額).{2,25}?)[，。;；].*?價格.*?NT\$/gm
    ];
    
    for (const line of lines) {
        // 跳過空行和總計行
        const trimmedLine = line.trim();
        if (!trimmedLine || /總共|總計|合計|總金額|謝謝|感謝|價格為/.test(trimmedLine)) {
            continue;
        }
        
        console.log('分析菜單行:', trimmedLine);
        
        // 嘗試所有匹配模式
        let itemName = null;
        let quantity = 1;
        
        for (const pattern of menuItemPatterns) {
            const match = trimmedLine.match(pattern);
            if (match) {
                console.log(`匹配模式: ${pattern.source}, 匹配結果:`, match);

                // 根據不同的匹配模式提取名稱和數量
                if (pattern.source.includes('\\*')) {
                    // 餐點 * 數量格式
                    itemName = match[1].trim();
                    quantity = parseInt(match[2], 10) || 1;
                } else if (pattern.source.includes('x')) {
                    // 餐點 x 數量格式
                    itemName = match[1].trim();
                    quantity = parseInt(match[2], 10) || 1;
                } else if (pattern.source.includes('[：:]')) {
                    // 項目：數量格式
                    itemName = match[1].trim();
                    quantity = parseInt(match[2], 10) || 1;
                } else if (pattern.source.includes('(\\d+)\\s*個?\\s*')) {
                    // 數量 + 餐點格式
                    quantity = parseInt(match[1], 10) || 1;
                    itemName = match[2].trim();
                } else {
                    // 其他格式，默認數量為1
                    itemName = match[1].trim();
                    // 如果匹配到數量，則使用匹配到的數量
                    if (match[2] && !isNaN(parseInt(match[2]))) {
                        quantity = parseInt(match[2]);
                    }
                }
                break;
            }
        }
        
        // 如果沒有匹配到任何模式，但行中包含價格，嘗試提取可能的餐點名稱
        if (!itemName && /NT\$\d+/.test(trimmedLine) && !/總共|總計|合計|總金額/.test(trimmedLine)) {
            // 提取 NT$ 前面的文本作為餐點名稱
            const priceIndex = trimmedLine.indexOf('NT$');
            if (priceIndex > 2) {
                const potentialName = trimmedLine.substring(0, priceIndex).trim();
                // 確保名稱不是太短或太長
                if (potentialName.length >= 2 && potentialName.length <= 25) {
                    itemName = potentialName.replace(/[的是，。:：]+$/, '').trim();
                }
            }
        }
        
        // 如果找到了餐點名稱，查找對應的價格
        if (itemName) {
            // 清理名稱中的干擾詞
            const cleanedItemName = itemName
                .replace(/[的是這個您您的]|選擇了|點購|餐點|為|包括/g, '')
                .replace(/^\s+|\s+$/g, '')
                .replace(/[，。;；:：]+$/, '');
            
            // 如果清理後的名稱太短，跳過
            if (cleanedItemName.length < 2) {
                console.log('跳過無效的餐點名稱:', itemName);
                continue;
            }
            
            // 檢查是否已處理過該餐點
            if (processedItems.has(cleanedItemName)) {
                console.log(`餐點「${cleanedItemName}」已處理過，跳過`);
                continue;
            }
            
            console.log(`分析餐點「${cleanedItemName}」，數量: ${quantity}`);
            
            // 查找真實價格
            let price = 0;
            let realMenuItem = null;
            
            // 直接匹配
            if (menuData[cleanedItemName]) {
                realMenuItem = cleanedItemName;
                price = menuData[cleanedItemName].price;
                console.log(`直接匹配到菜單項目「${realMenuItem}」，實際價格: NT$${price}`);
            } else {
                // 嘗試模糊匹配，按相似度排序
                const potentialMatches = [];
                
                for (const key in menuData) {
                    // 計算簡單的相似度分數
                    let score = 0;
                    if (key.includes(cleanedItemName)) score += 3;
                    if (cleanedItemName.includes(key)) score += 2;
                    
                    // 檢查關鍵詞重疊
                    const keyWords = key.split(/[的有和與]/).filter(w => w.length > 1);
                    const itemWords = cleanedItemName.split(/[的有和與]/).filter(w => w.length > 1);
                    
                    for (const word of itemWords) {
                        if (keyWords.some(kw => kw.includes(word) || word.includes(kw))) {
                            score += 1;
                        }
                    }
                    
                    if (score > 0) {
                        potentialMatches.push({
                            name: key,
                            price: menuData[key].price,
                            score: score
                        });
                    }
                }
                
                // 按相似度分數排序
                potentialMatches.sort((a, b) => b.score - a.score);
                
                if (potentialMatches.length > 0) {
                    realMenuItem = potentialMatches[0].name;
                    price = potentialMatches[0].price;
                    console.log(`模糊匹配到菜單項目「${realMenuItem}」，相似度分數: ${potentialMatches[0].score}，實際價格: NT$${price}`);
                }
            }
            
            if (realMenuItem) {
                // 如果已經有相同項目，增加數量而不是添加新項目
                const existingItem = items.find(item => item.name === realMenuItem);
                if (existingItem) {
                    existingItem.quantity += quantity;
                    console.log(`增加已存在項目「${realMenuItem}」的數量至 ${existingItem.quantity}`);
                } else {
                    items.push({
                        name: realMenuItem,
                        price: price,
                        quantity: quantity
                    });
                    console.log(`添加新項目「${realMenuItem}」，價格: NT$${price}，數量: ${quantity}`);
                }
                
                // 標記為已處理
                processedItems.add(cleanedItemName);
            } else {
                console.log(`無法匹配餐點「${cleanedItemName}」到菜單項目`);
            }
        }
    }
    
    console.log('提取到的最終菜單項目:', items);
    return items;
}

/**
 * 計算訂單總金額
 * @param {Array} items - 菜單項目數組
 * @returns {number} 總金額
 */
function calculateOrderTotal(items) {
    if (!items || items.length === 0) {
        return 0;
    }
    
    // 詳細記錄每個項目的價格計算
    console.log('計算訂單總金額，項目數:', items.length);
    
    return items.reduce((total, item) => {
        const itemTotal = item.price * item.quantity;
        console.log(`計算項目: ${item.name} * ${item.quantity} = NT$${itemTotal} (單價: NT$${item.price})`);
        return total + itemTotal;
    }, 0);
}

/**
 * 從回應文本中提取總金額
 * @param {string} responseText - USI AIOS的回應文本
 * @returns {number} 提取到的總金額，如果無法提取則返回0
 */
function extractTotalAmount(responseText) {
    if (!responseText) {
        return 0;
    }
    
    console.log('開始從回應中提取總金額');
    
    // 優先順序的總金額匹配模式
    const totalPatterns = [
        // 帶等號的計算式格式：NT$339 + NT$187 = NT$526
        /NT\$\d+\s*\+\s*NT\$\d+\s*=\s*NT\$(\d+)/i,
        
        // 總共是/總金額是格式
        /總共是\s*NT\$(\d+)/i,
        /總金額[是為：:]\s*NT\$(\d+)/i,
        /總計[是為：:]\s*NT\$(\d+)/i,
        /合計[是為：:]\s*NT\$(\d+)/i,
        
        // 總共/總計/合計格式
        /總共\s*NT\$(\d+)/i,
        /總計\s*NT\$(\d+)/i,
        /合計\s*NT\$(\d+)/i,
        /總金額\s*NT\$(\d+)/i,
        
        // 等號格式
        /=\s*NT\$(\d+)/i
    ];
    
    // 依次嘗試每種模式
    for (const pattern of totalPatterns) {
        const match = responseText.match(pattern);
        if (match && match[1]) {
            const amount = parseInt(match[1], 10);
            console.log(`從模式 [${pattern}] 中提取到總金額: NT$${amount}`);
            return amount;
        }
    }
    
    // 如果以上模式都沒匹配到，嘗試更寬鬆的方法
    
    // 尋找包含「總」字且包含價格的行
    const lines = responseText.split('\n');
    const totalLines = lines.filter(line => 
        /總共|總計|合計|總金額/.test(line) && /NT\$\d+/.test(line)
    );
    
    if (totalLines.length > 0) {
        // 取最後一個包含總計信息的行
        const lastTotalLine = totalLines[totalLines.length - 1];
        console.log('找到包含總計的行:', lastTotalLine);
        
        const priceMatch = lastTotalLine.match(/NT\$(\d+)/i);
        if (priceMatch && priceMatch[1]) {
            const amount = parseInt(priceMatch[1], 10);
            console.log(`從總計行中提取到總金額: NT$${amount}`);
            return amount;
        }
    }
    
    // 如果還是沒找到，查找所有價格，取最大值或合理的總價
    const allPrices = [...responseText.matchAll(/NT\$(\d+)/gi)]
        .map(match => parseInt(match[1], 10))
        .filter(price => price > 0);
    
    if (allPrices.length >= 2) {
        // 排序所有價格
        allPrices.sort((a, b) => a - b);
        console.log('找到的所有價格:', allPrices);
        
        // 最大值通常是總金額
        const maxPrice = allPrices[allPrices.length - 1];
        // 計算除了最大值以外的所有金額的總和
        const sumWithoutMax = allPrices.slice(0, -1).reduce((sum, price) => sum + price, 0);
        
        console.log(`最大金額: NT$${maxPrice}, 其他金額總和: NT$${sumWithoutMax}`);
        
        // 如果最大金額接近或大於其他金額之和，可能是總金額
        if (maxPrice >= sumWithoutMax * 0.85) { // 允許15%的誤差
            console.log(`選擇最大金額 NT$${maxPrice} 作為總金額`);
            return maxPrice;
        } else if (Math.abs(maxPrice - sumWithoutMax) < 30) { // 允許小誤差
            // 如果最大值接近於其他值之和，可能最大值就是總金額
            console.log(`選擇最大金額 NT$${maxPrice} 作為總金額（接近其他金額總和）`);
            return maxPrice;
        } else {
            // 否則，使用所有價格的總和作為總金額
            const totalSum = allPrices.reduce((sum, price) => sum + price, 0);
            console.log(`無法確定總金額，使用所有價格總和: NT$${totalSum}`);
            return totalSum;
        }
    } else if (allPrices.length === 1) {
        // 只有一個價格，直接使用
        console.log(`只找到一個價格，使用它作為總金額: NT$${allPrices[0]}`);
        return allPrices[0];
    }
    
    console.log('無法從回應中提取總金額');
    return 0;
}

// 導出函數
window.menuPriceValidator = {
    loadMenuData,
    validateAndCorrectPrices,
    extractMenuItemsWithCorrectPrices,
    calculateOrderTotal,
    extractTotalAmount
};
