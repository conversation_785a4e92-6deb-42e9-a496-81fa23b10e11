/**
 * 統一的訂單實體定義
 * 統一了 OrderService.ts 和 MockOrderService.ts 中的不同定義
 */

export interface OrderItem {
  id?: string;
  name: string;
  quantity: number;
  price: number;
  // 可選的多語言支持
  name_en?: string;
  name_zh?: string;
  name_jp?: string;
  // 可選的圖片和描述
  image_url?: string;
  description?: string;
  // 可選的餐點修飾詞
  modifiers?: string[];
  // 可選的分類信息
  category?: string;
}

export interface Order {
  id?: string;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  timestamp: Date;
  // 可選的用戶信息
  userId?: string;
  userName?: string;
  // 可選的餐廳信息
  restaurantId?: string;
  restaurantName?: string;
  // 可選的配送信息
  deliveryAddress?: string;
  deliveryTime?: Date;
  // 可選的支付信息
  paymentMethod?: string;
  paymentStatus?: PaymentStatus;
  // 可選的備註
  notes?: string;
  // 可選的會話信息
  sessionId?: string;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PREPARING = 'preparing',
  READY = 'ready',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

// 訂單創建請求
export interface CreateOrderRequest {
  items: OrderItem[];
  userId?: string;
  restaurantId?: string;
  deliveryAddress?: string;
  paymentMethod?: string;
  notes?: string;
  sessionId?: string;
}

// 訂單更新請求
export interface UpdateOrderRequest {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  deliveryTime?: Date;
  notes?: string;
}
