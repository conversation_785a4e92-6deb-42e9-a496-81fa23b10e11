/**
 * 統一錯誤處理系統
 * 解決錯誤處理不統一的問題
 */

import { Logger, createLogger } from './Logger.js';

export enum ErrorCode {
  // 通用錯誤
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  
  // 業務邏輯錯誤
  MENU_NOT_FOUND = 'MENU_NOT_FOUND',
  INVALID_ORDER = 'INVALID_ORDER',
  PROCESSING_FAILED = 'PROCESSING_FAILED',
  
  // 外部服務錯誤
  GEMINI_API_ERROR = 'GEMINI_API_ERROR',
  FIREBASE_ERROR = 'FIREBASE_ERROR',
  
  // 系統錯誤
  CONFIG_ERROR = 'CONFIG_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR'
}

export interface ErrorDetails {
  code: ErrorCode;
  message: string;
  details?: any;
  cause?: Error;
  context?: string;
}

export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly details?: any;
  public readonly cause?: Error;
  public readonly context?: string;
  public readonly timestamp: string;

  constructor(errorDetails: ErrorDetails) {
    super(errorDetails.message);
    this.name = 'AppError';
    this.code = errorDetails.code;
    this.details = errorDetails.details;
    this.cause = errorDetails.cause;
    this.context = errorDetails.context;
    this.timestamp = new Date().toISOString();

    // 確保堆棧追蹤正確
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details,
      context: this.context,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

export class ErrorHandler {
  private static logger = createLogger('ErrorHandler');

  /**
   * 統一錯誤處理方法
   */
  static handle(error: Error | AppError, context?: string): AppError {
    if (error instanceof AppError) {
      this.logger.error(`AppError in ${context || 'unknown'}`, error, {
        code: error.code,
        details: error.details
      });
      return error;
    }

    // 將普通錯誤轉換為 AppError
    const appError = new AppError({
      code: ErrorCode.UNKNOWN_ERROR,
      message: error.message || 'Unknown error occurred',
      cause: error,
      context
    });

    this.logger.error(`Unhandled error in ${context || 'unknown'}`, error);
    return appError;
  }

  /**
   * 創建 API 響應錯誤格式
   */
  static toApiResponse(error: AppError) {
    return {
      success: false,
      error: {
        code: error.code,
        message: error.message,
        ...(process.env.NODE_ENV !== 'production' && {
          details: error.details,
          context: error.context,
          timestamp: error.timestamp
        })
      }
    };
  }

  /**
   * Express 中間件錯誤處理器
   */
  static expressHandler(err: Error, req: any, res: any, next: any) {
    const appError = ErrorHandler.handle(err, `${req.method} ${req.path}`);
    
    // 根據錯誤類型設定 HTTP 狀態碼
    let statusCode = 500;
    switch (appError.code) {
      case ErrorCode.VALIDATION_ERROR:
        statusCode = 400;
        break;
      case ErrorCode.AUTHENTICATION_ERROR:
        statusCode = 401;
        break;
      case ErrorCode.AUTHORIZATION_ERROR:
        statusCode = 403;
        break;
      case ErrorCode.MENU_NOT_FOUND:
        statusCode = 404;
        break;
      case ErrorCode.GEMINI_API_ERROR:
      case ErrorCode.FIREBASE_ERROR:
        statusCode = 502;
        break;
    }

    res.status(statusCode).json(ErrorHandler.toApiResponse(appError));
  }
}

// 便利函數用於創建特定類型的錯誤
export const createError = {
  validation: (message: string, details?: any) => new AppError({
    code: ErrorCode.VALIDATION_ERROR,
    message,
    details
  }),

  menuNotFound: (restaurantId?: string) => new AppError({
    code: ErrorCode.MENU_NOT_FOUND,
    message: 'Menu not found',
    details: { restaurantId }
  }),

  geminiApi: (message: string, cause?: Error) => new AppError({
    code: ErrorCode.GEMINI_API_ERROR,
    message: `Gemini AI service error: ${message}`,
    cause
  }),

  config: (message: string) => new AppError({
    code: ErrorCode.CONFIG_ERROR,
    message: `Configuration error: ${message}`
  }),

  processing: (message: string, details?: any) => new AppError({
    code: ErrorCode.PROCESSING_FAILED,
    message,
    details
  })
};
