<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>語音語言測試 - Speech Language Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .language-selector {
            margin: 20px 0;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 6px;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .price-test {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 語音語言測試 / Speech Language Test</h1>
        
        <div class="language-selector">
            <h3>選擇語言 / Select Language:</h3>
            <button class="test-button" onclick="setLanguage('zh-TW')">繁體中文</button>
            <button class="test-button" onclick="setLanguage('en-US')">English</button>
            <button class="test-button" onclick="setLanguage('ja-JP')">日本語</button>
            <p>當前語言 / Current Language: <span id="current-lang">zh-TW</span></p>
        </div>

        <div class="test-section">
            <h3>🎵 基本語音測試 / Basic Speech Test</h3>
            <button class="test-button" onclick="testBasicSpeech()">測試基本語音 / Test Basic Speech</button>
        </div>

        <div class="test-section">
            <h3>💰 價格語音測試 / Price Speech Test</h3>
            <div class="price-test">
                <button class="test-button" onclick="testPriceSpeech(99)">$99 測試</button>
                <button class="test-button" onclick="testPriceSpeech(150)">$150 測試</button>
                <button class="test-button" onclick="testPriceSpeech(299)">$299 測試</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🍔 餐點語音測試 / Menu Item Speech Test</h3>
            <button class="test-button" onclick="testMenuItemSpeech()">測試餐點語音 / Test Menu Item Speech</button>
        </div>

        <div class="test-section">
            <h3>📋 完整訂單語音測試 / Full Order Speech Test</h3>
            <button class="test-button" onclick="testFullOrderSpeech()">測試完整訂單 / Test Full Order</button>
        </div>

        <div class="log-output" id="log-output">
            <strong>測試日誌 / Test Log:</strong><br>
        </div>
    </div>

    <script>
        let currentLanguage = 'zh-TW';
        
        // 模擬語言資源
        const testLanguageResources = {
            'zh-TW': {
                'identified_single_item': '為您識別到 {quantity} 份 {item}，單價 {price}。',
                'total_amount': '總金額為 {total}。',
                'basic_test_message': '這是中文語音測試。',
                'menu_item_name': '大麥克'
            },
            'en-US': {
                'identified_single_item': 'Identified {quantity} {item} at {price}.',
                'total_amount': 'Total amount: {total}.',
                'basic_test_message': 'This is an English speech test.',
                'menu_item_name': 'Big Mac'
            },
            'ja-JP': {
                'identified_single_item': '{quantity}個の{item}を識別しました。価格は{price}です。',
                'total_amount': '合計金額は{total}です。',
                'basic_test_message': 'これは日本語の音声テストです。',
                'menu_item_name': 'ビッグマック'
            }
        };

        function log(message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `<br>[${timestamp}] ${message}`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function setLanguage(lang) {
            currentLanguage = lang;
            document.getElementById('current-lang').textContent = lang;
            localStorage.setItem('preferredLanguage', lang);
            log(`🌐 語言設定為: ${lang}`);
        }

        function getTranslation(key) {
            return testLanguageResources[currentLanguage]?.[key] || key;
        }

        function formatPriceForSpeech(price) {
            if (currentLanguage === 'en-US' || currentLanguage === 'en') {
                return `$${price}`;
            } else if (currentLanguage === 'ja-JP' || currentLanguage === 'ja') {
                return `${price}円`;
            } else {
                return `${price}元`;
            }
        }

        async function speakText(text) {
            log(`🔊 準備播放語音: ${text}`);
            
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                
                // 根據當前語言設定語音語言
                if (currentLanguage === 'en-US' || currentLanguage === 'en') {
                    utterance.lang = 'en-US';
                    log(`🌐 設定語音語言為: en-US`);
                } else if (currentLanguage === 'ja-JP' || currentLanguage === 'ja') {
                    utterance.lang = 'ja-JP';
                    log(`🌐 設定語音語言為: ja-JP`);
                } else {
                    utterance.lang = 'zh-TW';
                    log(`🌐 設定語音語言為: zh-TW`);
                }
                
                utterance.rate = 0.9;
                utterance.volume = 0.9;
                
                utterance.onstart = () => log('✅ 語音播放開始');
                utterance.onend = () => log('✅ 語音播放結束');
                utterance.onerror = (e) => log(`❌ 語音播放錯誤: ${e.error}`);
                
                speechSynthesis.speak(utterance);
            } else {
                log('❌ 瀏覽器不支援語音合成');
            }
        }

        function testBasicSpeech() {
            const message = getTranslation('basic_test_message');
            log(`🎵 測試基本語音: ${message}`);
            speakText(message);
        }

        function testPriceSpeech(price) {
            const formattedPrice = formatPriceForSpeech(price);
            const message = `價格測試: ${formattedPrice}`;
            log(`💰 測試價格語音: ${message}`);
            speakText(message);
        }

        function testMenuItemSpeech() {
            const itemName = getTranslation('menu_item_name');
            const price = 150;
            const formattedPrice = formatPriceForSpeech(price);
            
            const template = getTranslation('identified_single_item');
            const message = template
                .replace('{quantity}', '1')
                .replace('{item}', itemName)
                .replace('{price}', formattedPrice);
                
            log(`🍔 測試餐點語音: ${message}`);
            speakText(message);
        }

        function testFullOrderSpeech() {
            const itemName = getTranslation('menu_item_name');
            const price = 150;
            const quantity = 2;
            const total = price * quantity;
            
            const formattedPrice = formatPriceForSpeech(price);
            const formattedTotal = formatPriceForSpeech(total);
            
            const itemTemplate = getTranslation('identified_single_item');
            const totalTemplate = getTranslation('total_amount');
            
            const itemMessage = itemTemplate
                .replace('{quantity}', quantity)
                .replace('{item}', itemName)
                .replace('{price}', formattedPrice);
                
            const totalMessage = totalTemplate.replace('{total}', formattedTotal);
            
            const fullMessage = `${itemMessage} ${totalMessage}`;
            
            log(`📋 測試完整訂單語音: ${fullMessage}`);
            speakText(fullMessage);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 語音語言測試頁面已載入');
            log('請選擇語言並測試不同的語音功能');
        });
    </script>
</body>
</html>
