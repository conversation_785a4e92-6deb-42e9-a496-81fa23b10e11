import { TextToSpeech } from '@capacitor-community/text-to-speech';
import { createLogger } from '../utils/Logger.js';

// 定義 TextToSpeech 相關的類型
interface TTSResult {
  supported: boolean;
}

interface TTSLanguagesResult {
  languages: string[];
}

interface TTSVoicesResult {
  voices: any[];
}

const logger = createLogger('NativeSpeechService');

export interface SpeechOptions {
  text: string;
  lang?: string;
  rate?: number;
  pitch?: number;
  volume?: number;
  voice?: number;
}

export class NativeSpeechService {
  private static instance: NativeSpeechService;
  private isSupported: boolean = false;
  private isEnabled: boolean = true;

  private constructor() {
    this.checkSupport();
  }

  public static getInstance(): NativeSpeechService {
    if (!NativeSpeechService.instance) {
      NativeSpeechService.instance = new NativeSpeechService();
    }
    return NativeSpeechService.instance;
  }

  private async checkSupport(): Promise<void> {
    try {
      const result: TTSResult = await TextToSpeech.isLanguageSupported({ lang: 'zh-TW' });
      this.isSupported = result.supported;
      logger.info('原生語音支援狀態:', this.isSupported);
    } catch (error: unknown) {
      logger.error('檢查語音支援時發生錯誤:', error instanceof Error ? error : new Error(String(error)));
      this.isSupported = false;
    }
  }

  public async speak(options: SpeechOptions): Promise<void> {
    if (!this.isEnabled) {
      logger.info('語音功能已被禁用');
      return;
    }

    if (!this.isSupported) {
      logger.warn('設備不支援原生語音功能');
      return;
    }

    if (!options.text || options.text.trim() === '') {
      logger.warn('語音文本為空');
      return;
    }

    try {
      // 停止當前播放的語音
      await this.stop();

      // 設定語音參數
      const speechOptions = {
        text: options.text,
        lang: options.lang || 'zh-TW',
        rate: options.rate || 0.9,
        pitch: options.pitch || 1.0,
        volume: options.volume || 0.8,
        voice: options.voice || 0
      };

      logger.info('開始播放語音:', options.text.substring(0, 50) + (options.text.length > 50 ? '...' : ''));
      
      await TextToSpeech.speak(speechOptions);
      
      logger.info('語音播放完成');
    } catch (error: unknown) {
      logger.error('語音播放失敗:', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      await TextToSpeech.stop();
    } catch (error: unknown) {
      logger.error('停止語音播放失敗:', error instanceof Error ? error : new Error(String(error)));
    }
  }

  public async getSupportedLanguages(): Promise<string[]> {
    try {
      const result: TTSLanguagesResult = await TextToSpeech.getSupportedLanguages();
      return result.languages;
    } catch (error: unknown) {
      logger.error('獲取支援語言失敗:', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  public async getSupportedVoices(): Promise<any[]> {
    try {
      const result: TTSVoicesResult = await TextToSpeech.getSupportedVoices();
      return result.voices;
    } catch (error: unknown) {
      logger.error('獲取支援語音失敗:', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    logger.info('語音功能狀態:', enabled ? '啟用' : '禁用');
  }

  public isLanguageSupported(lang: string): Promise<boolean> {
    return TextToSpeech.isLanguageSupported({ lang }).then((result: TTSResult) => result.supported);
  }

  public getSupported(): boolean {
    return this.isSupported;
  }

  public getEnabled(): boolean {
    return this.isEnabled;
  }
}

// 導出單例實例
export const nativeSpeechService = NativeSpeechService.getInstance();