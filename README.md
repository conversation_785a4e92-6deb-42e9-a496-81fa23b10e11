# 🍽️ Smart Ordering - 智能點餐 Android 應用

一個基於 Capacitor 的跨平台智能點餐 Android 應用程式，整合了自然語言處理、語音識別和 AI 助手功能，讓用戶可以透過自然語言（文字或語音）進行餐點訂購。

## 📱 應用特色

- **🤖 AI 智能點餐**：整合 Google Gemini AI，支援自然語言對話式點餐
- **🎤 語音識別**：支援中文語音輸入，即時語音轉文字
- **🔊 語音合成**：智能語音播報，支援中文 TTS 輸出
- **🌍 多語言支援**：繁體中文、英文、日文完整本地化
- **📱 原生體驗**：基於 Capacitor 框架，提供接近原生的使用體驗
- **☁️ 雲端整合**：連接遠程服務器，支援即時數據同步

## 🛠️ 技術架構

### 前端技術
- **Capacitor 7.3.0**：跨平台應用框架
- **HTML5/CSS3/JavaScript**：現代化 Web 技術
- **Web Speech API**：語音識別與合成
- **響應式設計**：適配各種螢幕尺寸

### 後端服務
- **Node.js + Express**：RESTful API 服務
- **TypeScript**：類型安全的開發體驗
- **Google Gemini AI**：自然語言處理
- **Firebase/Firestore**：雲端數據庫

### 語音功能
- **@capacitor-community/speech-recognition**：語音識別插件
- **@capacitor-community/text-to-speech**：語音合成插件
- **智能語音選擇**：自動選擇最佳中文語音引擎
- **降噪處理**：提高語音識別準確率

### 其他插件
- **@capacitor/camera**：相機功能
- **@capacitor/filesystem**：文件系統存取
- **@capacitor/splash-screen**：啟動畫面
- **@capacitor/status-bar**：狀態列控制

## 🚀 快速開始

### 環境需求
- **Node.js** 18+
- **Android Studio** (用於 Android 開發)
- **Capacitor CLI** 7.3.0+
- **Google Gemini API** 金鑰
- **Firebase** 專案配置

### 安裝與設置

1. **克隆專案**：
   ```bash
   git clone <repository-url>
   cd "New Natural Order - Android"
   ```

2. **安裝依賴**：
   ```bash
   npm install
   ```

3. **環境配置**：
   ```bash
   cp .env.example .env.android
   # 編輯 .env.android 文件，填入您的配置
   ```

4. **編譯 TypeScript**：
   ```bash
   npm run build
   ```

5. **同步 Capacitor**：
   ```bash
   npx cap sync android
   ```

6. **在 Android Studio 中開啟**：
   ```bash
   npx cap open android
   ```

### 開發模式

```bash
# 啟動開發服務器
npm run dev

# 同步到 Android
npx cap sync android

# 在設備上運行
npx cap run android
```

## 📋 主要功能

### 🎤 語音點餐
- **語音識別**：點擊麥克風按鈕開始語音輸入
- **即時轉換**：語音即時轉換為文字
- **智能理解**：AI 理解複雜的點餐需求
- **語音確認**：訂單確認語音播報

### 💬 文字點餐
- **自然語言輸入**：支援自然的中文表達
- **智能解析**：自動識別餐點、數量、特殊要求
- **即時回應**：AI 助手即時回應和建議
- **訂單確認**：詳細的訂單摘要顯示

### 📱 用戶界面
- **直觀設計**：簡潔易用的操作界面
- **多語言切換**：支援繁中/英文/日文切換
- **響應式布局**：適配不同螢幕尺寸
- **無障礙支援**：支援語音操作和視覺輔助

### ☁️ 雲端服務
- **即時同步**：訂單數據即時同步到雲端
- **菜單管理**：動態載入最新菜單信息
- **用戶歷史**：保存用戶點餐歷史記錄
- **多設備同步**：支援多設備數據同步

## 🔧 配置說明

### Capacitor 配置

```typescript
// capacitor.config.ts
const config: CapacitorConfig = {
  appId: 'com.aios.app.ordering',
  appName: 'Smart Ordering',
  webDir: 'public',
  server: {
    url: 'https://rudylee.eu.org:4003' // 遠程服務器
  }
};
```

### 環境變數

```bash
# .env.android
GEMINI_API_KEY=your_gemini_api_key
FIREBASE_CONFIG=your_firebase_config
USE_GEMINI_AI=true
GEMINI_MODEL=gemini-1.5-pro
LOG_LEVEL=INFO
```

## 🎯 語音功能詳解

### 語音識別 (Speech Recognition)
- **多語言支援**：支援中文、英文、日文語音識別
- **連續識別**：支援長時間連續語音輸入
- **即時結果**：提供即時和最終識別結果
- **錯誤處理**：智能處理網路錯誤和權限問題

### 語音合成 (Text-to-Speech)
- **智能語音選擇**：自動選擇最佳中文語音引擎
- **語音參數優化**：調整語速、音調、音量
- **多語言播報**：支援中英日三語語音播報
- **回退機制**：Capacitor TTS 失敗時自動回退到瀏覽器 TTS

### 語音優化特性
- **動態語音檢測**：檢測設備可用語音包
- **優先級選擇**：台灣中文 > 香港中文 > 其他中文語音
- **參數調優**：語速 0.9、音量 0.9，確保清晰度
- **詳細日誌**：完整的語音操作日誌記錄

## 🧪 測試

### 單元測試
```bash
# 運行所有測試
npm test

# 運行 Jest 測試
npm run test:jest

# 測試覆蓋率
npm run test:coverage

# 監視模式
npm run test:jest:watch
```

### 設備測試
```bash
# 在 Android 設備上測試
npx cap run android

# 在 Android 模擬器上測試
npx cap run android --target=emulator
```

## 📱 部署指南

### Android APK 構建

1. **準備發布版本**：
   ```bash
   npm run build
   npx cap sync android
   ```

2. **在 Android Studio 中**：
   - 開啟 `android` 目錄
   - 選擇 `Build > Generate Signed Bundle / APK`
   - 選擇 APK 或 Bundle 格式
   - 配置簽名金鑰
   - 構建發布版本

3. **命令行構建**：
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

### 發布到 Google Play

1. **準備應用**：
   - 更新版本號 (`android/app/build.gradle`)
   - 準備應用圖標和截圖
   - 撰寫應用描述

2. **上傳到 Play Console**：
   - 創建新版本
   - 上傳 AAB 文件
   - 填寫版本說明
   - 提交審核

## 🔍 故障排除

### 常見問題

**Q: 語音識別無法使用**
A: 請檢查：
- 麥克風權限是否已授予
- 設備是否支援語音識別
- 網路連線是否正常
- 嘗試重新啟動應用

**Q: 語音合成沒有聲音**
A: 請檢查：
- 設備音量是否開啟
- 是否安裝了中文語音包
- 嘗試切換到其他語音引擎
- 查看應用日誌中的語音設定信息

**Q: 無法連接到服務器**
A: 請檢查：
- 網路連線是否正常
- 服務器 URL 是否正確
- 防火牆設定是否阻擋連線
- 嘗試切換網路環境

**Q: AI 無法回應**
A: 請檢查：
- Gemini API 金鑰是否正確
- API 配額是否已用完
- 服務器是否正常運行
- 查看網路請求日誌

### 調試技巧

```bash
# 查看設備日誌
adb logcat | grep -i "smart ordering"

# 查看 Capacitor 日誌
npx cap run android --livereload

# 檢查網路請求
# 在 Chrome DevTools 中查看 Network 標籤
```

## 🔄 版本歷史

### v1.0.0 (當前版本)
- ✅ 基礎 Android 應用框架
- ✅ 語音識別與合成功能
- ✅ AI 智能點餐助手
- ✅ 多語言界面支援
- ✅ 雲端服務整合
- ✅ 中文語音優化
- ✅ 響應式用戶界面

### 計劃功能
- 🔄 離線模式支援
- 🔄 用戶偏好設定
- 🔄 訂單歷史查詢
- 🔄 推送通知
- 🔄 社交分享功能

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

### 開發規範
- 遵循 TypeScript 編碼規範
- 所有新功能都需要包含測試
- 保持代碼覆蓋率在 80% 以上
- 使用有意義的 commit 訊息
- 測試 Android 設備兼容性

## 📄 許可證

本專案使用 ISC 許可證 - 詳見 [LICENSE](LICENSE) 文件

## 🙏 致謝

- **Capacitor 團隊** - 提供優秀的跨平台框架
- **Google Generative AI 團隊** - 提供強大的 Gemini AI 服務
- **Firebase 團隊** - 提供可靠的雲端數據庫解決方案
- **Capacitor Community** - 提供語音識別和 TTS 插件
- **Android 開發社群** - 提供豐富的開發資源和支援
- **所有測試用戶** - 協助改進應用功能和用戶體驗

### 技術支援
- [Capacitor 官方文檔](https://capacitorjs.com/docs)
- [Google Gemini AI 官方文檔](https://ai.google.dev/)
- [Firebase 官方文檔](https://firebase.google.com/docs)
- [Android 開發者文檔](https://developer.android.com/)
- [Capacitor Community 插件](https://github.com/capacitor-community)

---

**Smart Ordering** - 讓點餐變得更智能、更簡單！ 🍽️✨
