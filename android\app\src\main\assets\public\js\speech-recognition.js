/**
 * 語音識別模組的前端整合
 * 提供語音控制和語音轉文字功能
 */
 
// 語音識別狀態
// 檢查全局變數，避免重複宣告
if (typeof window.speechRecognitionInstance === 'undefined') {
  window.speechRecognitionInstance = null;
}
let isRecognizing = false;
let isAndroidWebView = false;
let speechSupported = false;

// DOM 元素
let micButton = null;
let statusIndicator = null;
let resultContainer = null;
let orderInput = null;

// 檢測運行環境
function detectEnvironment() {
  // 檢測是否為 Android WebView
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  isAndroidWebView = /Android/.test(userAgent) && /wv/.test(userAgent);
  
  // 檢測語音 API 支援
  speechSupported = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
  
  console.log('環境檢測結果:', {
    isAndroidWebView,
    speechSupported,
    userAgent: userAgent.substring(0, 100)
  });
  
  return {
    isAndroidWebView,
    speechSupported,
    shouldUseFallback: isAndroidWebView || !speechSupported
  };
}

// 初始化語音識別功能
function initSpeechRecognition() {
  console.log('初始化語音識別功能');
  
  const env = detectEnvironment();
  
  if (env.shouldUseFallback) {
    console.log('使用備用語音服務');
    initFallbackSpeechService();
    return;
  }
    // 建立語音識別物件
  window.speechRecognitionInstance = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
  
  // 配置語音識別 - 根據當前語言設定調整
  const currentLanguage = getCurrentLanguage();
  if (currentLanguage === 'en-US') {
    window.speechRecognitionInstance.lang = 'en-US';
  } else if (currentLanguage === 'ja-JP') {
    window.speechRecognitionInstance.lang = 'ja-JP';
  } else {
    window.speechRecognitionInstance.lang = 'zh-TW';
  }
  window.speechRecognitionInstance.continuous = false;
  window.speechRecognitionInstance.interimResults = true;
  window.speechRecognitionInstance.maxAlternatives = 1;
  
  // 設置事件處理
  setupRecognitionEvents();
  
  // 綁定 UI 元素
  bindUIElements();
}

// 配置語音識別事件
function setupRecognitionEvents() {
  if (!window.speechRecognitionInstance) return;
  
  window.speechRecognitionInstance.onstart = function() {
    console.log('語音識別開始');
    isRecognizing = true;
    updateUI();
    showStatus(getTranslation('speech_listening') || '正在聆聽...');
  };
  
  window.speechRecognitionInstance.onresult = function(event) {
    let interimTranscript = '';
    let finalTranscript = '';
    
    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript;
      if (event.results[i].isFinal) {
        finalTranscript += transcript;
      } else {
        interimTranscript += transcript;
      }
    }
    
    if (interimTranscript) {
      showInterimResult(interimTranscript);
    }
    
    if (finalTranscript) {
      finalizeResult(finalTranscript);
      // 自動停止識別
      setTimeout(() => {
        if (isRecognizing) {
          stopRecognition();
        }
      }, 500);
    }
  };
  
  window.speechRecognitionInstance.onerror = function(event) {
    console.error('語音識別錯誤:', event.error);
    isRecognizing = false;
    updateUI();
    
    // 根據錯誤類型提供不同的處理
    let errorMessage = '語音識別發生錯誤';
    switch(event.error) {
      case 'not-allowed':
        errorMessage = '語音權限被拒絕，請檢查瀏覽器設定';
        break;
      case 'no-speech':
        errorMessage = '未檢測到語音，請重試';
        break;
      case 'network':
        errorMessage = '網路連接問題，嘗試使用備用輸入';
        break;
      case 'aborted':
        errorMessage = '語音識別被中斷';
        break;
      default:
        errorMessage = `語音識別錯誤: ${event.error}`;
    }
    
    showError(errorMessage);
    
    // 如果是權限或網路問題，自動切換到備用模式
    if (['not-allowed', 'network'].includes(event.error)) {
      setTimeout(() => {
        if (window.fallbackSpeechUI) {
          window.fallbackSpeechUI.showInterface();
        }
      }, 2000);
    }
  };
  
  window.speechRecognitionInstance.onend = function() {
    console.log('語音識別結束');
    isRecognizing = false;
    updateUI();
    showStatus(getTranslation('speech_ready') || '語音識別就緒');
  };
}

// 綁定 UI 元素
function bindUIElements() {
  micButton = document.getElementById('voice-btn') || document.querySelector('.voice-btn');
  statusIndicator = document.getElementById('voice-status') || document.querySelector('.voice-status');
  resultContainer = document.getElementById('speech-result') || document.querySelector('.speech-result');
  orderInput = document.getElementById('user-input') || document.querySelector('#user-input');
  
  if (micButton) {
    micButton.addEventListener('click', toggleRecognition);
  }
}

// 切換語音識別狀態
function toggleRecognition() {
  if (isRecognizing) {
    stopRecognition();
  } else {
    startRecognition();
  }
}

// 開始語音識別
function startRecognition() {
  if (!window.speechRecognitionInstance) {
    console.warn('語音識別實例不存在，嘗試使用備用服務');
    if (window.fallbackSpeechUI) {
      window.fallbackSpeechUI.showInterface();
    }
    return;
  }
  
  // 防止重複啟動
  if (isRecognizing) {
    console.warn('語音識別已在進行中');
    return;
  }
  
  try {
    isRecognizing = true;
    window.speechRecognitionInstance.start();
    showStatus(getTranslation('speech_listening') || '正在聆聽...');
    updateUI();
  } catch (error) {
    console.error('啟動語音識別失敗:', error);
    isRecognizing = false;
    showError(`無法啟動語音識別: ${error.message}`);
    updateUI();
    
    // 如果原生語音失敗，嘗試使用備用服務
    if (window.fallbackSpeechUI) {
      setTimeout(() => {
        window.fallbackSpeechUI.showInterface();
      }, 1000);
    }
  }
}

// 停止語音識別
function stopRecognition() {
  if (!window.speechRecognitionInstance) return;
  
  try {
    if (isRecognizing) {
      window.speechRecognitionInstance.stop();
      isRecognizing = false;
      showStatus(getTranslation('speech_stopped') || '已停止聆聽');
      updateUI();
    }
  } catch (error) {
    console.error('停止語音識別失敗:', error);
    isRecognizing = false;
    updateUI();
  }
}

// 顯示中間結果
function showInterimResult(text) {
  if (resultContainer) {
    resultContainer.innerHTML = `<span class="interim-result">${text}</span>`;
  }
}

// 處理最終結果
function finalizeResult(text) {
  if (resultContainer) {
    resultContainer.innerHTML = `<span class="final-result">${text}</span>`;
  }
  
  // 如果有訂單輸入框，將識別結果填入
  if (orderInput) {
    orderInput.value = text;
    
    // 觸發輸入事件，以便其他依賴輸入的功能可以響應
    const inputEvent = new Event('input', { bubbles: true });
    orderInput.dispatchEvent(inputEvent);
  }
  
  // 自動提交訂單（可選功能，根據需要啟用或禁用）
  // autoSubmitOrder();
}

// 顯示語音識別狀態
function showStatus(message) {
  if (statusIndicator) {
    statusIndicator.textContent = message;
  }
}

// 顯示錯誤信息
function showError(message) {
  if (statusIndicator) {
    statusIndicator.textContent = message;
    statusIndicator.classList.add('error');
    
    // 3 秒後清除錯誤狀態
    setTimeout(() => {
      statusIndicator.classList.remove('error');
    }, 3000);
  }
}

// 更新 UI 以反映當前狀態
function updateUI() {
  if (micButton) {
    if (isRecognizing) {
      micButton.classList.add('recording');
      micButton.innerHTML = '<i class="fas fa-stop"></i>';
      micButton.title = getTranslation('click_stop_speech') || '點擊停止語音識別';
    } else {
      micButton.classList.remove('recording');
      micButton.innerHTML = '<i class="fas fa-microphone"></i>';
      micButton.title = getTranslation('click_start_speech') || '點擊開始語音識別';
    }
  }
}

// 自動提交訂單
function autoSubmitOrder() {
  const orderForm = document.getElementById('order-form');
  
  if (orderForm && orderInput && orderInput.value.trim()) {
    // 延遲提交以便用戶有時間檢查識別結果
    setTimeout(() => {
      orderForm.submit();
    }, 1500);
  }
}

// 禁用語音功能
function disableSpeechFeatures() {
  const speechElements = document.querySelectorAll('.speech-feature');
  speechElements.forEach(element => {
    element.classList.add('disabled');
    element.title = '您的瀏覽器不支援語音識別功能';
    
    // 禁用按钮
    if (element.tagName === 'BUTTON') {
      element.disabled = true;
    }
  });
  
  showStatus('語音識別不可用 - 瀏覽器不支援');
}

// 在頁面加載完成後初始化語音識別
document.addEventListener('DOMContentLoaded', function() {
  // 初始化語音識別功能
  initSpeechRecognition();
});

// 初始化備用語音服務
function initFallbackSpeechService() {
  console.log('初始化備用語音服務');
  
  // 綁定 UI 元素
  bindUIElements();
  
  // 修改麥克風按鈕行為
  if (micButton) {
    micButton.addEventListener('click', function() {
      if (window.FallbackSpeechUI) {
        window.FallbackSpeechUI.show(function(result) {
          if (result && result.trim()) {
            finalizeResult(result);
          }
        });
      } else {
        showError('備用語音服務不可用');
      }
    });
    
    // 更新按鈕樣式和提示
    micButton.innerHTML = '<i class="fas fa-keyboard"></i>';
    micButton.title = getTranslation('click_text_input') || '點擊進行文字輸入';
    micButton.classList.add('fallback-mode');
  }
  
  showStatus(getTranslation('fallback_speech_ready') || '備用語音服務已就緒');
}

// 檢查是否使用備用語音服務
function isFallbackMode() {
  return micButton && micButton.classList.contains('fallback-mode');
}

// 導出函數供其他腳本使用
window.voiceRecognition = {
  start: startRecognition,
  stop: stopRecognition,
  toggle: toggleRecognition,
  isFallbackMode: isFallbackMode
};

// 導出備用語音初始化函數
window.initFallbackSpeech = initFallbackSpeechService;
