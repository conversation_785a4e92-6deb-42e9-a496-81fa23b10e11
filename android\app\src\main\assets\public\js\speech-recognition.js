/**
 * 語音識別模組的前端整合
 * 提供語音控制和語音轉文字功能
 */
 
// 語音識別狀態
// 檢查全局變數，避免重複宣告
if (typeof window.speechRecognitionInstance === 'undefined') {
  window.speechRecognitionInstance = null;
}
let isRecognizing = false;
let isAndroidWebView = false;
let speechSupported = false;
let silenceTimer = null;
let lastSpeechTime = null;
const SILENCE_TIMEOUT = 15000; // 15 秒靜默後停止

// DOM 元素
let micButton = null;
let statusIndicator = null;
let resultContainer = null;
let orderInput = null;

// 檢測運行環境
function detectEnvironment() {
  // 檢測是否為 Android WebView
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  isAndroidWebView = /Android/.test(userAgent) && /wv/.test(userAgent);
  
  // 檢測語音 API 支援
  speechSupported = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
  
  console.log('環境檢測結果:', {
    isAndroidWebView,
    speechSupported,
    userAgent: userAgent.substring(0, 100)
  });
  
  return {
    isAndroidWebView,
    speechSupported,
    shouldUseFallback: isAndroidWebView || !speechSupported
  };
}

// 初始化語音識別功能
function initSpeechRecognition() {
  console.log('初始化語音識別功能');
  
  const env = detectEnvironment();
  
  if (env.shouldUseFallback) {
    console.log('使用備用語音服務');
    initFallbackSpeechService();
    return;
  }
    // 建立語音識別物件
  window.speechRecognitionInstance = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
  
  // 配置語音識別 - 根據當前語言設定調整
  const currentLanguage = getCurrentLanguage();
  if (currentLanguage === 'en-US') {
    window.speechRecognitionInstance.lang = 'en-US';
  } else if (currentLanguage === 'ja-JP') {
    window.speechRecognitionInstance.lang = 'ja-JP';
  } else {
    window.speechRecognitionInstance.lang = 'zh-TW';
  }
  
  // 優化語音識別配置
  window.speechRecognitionInstance.continuous = true;
  window.speechRecognitionInstance.interimResults = true;
  window.speechRecognitionInstance.maxAlternatives = 1;
  
  // 設置語音檢測參數以提高靈敏度
  if ('serviceURI' in window.speechRecognitionInstance) {
    // 某些瀏覽器支援額外配置
    window.speechRecognitionInstance.serviceURI = '';
  }
  
  // 增加語音識別的等待時間
  if (window.speechRecognitionInstance.grammars) {
    window.speechRecognitionInstance.grammars = new (window.SpeechGrammarList || window.webkitSpeechGrammarList)();
  }
  
  // 設置事件處理
  setupRecognitionEvents();
  
  // 綁定 UI 元素
  bindUIElements();
}

// 配置語音識別事件
function setupRecognitionEvents() {
  if (!window.speechRecognitionInstance) return;
  
  window.speechRecognitionInstance.onstart = function() {
    console.log('語音識別開始');
    window.speechStartTriggered = true;
    isRecognizing = true;
    updateUI();
    
    // 顯示更明確的提示
    showStatus('🎤 語音識別已啟動，請開始說話...');
    
    // 3秒後如果還沒有檢測到語音，給出提示
    setTimeout(() => {
      if (isRecognizing && !window.speechDetected) {
        showStatus('🔊 請大聲清楚地說話...');
      }
    }, 3000);
  };
  
  window.speechRecognitionInstance.onspeechstart = function() {
    console.log('檢測到語音開始');
    window.speechDetected = true;
    showStatus('✅ 正在識別語音...');
  };
  
  window.speechRecognitionInstance.onspeechend = function() {
    console.log('檢測到語音結束');
    showStatus('⏳ 處理語音中...');
  };
  
  window.speechRecognitionInstance.onsoundstart = function() {
    console.log('檢測到聲音開始');
    window.speechDetected = true;
    showStatus('🎵 檢測到聲音...');
  };
  
  window.speechRecognitionInstance.onsoundend = function() {
    console.log('檢測到聲音結束');
  };
  
  window.speechRecognitionInstance.onaudiostart = function() {
    console.log('音頻捕獲開始');
    
    // 如果 onstart 沒有觸發，用 onaudiostart 作為啟動成功的標誌
    if (!window.speechStartTriggered) {
      console.log('使用 onaudiostart 作為啟動成功標誌');
      window.speechStartTriggered = true;
      isRecognizing = true;
      updateUI();
      showStatus('🎤 語音識別已啟動，請開始說話...');
    }
  };
  
  window.speechRecognitionInstance.onaudioend = function() {
    console.log('音頻捕獲結束');
  };
  
  window.speechRecognitionInstance.onnomatch = function() {
    console.log('無法識別語音');
  };
  
  window.speechRecognitionInstance.onresult = function(event) {
    let interimTranscript = '';
    let finalTranscript = '';
    
    // 更新最後語音時間
    lastSpeechTime = Date.now();
    
    // 清除之前的靜默計時器
    if (silenceTimer) {
      clearTimeout(silenceTimer);
      silenceTimer = null;
    }
    
    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript;
      if (event.results[i].isFinal) {
        finalTranscript += transcript;
      } else {
        interimTranscript += transcript;
      }
    }
    
    if (interimTranscript) {
      showInterimResult(interimTranscript);
    }
    
    if (finalTranscript) {
      finalizeResult(finalTranscript);
    }
    
    // 設置新的靜默計時器
    silenceTimer = setTimeout(() => {
      if (isRecognizing && lastSpeechTime && (Date.now() - lastSpeechTime) >= SILENCE_TIMEOUT) {
        console.log('檢測到長時間靜默，停止語音識別');
        stopRecognition();
      }
    }, SILENCE_TIMEOUT);
  };
  
  window.speechRecognitionInstance.onerror = function(event) {
    console.error('語音識別錯誤:', event.error);
    
    // 根據錯誤類型提供不同的處理
    let errorMessage = '語音識別發生錯誤';
    let shouldStop = true;
    
    switch(event.error) {
      case 'not-allowed':
        errorMessage = '語音權限被拒絕，請檢查瀏覽器設定';
        break;
      case 'no-speech':
        // no-speech 錯誤通常是暫時的，不應該立即停止識別
        console.log('暫時未檢測到語音，繼續等待...');
        shouldStop = false;
        showStatus('請開始說話...');
        return; // 不停止識別，繼續等待語音輸入
      case 'network':
        errorMessage = '網路連接問題，嘗試使用備用輸入';
        break;
      case 'aborted':
        errorMessage = '語音識別被中斷';
        break;
      default:
        errorMessage = `語音識別錯誤: ${event.error}`;
    }
    
    if (shouldStop) {
      isRecognizing = false;
      updateUI();
      showError(errorMessage);
      
      // 不自動切換到備用模式，讓用戶手動選擇
    }
  };
  
  window.speechRecognitionInstance.onend = function() {
    console.log('語音識別結束');
    console.log('結束時的 isRecognizing 狀態:', isRecognizing);
    console.log('是否曾經觸發啟動事件:', window.speechStartTriggered || false);
    isRecognizing = false;
    updateUI();
    showStatus(getTranslation('speech_ready') || '語音識別就緒');
    
    // 重置標記
    window.speechStartTriggered = false;
    window.speechDetected = false;
  };
}

// 暴露全域接口供其他模組使用（移到文件末尾統一定義）

// 綁定 UI 元素
function bindUIElements() {
  micButton = document.getElementById('voice-btn') || document.querySelector('.voice-btn');
  statusIndicator = document.getElementById('voice-status') || document.querySelector('.voice-status');
  resultContainer = document.getElementById('speech-result') || document.querySelector('.speech-result');
  orderInput = document.getElementById('user-input') || document.querySelector('#user-input');
  
  if (micButton) {
    micButton.addEventListener('click', toggleRecognition);
  }
}

// 切換語音識別狀態
function toggleRecognition() {
  if (isRecognizing) {
    stopRecognition();
  } else {
    startRecognition();
  }
}

// 開始語音識別
function startRecognition() {
  console.log('=== 開始語音識別流程 ===');
  console.log('調用堆疊:', new Error().stack);
  console.log('speechRecognitionInstance 存在:', !!window.speechRecognitionInstance);
  console.log('當前 isRecognizing 狀態:', isRecognizing);
  
  // 防止重複啟動 - 簡化檢查邏輯
  if (isRecognizing) {
    console.warn('語音識別已在進行中，忽略重複啟動請求');
    console.warn('isRecognizing:', isRecognizing);
    return;
  }

  if (!window.speechRecognitionInstance) {
    console.warn('語音識別實例不存在');
    showError('語音識別功能不可用');
    return;
  }
  
  // 檢查語音識別實例的狀態
  console.log('語音識別實例配置:');
  console.log('- continuous:', window.speechRecognitionInstance.continuous);
  console.log('- interimResults:', window.speechRecognitionInstance.interimResults);
  console.log('- lang:', window.speechRecognitionInstance.lang);
  console.log('- maxAlternatives:', window.speechRecognitionInstance.maxAlternatives);
  
  // 檢查麥克風權限
  if (navigator.permissions) {
    navigator.permissions.query({name: 'microphone'}).then(function(result) {
      console.log('麥克風權限狀態:', result.state);
      if (result.state === 'denied') {
        console.error('麥克風權限被拒絕');
        showError('麥克風權限被拒絕，請在瀏覽器設定中允許麥克風存取');
        isRecognizing = false;
        updateUI();
        return;
      }
    }).catch(function(error) {
      console.warn('無法檢查麥克風權限:', error);
    });
  }
  
  // 先嘗試獲取麥克風權限
  console.log('嘗試獲取麥克風權限...');
  navigator.mediaDevices.getUserMedia({ audio: true })
    .then(function(stream) {
      console.log('麥克風權限獲取成功');
      // 立即停止媒體流，我們只需要權限
      stream.getTracks().forEach(track => track.stop());
      
      // 現在嘗試啟動語音識別
      try {
        console.log('嘗試啟動語音識別...');

        // 強制停止任何現有的語音識別實例
        try {
          window.speechRecognitionInstance.abort();
          console.log('已強制停止現有的語音識別實例');
        } catch (abortError) {
          console.log('停止現有實例時發生錯誤（可能沒有運行中的實例）:', abortError.message);
        }

        // 重置標記
        window.speechStartTriggered = false;
        window.speechDetected = false;

        // 等待一小段時間讓實例完全重置
        setTimeout(() => {
          try {
            // 設置語音識別開始時間
            lastSpeechTime = Date.now();

            // 啟動語音識別
            window.speechRecognitionInstance.start();
            console.log('語音識別 start() 方法已調用');
            showStatus(getTranslation('speech_listening') || '正在聆聽...');
            updateUI();

            // 3秒後如果還沒有檢測到語音，給出提示
            setTimeout(() => {
              if (isRecognizing && !window.speechDetected) {
                showStatus('🔊 請大聲清楚地說話...');
              }
            }, 3000);

          } catch (startError) {
            console.error('延遲啟動語音識別失敗:', startError);
            handleSpeechStartError(startError);
          }
        }, 200); // 增加等待時間確保實例完全重置

      } catch (error) {
        console.error('啟動語音識別失敗:', error);
        handleSpeechStartError(error);
      }
    })
    .catch(function(error) {
      console.error('獲取麥克風權限失敗:', error);
      isRecognizing = false;
      updateUI();
      
      if (error.name === 'NotAllowedError') {
        showError('麥克風權限被拒絕，請在瀏覽器設定中允許麥克風存取');
      } else if (error.name === 'NotFoundError') {
        showError('未找到麥克風設備，請檢查硬體連接');
      } else {
        showError(`麥克風存取失敗: ${error.message}`);
      }
      
      // 不自動切換到備用服務
    });
  
  // 移除原來的 try-catch，因為已經整合到上面的 promise 中
}

// 統一的語音啟動錯誤處理函數
function handleSpeechStartError(error) {
  console.error('錯誤詳情:', error.name, error.message);

  // 重置狀態
  isRecognizing = false;
  updateUI();

  // 根據錯誤類型提供不同的處理
  if (error.name === 'InvalidStateError') {
    console.warn('語音識別狀態錯誤，建議重新整理頁面');
    showError('語音識別狀態異常，請重新整理頁面後再試');
  } else if (error.name === 'NotAllowedError') {
    showError('麥克風權限被拒絕，請允許使用麥克風');
  } else if (error.name === 'ServiceNotAllowedError') {
    showError('語音識別服務不可用，請檢查網路連線');
  } else {
    showError('語音識別啟動失敗: ' + error.message);
  }

  // 如果原生語音失敗，嘗試使用備用服務
  if (window.fallbackSpeechUI) {
    setTimeout(() => {
      console.log('嘗試切換到備用語音服務');
      window.fallbackSpeechUI.showInterface();
    }, 1000);
  }
}

// 停止語音識別
function stopRecognition() {
  if (!window.speechRecognitionInstance) return;
  
  // 清除靜默計時器
  if (silenceTimer) {
    clearTimeout(silenceTimer);
    silenceTimer = null;
  }
  
  try {
    if (isRecognizing) {
      window.speechRecognitionInstance.stop();
      isRecognizing = false;
      lastSpeechTime = null;
      
      // 重置所有狀態標記
      window.speechStartTriggered = false;
      window.speechDetected = false;
      
      showStatus(getTranslation('speech_stopped') || '已停止聆聽');
      updateUI();
    }
  } catch (error) {
    console.error('停止語音識別失敗:', error);
    isRecognizing = false;
    lastSpeechTime = null;
    
    // 即使出錯也要重置狀態標記
    window.speechStartTriggered = false;
    window.speechDetected = false;
    
    updateUI();
  }
}

// 顯示中間結果
function showInterimResult(text) {
  if (resultContainer) {
    resultContainer.innerHTML = `<span class="interim-result">${text}</span>`;
  }
}

// 處理最終結果
function finalizeResult(text) {
  if (resultContainer) {
    resultContainer.innerHTML = `<span class="final-result">${text}</span>`;
  }
  
  // 如果有訂單輸入框，將識別結果填入
  if (orderInput) {
    orderInput.value = text;
    
    // 觸發輸入事件，以便其他依賴輸入的功能可以響應
    const inputEvent = new Event('input', { bubbles: true });
    orderInput.dispatchEvent(inputEvent);
  }
  
  // 自動提交訂單（可選功能，根據需要啟用或禁用）
  // autoSubmitOrder();
}

// 顯示語音識別狀態
function showStatus(message) {
  if (statusIndicator) {
    statusIndicator.textContent = message;
  }
}

// 顯示錯誤信息
function showError(message) {
  if (statusIndicator) {
    statusIndicator.textContent = message;
    statusIndicator.classList.add('error');
    
    // 3 秒後清除錯誤狀態
    setTimeout(() => {
      statusIndicator.classList.remove('error');
    }, 3000);
  }
}

// 更新 UI 以反映當前狀態
function updateUI() {
  if (micButton) {
    if (isRecognizing) {
      micButton.classList.add('recording');
      micButton.innerHTML = '<i class="fas fa-stop"></i>';
      micButton.title = getTranslation('click_stop_speech') || '點擊停止語音識別';
    } else {
      micButton.classList.remove('recording');
      micButton.innerHTML = '<i class="fas fa-microphone"></i>';
      micButton.title = getTranslation('click_start_speech') || '點擊開始語音識別';
    }
  }
}

// 自動提交訂單
function autoSubmitOrder() {
  const orderForm = document.getElementById('order-form');
  
  if (orderForm && orderInput && orderInput.value.trim()) {
    // 延遲提交以便用戶有時間檢查識別結果
    setTimeout(() => {
      orderForm.submit();
    }, 1500);
  }
}

// 禁用語音功能
function disableSpeechFeatures() {
  const speechElements = document.querySelectorAll('.speech-feature');
  speechElements.forEach(element => {
    element.classList.add('disabled');
    element.title = '您的瀏覽器不支援語音識別功能';
    
    // 禁用按钮
    if (element.tagName === 'BUTTON') {
      element.disabled = true;
    }
  });
  
  showStatus('語音識別不可用 - 瀏覽器不支援');
}

// 在頁面加載完成後初始化語音識別
document.addEventListener('DOMContentLoaded', function() {
  // 初始化語音識別功能
  initSpeechRecognition();
});

// 初始化備用語音服務
function initFallbackSpeechService() {
  console.log('初始化備用語音服務');
  
  // 綁定 UI 元素
  bindUIElements();
  
  // 修改麥克風按鈕行為
  if (micButton) {
    // 移除之前的事件監聽器，避免重複綁定
    micButton.removeEventListener('click', toggleRecognition);
    
    // 添加備用語音服務的事件監聽器
    micButton.addEventListener('click', function() {
      if (window.FallbackSpeechUI) {
        window.FallbackSpeechUI.show(function(result) {
          if (result && result.trim()) {
            finalizeResult(result);
          }
        });
      } else {
        showError('備用語音服務不可用');
      }
    });
    
    // 更新按鈕樣式和提示
    micButton.innerHTML = '<i class="fas fa-keyboard"></i>';
    micButton.title = getTranslation('click_text_input') || '點擊進行文字輸入';
    micButton.classList.add('fallback-mode');
  }
  
  showStatus(getTranslation('fallback_speech_ready') || '備用語音服務已就緒');
}

// 檢查是否使用備用語音服務
function isFallbackMode() {
  return micButton && micButton.classList.contains('fallback-mode');
}

// 導出函數供其他腳本使用
window.voiceRecognition = {
  start: startRecognition,
  stop: stopRecognition,
  toggle: toggleRecognition,
  isRecognizing: function() { return isRecognizing; },
  isFallbackMode: isFallbackMode
};

console.log('語音識別模組已載入，全域接口已暴露:', Object.keys(window.voiceRecognition));

// 導出備用語音初始化函數
window.initFallbackSpeech = initFallbackSpeechService;
