#!/usr/bin/env pwsh
# Docker 構建和部署腳本
# 適用於 Windows PowerShell

# 設定錯誤處理
$ErrorActionPreference = "Stop"

Write-Host "=== 自然語言點餐系統 Docker 部署腳本 ===" -ForegroundColor Green

# 檢查 Docker 是否安裝
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "錯誤: 未找到 Docker，請先安裝 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 檢查 Docker Compose 是否可用
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "錯誤: 未找到 Docker Compose，請先安裝 Docker Compose" -ForegroundColor Red
    exit 1
}

# 檢查 .env 文件是否存在
if (-not (Test-Path ".env")) {
    Write-Host "警告: 未找到 .env 文件，正在複製 .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "已創建 .env 文件，請編輯此文件並填入您的 Firebase 配置" -ForegroundColor Cyan
        Write-Host "編輯完成後請重新運行此腳本" -ForegroundColor Cyan
        exit 0
    } else {
        Write-Host "錯誤: 未找到 .env.example 文件" -ForegroundColor Red
        exit 1
    }
}

# 創建必要的目錄
Write-Host "創建數據目錄..." -ForegroundColor Blue
if (-not (Test-Path "uploads")) {
    New-Item -ItemType Directory -Path "uploads" -Force | Out-Null
    Write-Host "已創建 uploads 目錄" -ForegroundColor Green
}

if (-not (Test-Path "appPrompt")) {
    New-Item -ItemType Directory -Path "appPrompt" -Force | Out-Null
    Write-Host "已創建 appPrompt 目錄" -ForegroundColor Green
}

# 顯示選項菜單
Write-Host ""
Write-Host "請選擇操作:" -ForegroundColor Cyan
Write-Host "1. 構建並啟動服務 (docker-compose up -d --build)" -ForegroundColor White
Write-Host "2. 啟動服務 (docker-compose up -d)" -ForegroundColor White
Write-Host "3. 停止服務 (docker-compose down)" -ForegroundColor White
Write-Host "4. 查看服務狀態 (docker-compose ps)" -ForegroundColor White
Write-Host "5. 查看日誌 (docker-compose logs -f)" -ForegroundColor White
Write-Host "6. 重啟服務 (docker-compose restart)" -ForegroundColor White
Write-Host "7. 完全重建 (停止→清理→無快取重建→啟動)" -ForegroundColor White
Write-Host "8. 清理所有容器和映像 (docker-compose down --rmi all)" -ForegroundColor White
Write-Host "9. 退出" -ForegroundColor White

$choice = Read-Host "請輸入選項 (1-9)"

if ($choice -eq "1") {
    Write-Host "正在構建並啟動服務..." -ForegroundColor Blue
    docker-compose up -d --build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服務已成功啟動!" -ForegroundColor Green
        Write-Host "訪問地址: http://localhost:3003" -ForegroundColor Cyan
    } else {
        Write-Host "服務啟動失敗，請檢查日誌" -ForegroundColor Red
        exit 1
    }
}
elseif ($choice -eq "2") {
    Write-Host "正在啟動服務..." -ForegroundColor Blue
    docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服務已成功啟動!" -ForegroundColor Green
        Write-Host "訪問地址: http://localhost:3003" -ForegroundColor Cyan
    } else {
        Write-Host "服務啟動失敗，請檢查日誌" -ForegroundColor Red
        exit 1
    }
}
elseif ($choice -eq "3") {
    Write-Host "正在停止服務..." -ForegroundColor Blue
    docker-compose down
    Write-Host "服務已停止" -ForegroundColor Green
}
elseif ($choice -eq "4") {
    Write-Host "服務狀態:" -ForegroundColor Blue
    docker-compose ps
}
elseif ($choice -eq "5") {
    Write-Host "顯示日誌 (按 Ctrl+C 退出):" -ForegroundColor Blue
    docker-compose logs -f
}
elseif ($choice -eq "6") {
    Write-Host "正在重啟服務..." -ForegroundColor Blue
    docker-compose restart
    Write-Host "服務已重啟" -ForegroundColor Green
}
elseif ($choice -eq "7") {
    Write-Host "正在執行完全重建..." -ForegroundColor Blue
    
    # 停止現有容器
    Write-Host "1. 停止現有容器..." -ForegroundColor Cyan
    docker-compose down
    
    # 清理未使用的映像
    Write-Host "2. 清理未使用的映像..." -ForegroundColor Cyan
    docker image prune -f
    
    # 無快取重建
    Write-Host "3. 無快取重建映像..." -ForegroundColor Cyan
    docker-compose build --no-cache
    
    # 啟動服務
    Write-Host "4. 啟動服務..." -ForegroundColor Cyan
    docker-compose up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "完全重建成功!" -ForegroundColor Green
        Write-Host "訪問地址: http://localhost:3003" -ForegroundColor Cyan
    } else {
        Write-Host "服務啟動失敗，請檢查日誌" -ForegroundColor Red
        exit 1
    }
}
elseif ($choice -eq "8") {
    Write-Host "警告: 這將刪除所有相關的容器和映像!" -ForegroundColor Red
    $confirm = Read-Host "確定要繼續嗎? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        Write-Host "正在清理所有容器和映像..." -ForegroundColor Blue
        docker-compose down --rmi all --volumes --remove-orphans
        Write-Host "清理完成" -ForegroundColor Green
    } else {
        Write-Host "已取消清理操作" -ForegroundColor Yellow
    }
}
elseif ($choice -eq "9") {
    Write-Host "退出腳本" -ForegroundColor Green
    exit 0
}
else {
    Write-Host "無效選項，請重新運行腳本" -ForegroundColor Red
    exit 1
}

Write-Host "`n腳本執行完成" -ForegroundColor Green