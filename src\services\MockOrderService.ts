/**
 * MockOrderService.ts
 * 提供模擬的訂單服務功能，在 Firebase 權限問題解決前使用
 */
// 移除 Firebase Timestamp 依賴，使用原生 Date
// import { Timestamp } from '../config/firebase.js';
import { createLogger } from '../utils/Logger.js';
import { Order, OrderItem, OrderStatus } from '../types/entities/Order.js';

const logger = createLogger('MockOrderService');

// 用於存儲臨時訂單數據
const mockOrders: Record<string, Order> = {};
let lastOrderId = 0;

/**
 * 生成唯一ID
 */
function generateOrderId(): string {
  lastOrderId++;
  const timestamp = Date.now();
  return `mock-order-${timestamp}-${lastOrderId}`;
}

export class MockOrderService {
  async createOrder(items: OrderItem[], userId: string | null = null): Promise<Order> {
    try {
      // 記錄收到的原始訂單項目
      logger.info('收到訂單項目', { itemCount: items.length });
      
      // 合併相同商品的訂單項目
      const mergedItems = this.mergeOrderItems(items);
      logger.debug('合併後的訂單項目', { mergedItemCount: mergedItems.length });
      
      // 計算總金額
      const totalAmount = mergedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        // 創建訂單數據基本結構
      const orderData: Order = {
        items: mergedItems,
        totalAmount,
        status: OrderStatus.PENDING,
        timestamp: new Date()  // 使用 JavaScript Date 物件代替 Firestore Timestamp
      };
      
      // 只有在 userId 有實際值且為字串類型時才添加到訂單數據中
      if (userId && typeof userId === 'string' && userId.trim() !== '') {
        orderData.userId = userId.trim();        logger.info('添加有效的 userId 到訂單', { userId });
      } else {
        logger.debug('未添加 userId 到訂單，值無效或為空', { userId });
      }
      
      // 生成訂單 ID 並儲存
      const orderId = generateOrderId();
      mockOrders[orderId] = orderData;
      
      logger.info('訂單已成功創建', { orderId });
      
      return { ...orderData, id: orderId };
    } catch (error) {
      logger.error('處理訂單時出錯', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
  
  // 合併相同商品的訂單項目並過濾測試數據
  private mergeOrderItems(items: OrderItem[]): OrderItem[] {
    // 先過濾無效和測試數據
    const validItems = items.filter(item => {
      // 檢查是否有任何有效的名稱欄位
      const itemName = item.name || item.name_en || item.name_jp || item.name_zh;
      return item && 
        itemName && 
        typeof itemName === 'string' &&
        // 排除純數字名稱（可能是測試數據）
        !/^\d+$/.test(itemName.trim()) &&
        typeof item.quantity === 'number' && item.quantity > 0 &&
        typeof item.price === 'number' && item.price >= 0;
    });
    
    logger.debug('過濾並合併訂單項目', { 
      validItemCount: validItems.length, 
      originalItemCount: items.length 
    });
    
    // 使用 Map 根據商品名稱合併相同的商品
    const itemMap = new Map<string, OrderItem>();
    
    for (const item of validItems) {
      // 獲取有效的名稱欄位並清理，移除多餘的換行和空格
      const itemName = item.name || item.name_en || item.name_jp || item.name_zh;
      if (!itemName) {
        logger.warn('項目缺少有效的名稱欄位', { item });
        continue;
      }
      const cleanName = itemName.replace(/\n\s+/g, ' ').trim();
      
      if (itemMap.has(cleanName)) {
        // 如果已存在相同名稱的商品，增加數量
        const existingItem = itemMap.get(cleanName)!;
        existingItem.quantity += item.quantity;
      } else {
        // 否則添加新項目，保持原始的多語言欄位結構，但確保有 name 欄位用於相容性
        const mergedItem = { ...item };
        if (!mergedItem.name) {
          mergedItem.name = cleanName;
        }
        itemMap.set(cleanName, mergedItem);
      }
    }
    
    // 將 Map 轉換為數組並返回
    return Array.from(itemMap.values());
  }
  
  // 確認訂單
  async confirmOrder(orderId: string): Promise<Order> {
    if (!mockOrders[orderId]) {
      throw new Error(`[模擬服務] 訂單不存在: ${orderId}`);
    }
      // 更新訂單狀態
    mockOrders[orderId].status = OrderStatus.CONFIRMED;
    
    // 模擬1分鐘後開始準備訂單
    setTimeout(() => {
      if (mockOrders[orderId]) {
        mockOrders[orderId].status = OrderStatus.PREPARING;
        logger.info('訂單狀態更新', { orderId, status: 'preparing' });
      }
    }, 60000);
    
    return { ...mockOrders[orderId], id: orderId };
  }
  
  // 獲取訂單資訊
  async getOrder(orderId: string): Promise<Order | null> {
    const order = mockOrders[orderId];
    
    if (!order) {
      return null;
    }
    
    return { ...order, id: orderId };
  }
    // 更新訂單狀態
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    if (!mockOrders[orderId]) {
      throw new Error(`[模擬服務] 訂單不存在: ${orderId}`);
    }
    
    mockOrders[orderId].status = status;
  }
}

export default new MockOrderService();
