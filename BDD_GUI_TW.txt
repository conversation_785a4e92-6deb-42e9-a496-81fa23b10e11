Feature: 自然語言點餐系統 (含GUI介面需求)
  As a customer
  I want to order food using natural language (text or voice) with intuitive GUI interface
  So that I can place my order quickly and intuitively with great user experience

# ========== 原有功能場景 ==========

Scenario: 顧客使用文字輸入點餐
  Given a customer is on the ordering page
  When the customer enters "我想點一個大麥克套餐和一杯玉米湯" into the natural language input field
  Then the system should identify "大麥克套餐" and "玉米湯"
  And query the database/RAG for details (name, price, image, availability) of "大麥克套餐" and "玉米湯"
  And present the identified items and their details back to the customer using natural language for confirmation (e.g., "您是想點一份大麥克套餐和一份玉米湯嗎？")

Scenario: 顧客使用語音輸入點餐
  Given a customer is on the ordering page
  When the customer uses voice input saying "我要一個麥脆雞兩塊餐"
  Then the system should transcribe the voice input to text
  And identify "麥脆雞兩塊餐"
  And query the database/RAG for details (name, price, image, availability) of "麥脆雞兩塊餐"
  And present the identified item and its details back to the customer using natural language for confirmation (e.g., "您選的是一份麥脆雞兩塊餐，對嗎？")

Scenario: 顧客修改點餐內容
  Given the system has presented the identified items for confirmation
  When the customer says "玉米湯換成可樂"
  Then the system should remove "玉米湯" from the proposed order
  And query the database/RAG for details of "可樂"
  And present the modified order (大麥克套餐, 可樂) back to the customer using natural language for confirmation

Scenario: 系統無法識別餐點
  Given a customer is on the ordering page
  When the customer enters "來一份神秘魔法漢堡"
  Then the system should indicate that the item "神秘魔法漢堡" was not found
  And suggest checking the menu or rephrasing the request

Scenario: 顧客修改點餐內容 - 增加品項
  Given the system has presented the identified items for confirmation (大麥克套餐)
  When the customer says "再加一份薯條"
  Then the system should identify "薯條"
  And query the database/RAG for details of "薯條"
  And add "薯條" to the proposed order
  And present the updated order (大麥克套餐, 薯條) back to the customer using natural language for confirmation (e.g., "好的，幫您加了一份薯條，現在是包含大麥克套餐和薯條，請問確認嗎？")

Scenario: 顧客修改點餐內容 - 移除品項
  Given the system has presented the identified items for confirmation (大麥克套餐, 玉米湯)
  When the customer says "玉米湯不要了"
  Then the system should remove "玉米湯" from the proposed order
  And present the updated order (大麥克套餐) back to the customer using natural language for confirmation (e.g., "好的，已經移除玉米湯，目前是只有大麥克套餐，請問確認嗎？")

Scenario: 系統無法識別部分餐點
  Given a customer is on the ordering page
  When the customer enters "我要一個吉士漢堡和一個不知道什麼派"
  Then the system should identify "吉士漢堡"
  And indicate that the item "不知道什麼派" was not found
  And suggest checking the menu or rephrasing the request for the unknown item

Scenario: 顧客詢問餐點資訊
  Given a customer is on the ordering page
  When the customer enters "大麥克是什麼"
  Then the system should query the database/RAG for details of "大麥克" (description, ingredients, etc.)
  And present the information about "大麥克" to the customer using natural language (e.g., "大麥克是一款經典的雙層牛肉漢堡，包含...")

Scenario: 顧客確認訂單
  Given the system has presented the final order for confirmation (大麥克套餐, 可樂)
  When the customer says "確認" 或 "沒問題"
  Then the system should process the order
  And display an order confirmation message (e.g., "您的訂單已送出！")

Scenario: 顧客取消訂單
  Given the system has presented the identified items for confirmation (大麥克套餐, 玉米湯)
  When the customer says "取消" 或 "我不要了"
  Then the system should cancel the current order
  And display a message indicating the cancellation (e.g., "好的，您的訂單已取消。")

# ========== GUI介面需求場景 ==========

Scenario: 主頁面GUI載入顯示
  Given 顧客訪問點餐系統網址
  When 主頁面載入完成
  Then 應該顯示清晰的品牌標題和歡迎訊息
  And 顯示語言切換按鈕（繁中/英文/日文）
  And 顯示大型的自然語言輸入框，並附有提示文字「請輸入您想要的餐點...」
  And 顯示語音輸入按鈕，圖示清楚易懂
  And 顯示「開始點餐」按鈕，顏色醒目
  And 整體介面應該簡潔、現代化，適合觸控操作

Scenario: 自然語言輸入框GUI互動
  Given 顧客在主頁面
  When 顧客點擊自然語言輸入框
  Then 輸入框應該有明顯的焦點狀態（邊框高亮）
  And 顯示輸入提示範例「例如：我要一個大麥克套餐」
  And 支援多行文字輸入
  And 字體大小適中，易於閱讀
  And 輸入框應該自動調整高度以適應內容

Scenario: 語音輸入GUI狀態顯示
  Given 顧客在點餐頁面
  When 顧客點擊語音輸入按鈕
  Then 按鈕應該變為錄音狀態（紅色圓點動畫）
  And 顯示「正在聆聽...」的文字提示
  And 顯示音量波形動畫
  And 提供「停止錄音」按鈕
  And 錄音完成後顯示「正在處理語音...」的載入動畫

Scenario: 餐點識別結果GUI顯示
  Given 系統已識別顧客輸入的餐點
  When 顯示識別結果
  Then 每個識別的餐點應該以卡片形式顯示
  And 卡片包含：餐點圖片、名稱、價格、簡短描述
  And 顯示數量調整按鈕（+ / -）
  And 每個卡片有「移除」按鈕
  And 底部顯示總價格，字體醒目
  And 顯示「確認訂單」和「繼續點餐」按鈕

Scenario: 繼續點餐功能GUI行為
  Given 顧客已經選擇了一些餐點並看到識別結果
  When 顧客點擊「繼續點餐」按鈕
  Then 系統應該保留當前已選擇的餐點
  And 清空輸入框準備接受新的點餐輸入
  And 顯示提示訊息「已保留當前餐點，請繼續輸入新的餐點」
  And 當顧客輸入新餐點並提交後
  And 系統應該將新餐點與之前保留的餐點合併顯示
  And 重新計算並顯示總價格
  And 繼續提供「確認訂單」和「繼續點餐」選項

Scenario: 餐點尺寸選擇GUI處理
  Given 顧客輸入的餐點沒有明確指定尺寸（如「玉米濃湯」而非「大份玉米濃湯」）
  When 系統識別到該餐點有多種尺寸選項
  Then 系統應該顯示尺寸選擇界面
  And 列出所有可用的尺寸選項（如小份、大份）
  And 每個選項顯示對應的價格
  And 提供明確的選擇按鈕或下拉選單
  And 顯示「請選擇尺寸」的提示訊息
  And 等待顧客選擇後才將餐點加入訂單
  And 如果顧客未選擇，不應該自動默認為最小尺寸

Scenario: 訂單確認頁面GUI設計
  Given 顧客確認要下訂單
  When 進入訂單確認頁面
  Then 顯示完整的訂單摘要列表
  And 每項餐點顯示：名稱、數量、單價、小計
  And 顯示總金額，字體大且醒目
  And 顯示預估準備時間
  And 提供「確認下單」按鈕（綠色，醒目）
  And 提供「返回修改」按鈕（灰色）
  And 顯示付款方式選擇（現金/信用卡等）

Scenario: 錯誤訊息GUI顯示
  Given 系統無法識別餐點或發生錯誤
  When 需要顯示錯誤訊息
  Then 錯誤訊息應該以明顯但不刺眼的方式顯示
  And 使用溫和的紅色或橙色背景
  And 提供具體的錯誤說明和建議
  And 顯示「重試」按鈕
  And 錯誤訊息應該在幾秒後自動消失

Scenario: 載入狀態GUI顯示
  Given 系統正在處理顧客請求
  When 需要等待處理時間
  Then 顯示載入動畫（旋轉圓圈或進度條）
  And 顯示處理狀態文字「正在識別餐點...」
  And 載入動畫應該流暢不卡頓
  And 提供取消按鈕以中斷長時間處理

Scenario: 響應式設計GUI適配
  Given 顧客使用不同設備訪問系統
  When 在手機、平板、桌面電腦上瀏覽
  Then 介面應該自動適配螢幕尺寸
  And 按鈕大小適合觸控操作（最小44px）
  And 文字大小在各設備上都清晰可讀
  And 重要功能在小螢幕上仍然易於訪問
  And 橫向和直向模式都能正常顯示

Scenario: 無障礙設計GUI支援
  Given 有視覺或操作障礙的顧客使用系統
  When 使用輔助技術瀏覽
  Then 所有按鈕和輸入框都有適當的標籤
  And 支援鍵盤導航
  And 顏色對比度符合無障礙標準
  And 重要訊息不僅依賴顏色傳達
  And 支援螢幕閱讀器

Scenario: 多語言GUI切換
  Given 顧客想要切換介面語言
  When 點擊語言切換按鈕
  Then 顯示語言選項（繁體中文、English、日本語）
  And 切換後所有介面文字立即更新
  And 保持當前的點餐狀態不變
  And 語言偏好設定被記住
  And 餐點名稱也相應切換語言顯示