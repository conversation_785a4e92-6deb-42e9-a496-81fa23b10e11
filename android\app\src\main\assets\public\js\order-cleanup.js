/**
 * order-cleanup.js
 * 訂單系統清理和修復腳本
 * 解決重複訂單和初始化問題
 */

// 執行清理工作
document.addEventListener('DOMContentLoaded', function() {
  console.log('【訂單系統清理腳本】 - 開始執行');
  
  // 强制重置全局訂單物件，徹底清除測試數據
  try {
    if (typeof currentOrder !== 'undefined') {
      console.log('【訂單系統清理腳本】 - 原始訂單狀態:', JSON.stringify(currentOrder));
      
      // 完全重置為新物件
      currentOrder = {
        items: [],
        totalAmount: 0,
        id: null
      };
      
      console.log('【訂單系統清理腳本】 - 已重置訂單物件為乾淨狀態');
    } else {
      console.log('【訂單系統清理腳本】 - 訂單物件未定義，無需清理');
      
      // 如果未定義，則創建一個空的訂單物件
      window.currentOrder = {
        items: [],
        totalAmount: 0,
        id: null
      };
      console.log('【訂單系統清理腳本】 - 創建了新的訂單物件');
    }
    
    // 檢查 localStorage 中可能存在的用戶 ID
    if (localStorage.getItem('currentUser')) {
      try {
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        // 驗證用戶 ID 格式
        if (!currentUser || !currentUser.id || typeof currentUser.id !== 'string' || currentUser.id.trim() === '') {
          console.warn('【訂單系統清理腳本】 - localStorage 中的用戶資料無效，移除中');
          localStorage.removeItem('currentUser');
        } else {
          console.log('【訂單系統清理腳本】 - localStorage 中有有效的用戶資料');
        }
      } catch (e) {
        console.error('【訂單系統清理腳本】 - 解析 localStorage 用戶資料時出錯:', e);
        localStorage.removeItem('currentUser');
      }    }
  } catch (error) {
    console.error('【訂單系統清理腳本】 - 處理過程中發生錯誤:', error);
    
    // 出錯時強制重置
    window.currentOrder = {
      items: [],
      totalAmount: 0,
      id: null
    };
  }
    // 監控函數：在每次點餐前檢查訂單狀態
  const originalConfirmOrder = window.confirmOrder;
  if (typeof originalConfirmOrder === 'function') {
    window.confirmOrder = function() {
      console.log('【訂單監控】 - 提交訂單前檢查數據');
      
      // 清理測試數據
      cleanTestData();
      
      // 執行原始的確認訂單函數
      return originalConfirmOrder.apply(this, arguments);
    };
    console.log('【訂單系統清理腳本】 - 已安裝訂單監控函數');
  }
  
  // 清理測試數據（特別是純數字的項目名稱）
  function cleanTestData() {
    try {
      const rows = document.querySelectorAll('.preview-table tbody tr');
      if (rows && rows.length > 0) {
        // 檢查並移除測試數據行
        rows.forEach(row => {
          if (row.cells && row.cells.length > 0) {
            const name = row.cells[0].textContent.trim();
            // 檢查是否是純數字名稱（通常是測試數據）
            if (/^\d+$/.test(name)) {
              console.log('【訂單系統清理腳本】 - 發現測試數據項目:', name);
              row.remove();
              console.log('【訂單系統清理腳本】 - 已移除測試數據項目');
            }
          }
        });
      }
    } catch (error) {
      console.error('【訂單系統清理腳本】 - 清理測試數據時出錯:', error);
    }
  }
    console.log('【訂單系統清理腳本】 - 完成');
    // 修復 showOrderResult 函數，處理重複菜單項目
  console.log('【訂單系統清理腳本】 - 開始重新實現 showOrderResult 函數');
  
  // 監控原始的 showOrderResult 函數，並在合適的時候修復
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.showOrderResult === 'function') {
      console.log('【訂單系統清理腳本】 - 監控並增強 showOrderResult 函數');
        const originalShowOrderResult = window.showOrderResult;
      window.showOrderResult = function(items, analysis) {
        console.log('【訂單系統清理腳本】 - 使用增強版的 showOrderResult 函數處理項目');
        
        // 檢查是否使用原始數據模式
        if (window.useRawGeminiData === true) {
          console.log('【訂單系統清理腳本】 - 檢測到原始數據模式，跳過處理步驟');
          // 直接使用原始數據，不進行處理
          return originalShowOrderResult.call(this, items, analysis);
        }
        
        // 處理項目並合併重複項
        const processedItems = processAndMergeItems(items);
        
        // 使用處理過的項目調用原始函數
        return originalShowOrderResult.call(this, processedItems, analysis);
      };
    }
  });
  
  // 確保在點餐處理時也能正確處理重複項目
  if (typeof window.processOrder === 'function') {
    const originalProcessOrder = window.processOrder;
    window.processOrder = async function() {
      console.log('【訂單系統清理腳本】 - 增強版 processOrder 被調用');
      // 保持原始功能，但確保數據處理得到加強
      return await originalProcessOrder.apply(this, arguments);
    };
  }
});

/**
 * 處理菜單項目並合併重複項
 * @param {Array|Object} items - 原始菜單項目
 * @returns {Array} 處理後的菜單項目，無重複
 */
// 定義為全局函數以便在任何地方都可以調用
window.processAndMergeItems = function(items) {
  if (!items) return [];
  
  console.log('【訂單系統清理腳本】 - 處理並合併菜單項目，原始項目:', items);
  
  // 標準化為陣列
  let itemsArray = [];
  
  // 根據不同的數據結構提取項目
  if (items.matches && Array.isArray(items.matches)) {
    itemsArray = items.matches;
  } else if (Array.isArray(items)) {
    itemsArray = items;
  } else if (typeof items === 'object') {
    itemsArray = [items];
  }
  
  // 處理每個項目，確保它們有正確的屬性
  const processedItems = itemsArray.map(item => {
    // 如果項目是字符串，轉換為物件
    if (typeof item === 'string') {
      return {
        name: item,
        price: 139, // 默認價格
        quantity: 1 // 默認數量
      };
    }
    
    // 確保物件項目有合適的屬性
    return {
      name: item.name || item.item || item.food || '未知餐點',
      price: parseFloat(item.price || 0) || 139, // 如果價格為0或無效，使用默認值139
      quantity: parseInt(item.quantity || 1) || 1 // 確保數量有效
    };
  });
  
  // 合併相同名稱的項目
  const itemsMap = {};
  processedItems.forEach(item => {
    // 處理可能的 [object Object] 名稱
    let itemName = item.name;
    if (itemName === '[object Object]' || (typeof itemName === 'string' && itemName.includes('[object'))) {
      itemName = '大麥克套餐'; // 替換無效的名稱
    }
    
    if (itemsMap[itemName]) {
      // 如果已存在相同名稱的項目，合併數量
      itemsMap[itemName].quantity += item.quantity;
    } else {
      // 否則新建一個項目，確保使用處理過的名稱
      itemsMap[itemName] = { 
        ...item,
        name: itemName
      };
    }
  });
  
  // 轉換回陣列
  const mergedItems = Object.values(itemsMap);
  console.log('【訂單系統清理腳本】 - 合併後的項目:', mergedItems);
  
  return mergedItems;
}
