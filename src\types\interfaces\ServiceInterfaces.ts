/**
 * 服務層接口定義
 * 定義所有服務的標準接口，實現依賴反轉
 */

import { Order, OrderItem, CreateOrderRequest, UpdateOrderRequest } from '../entities/Order.js';
import { Menu, MenuItem, MenuCategory } from '../entities/MenuItem.js';
import { User, Session } from '../entities/User.js';
import { 
  NLPProcessRequestDto, 
  NLPProcessResponseDto,
  GeneratePromptRequestDto,
  GeneratePromptResponseDto 
} from '../dtos/NLPDto.js';

// 訂單服務接口
export interface IOrderService {
  createOrder(request: CreateOrderRequest): Promise<Order>;
  getOrder(orderId: string): Promise<Order | null>;
  updateOrder(orderId: string, request: UpdateOrderRequest): Promise<Order>;
  confirmOrder(orderId: string): Promise<Order>;
  cancelOrder(orderId: string): Promise<Order>;
  getOrdersByUser(userId: string): Promise<Order[]>;
  getOrdersByRestaurant(restaurantId: string): Promise<Order[]>;
}

// 菜單服務接口
export interface IMenuService {
  getMenu(restaurantId: string): Promise<Menu | null>;
  saveMenu(menu: Menu): Promise<string>;
  updateMenu(restaurantId: string, menu: Partial<Menu>): Promise<Menu>;
  deleteMenu(restaurantId: string): Promise<boolean>;
  searchMenuItems(restaurantId: string, query: string): Promise<MenuItem[]>;
  getMenuCategory(restaurantId: string, categoryId: string): Promise<MenuCategory | null>;
  validateMenu(menu: Menu): Promise<{ isValid: boolean; errors: string[] }>;
}

// NLP 服務接口
export interface INLPService {
  processOrder(request: NLPProcessRequestDto): Promise<NLPProcessResponseDto>;
  analyzeText(text: string, language?: string): Promise<any>;
  extractEntities(text: string): Promise<any[]>;
  detectLanguage(text: string): Promise<string>;
}

// 提示詞服務接口
export interface IPromptService {
  generatePrompt(request: GeneratePromptRequestDto): Promise<GeneratePromptResponseDto>;
  validatePrompt(prompt: string): Promise<{ isValid: boolean; errors: string[] }>;
  getPromptTemplate(type: string, language: string): Promise<string>;
  savePrompt(prompt: string, metadata: any): Promise<string>;
}

// 會話服務接口
export interface ISessionService {
  createSession(userId?: string): Promise<Session>;
  getSession(sessionId: string): Promise<Session | null>;
  updateSession(sessionId: string, updates: Partial<Session>): Promise<Session>;
  deleteSession(sessionId: string): Promise<boolean>;
  cleanupExpiredSessions(): Promise<number>;
}

// 用戶服務接口
export interface IUserService {
  getUser(userId: string): Promise<User | null>;
  createUser(userData: Partial<User>): Promise<User>;
  updateUser(userId: string, updates: Partial<User>): Promise<User>;
  deleteUser(userId: string): Promise<boolean>;
  getUserPreferences(userId: string): Promise<any>;
}

// 緩存服務接口
export interface ICacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<boolean>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<boolean>;
  exists(key: string): Promise<boolean>;
  getMany<T>(keys: string[]): Promise<(T | null)[]>;
  setMany<T>(items: { key: string; value: T; ttl?: number }[]): Promise<boolean>;
}

// 日誌服務接口
export interface ILoggerService {
  error(message: string, error?: Error, data?: any): void;
  warn(message: string, data?: any): void;
  info(message: string, data?: any): void;
  debug(message: string, data?: any): void;
  performance(operation: string, duration: number, data?: any): void;
  business(event: string, data?: any): void;
}

// 配置服務接口
export interface IConfigService {
  get<T>(key: string): T;
  set<T>(key: string, value: T): void;
  has(key: string): boolean;
  getAll(): Record<string, any>;
  reload(): Promise<void>;
}

// 健康檢查服務接口
export interface IHealthService {
  checkHealth(): Promise<{ status: string; details: any }>;
  checkDependency(name: string): Promise<{ status: string; latency: number }>;
  getSystemMetrics(): Promise<any>;
}

// 文件服務接口
export interface IFileService {
  upload(file: Buffer, filename: string, options?: any): Promise<string>;
  download(fileId: string): Promise<Buffer>;
  delete(fileId: string): Promise<boolean>;
  getMetadata(fileId: string): Promise<any>;
}

// 通知服務接口
export interface INotificationService {
  sendNotification(userId: string, message: string, type: string): Promise<boolean>;
  sendEmail(to: string, subject: string, content: string): Promise<boolean>;
  sendSMS(phone: string, message: string): Promise<boolean>;
}
