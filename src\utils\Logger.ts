/**
 * 統一日誌管理系統
 * 解決過多 console.log 和日誌管理不統一的問題
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

export interface LogEntry {
  timestamp: string;
  level: string;
  service: string;
  message: string;
  data?: any;
  error?: Error;
}

export class Logger {
  private static instance: Logger;
  private currentLevel: LogLevel;
  private serviceName: string;

  private constructor(serviceName: string = 'Unknown') {
    this.serviceName = serviceName;
    // 根據環境設定日誌級別
    this.currentLevel = this.getLogLevelFromEnv();
  }

  static getInstance(serviceName?: string): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(serviceName);
    }
    if (serviceName) {
      Logger.instance.serviceName = serviceName;
    }
    return Logger.instance;
  }

  private getLogLevelFromEnv(): LogLevel {
    const envLevel = process.env.LOG_LEVEL?.toUpperCase();
    switch (envLevel) {
      case 'ERROR': return LogLevel.ERROR;
      case 'WARN': return LogLevel.WARN;
      case 'INFO': return LogLevel.INFO;
      case 'DEBUG': return LogLevel.DEBUG;
      default: 
        // 生產環境默認 INFO，開發環境默認 DEBUG
        return process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;
    }
  }
  private formatLog(level: string, message: string, data?: any, error?: Error): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      service: this.serviceName,
      message,
      ...(data && { data }),
      ...(error && { error: { name: error.name, message: error.message, stack: error.stack } })
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.currentLevel;
  }

  private output(logEntry: LogEntry): void {
    const output = JSON.stringify(logEntry);
    
    // 根據日誌級別使用不同的輸出方法
    switch (logEntry.level) {
      case 'ERROR':
        console.error(output);
        break;
      case 'WARN':
        console.warn(output);
        break;
      case 'INFO':
        console.info(output);
        break;
      case 'DEBUG':
        console.log(output);
        break;
    }
  }

  error(message: string, error?: Error, data?: any): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.output(this.formatLog('ERROR', message, data, error));
    }
  }

  warn(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.output(this.formatLog('WARN', message, data));
    }
  }

  info(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.output(this.formatLog('INFO', message, data));
    }
  }

  debug(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.output(this.formatLog('DEBUG', message, data));
    }
  }

  // 用於性能監控的特殊方法
  performance(operation: string, duration: number, data?: any): void {
    this.info(`Performance: ${operation}`, { duration, ...data });
  }

  // 用於業務事件記錄
  business(event: string, data?: any): void {
    this.info(`Business Event: ${event}`, data);
  }
}

// 便利函數，用於快速創建特定服務的 Logger
export function createLogger(serviceName: string): Logger {
  return Logger.getInstance(serviceName);
}
