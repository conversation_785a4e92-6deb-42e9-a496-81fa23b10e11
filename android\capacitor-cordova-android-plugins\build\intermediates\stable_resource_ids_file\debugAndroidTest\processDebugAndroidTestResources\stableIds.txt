capacitor.cordova.android.plugins.test:styleable/View = 0x7f0e002c
capacitor.cordova.android.plugins.test:styleable/Toolbar = 0x7f0e002b
capacitor.cordova.android.plugins.test:styleable/StateListDrawableItem = 0x7f0e0028
capacitor.cordova.android.plugins.test:styleable/SearchView = 0x7f0e0025
capacitor.cordova.android.plugins.test:styleable/PopupWindow = 0x7f0e0022
capacitor.cordova.android.plugins.test:styleable/ListPopupWindow = 0x7f0e001e
capacitor.cordova.android.plugins.test:styleable/LinearLayoutCompat_Layout = 0x7f0e001d
capacitor.cordova.android.plugins.test:styleable/Fragment = 0x7f0e0018
capacitor.cordova.android.plugins.test:styleable/MenuView = 0x7f0e0021
capacitor.cordova.android.plugins.test:styleable/FontFamilyFont = 0x7f0e0017
capacitor.cordova.android.plugins.test:styleable/FontFamily = 0x7f0e0016
capacitor.cordova.android.plugins.test:styleable/ColorStateListItem = 0x7f0e0013
capacitor.cordova.android.plugins.test:styleable/Capability = 0x7f0e0011
capacitor.cordova.android.plugins.test:styleable/AppCompatTheme = 0x7f0e000f
capacitor.cordova.android.plugins.test:styleable/AppCompatTextView = 0x7f0e000e
capacitor.cordova.android.plugins.test:styleable/AppCompatTextHelper = 0x7f0e000d
capacitor.cordova.android.plugins.test:styleable/AppCompatSeekBar = 0x7f0e000c
capacitor.cordova.android.plugins.test:styleable/AppCompatImageView = 0x7f0e000b
capacitor.cordova.android.plugins.test:styleable/AppCompatEmojiHelper = 0x7f0e000a
capacitor.cordova.android.plugins.test:styleable/AnimatedStateListDrawableItem = 0x7f0e0008
capacitor.cordova.android.plugins.test:styleable/AnimatedStateListDrawableCompat = 0x7f0e0007
capacitor.cordova.android.plugins.test:styleable/AlertDialog = 0x7f0e0006
capacitor.cordova.android.plugins.test:styleable/ActionMode = 0x7f0e0004
capacitor.cordova.android.plugins.test:styleable/ActionBarLayout = 0x7f0e0001
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Toolbar = 0x7f0d015a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0d0159
capacitor.cordova.android.plugins.test:styleable/AnimatedStateListDrawableTransition = 0x7f0e0009
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.TextView = 0x7f0d0158
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Spinner.Underlined = 0x7f0d0157
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.SeekBar = 0x7f0d0152
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0d0151
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.SearchView = 0x7f0d0150
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0d014e
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.RatingBar = 0x7f0d014d
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0d014c
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0d0149
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ListView.Menu = 0x7f0d0147
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ListView.DropDown = 0x7f0d0146
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Spinner.DropDown = 0x7f0d0155
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.SearchView = 0x7f0d0141
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0d0140
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0d013c
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0d013b
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0d013a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0d0139
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0d0137
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionButton = 0x7f0d0136
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0d0131
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0d0130
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0d012e
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ImageButton = 0x7f0d012c
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0d012a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0d0127
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0d0126
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Button.Small = 0x7f0d0123
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Button.Colored = 0x7f0d0122
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0d0121
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0d0120
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0d0133
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Button.Borderless = 0x7f0d011f
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Button = 0x7f0d011e
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActivityChooserView = 0x7f0d011c
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0d011a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0d0119
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionBar.TabView = 0x7f0d0117
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionBar.Solid = 0x7f0d0114
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionBar = 0x7f0d0113
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0d0111
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0d010f
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.Dark = 0x7f0d010c
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat = 0x7f0d010a
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0d0105
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light = 0x7f0d0102
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Empty = 0x7f0d0101
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0d00ff
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Dialog = 0x7f0d00fd
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0d00fc
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0d00fb
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0d00fa
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight.Dialog = 0x7f0d00f8
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight = 0x7f0d00f6
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.CompactMenu = 0x7f0d00f5
capacitor.cordova.android.plugins.test:style/Theme.AppCompat = 0x7f0d00f4
capacitor.cordova.android.plugins.test:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0d00f1
capacitor.cordova.android.plugins.test:style/TextAppearance.Compat.Notification = 0x7f0d00ec
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0d00e7
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0d00e6
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0d00e1
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0d00df
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0d00de
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0d00dd
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0d00db
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0d00da
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0d00d7
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0d011d
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0d00d3
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0d00d0
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Menu = 0x7f0d00cf
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0d00cc
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0d00cb
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0d00ca
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0d00c9
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Display4 = 0x7f0d00c4
capacitor.cordova.android.plugins.test:style/TextAppearance.Compat.Notification.Title = 0x7f0d00f0
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Caption = 0x7f0d00c0
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Button = 0x7f0d00bf
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Body2 = 0x7f0d00be
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Body1 = 0x7f0d00bd
capacitor.cordova.android.plugins.test:styleable/ActionMenuItemView = 0x7f0e0002
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat = 0x7f0d00bc
capacitor.cordova.android.plugins.test:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0d00bb
capacitor.cordova.android.plugins.test:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0d00ba
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0d00b8
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0d00b5
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0d00b3
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0d00b2
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0d00b1
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0d00b0
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0d00ae
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0d00ac
capacitor.cordova.android.plugins.test:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0d00ab
capacitor.cordova.android.plugins.test:style/Platform.Widget.AppCompat.Spinner = 0x7f0d00aa
capacitor.cordova.android.plugins.test:style/Platform.V21.AppCompat.Light = 0x7f0d00a7
capacitor.cordova.android.plugins.test:style/Platform.V21.AppCompat = 0x7f0d00a6
capacitor.cordova.android.plugins.test:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0d00a5
capacitor.cordova.android.plugins.test:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0d00a4
capacitor.cordova.android.plugins.test:style/Platform.AppCompat.Light = 0x7f0d00a2
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Toolbar = 0x7f0d009f
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0d009e
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0d009c
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0d0098
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0d0095
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.RatingBar = 0x7f0d0094
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0d0090
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Headline = 0x7f0d00c5
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.PopupMenu = 0x7f0d008f
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0d015b
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ListView = 0x7f0d008c
capacitor.cordova.android.plugins.test:styleable/GradientColor = 0x7f0e001a
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ListMenuView = 0x7f0d008a
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0d0088
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0d0086
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0d0084
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0d0082
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0d007d
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0d007c
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0d007a
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0d0079
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Button.Small = 0x7f0d0077
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0d0074
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Button = 0x7f0d0072
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0d0071
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0d0070
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionMode = 0x7f0d006f
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0d006e
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0d0135
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0d006d
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0d0068
capacitor.cordova.android.plugins.test:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0d0066
capacitor.cordova.android.plugins.test:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0d0063
capacitor.cordova.android.plugins.test:style/Base.V7.Theme.AppCompat.Light = 0x7f0d0061
capacitor.cordova.android.plugins.test:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0d0060
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Subhead = 0x7f0d00d4
capacitor.cordova.android.plugins.test:style/Base.V7.Theme.AppCompat = 0x7f0d005f
capacitor.cordova.android.plugins.test:style/Base.V28.Theme.AppCompat.Light = 0x7f0d005e
capacitor.cordova.android.plugins.test:style/Base.V28.Theme.AppCompat = 0x7f0d005d
capacitor.cordova.android.plugins.test:styleable/CompoundButton = 0x7f0e0014
capacitor.cordova.android.plugins.test:style/Base.V26.Theme.AppCompat = 0x7f0d005a
capacitor.cordova.android.plugins.test:style/Base.V23.Theme.AppCompat = 0x7f0d0058
capacitor.cordova.android.plugins.test:style/Base.V22.Theme.AppCompat.Light = 0x7f0d0057
capacitor.cordova.android.plugins.test:style/Base.V22.Theme.AppCompat = 0x7f0d0056
capacitor.cordova.android.plugins.test:style/Base.V21.Theme.AppCompat.Light = 0x7f0d0053
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0d004b
capacitor.cordova.android.plugins.test:styleable/MenuItem = 0x7f0e0020
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat = 0x7f0d004a
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0d0047
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0d0046
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0d0045
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0d0041
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0d0040
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Dialog = 0x7f0d003e
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.CompactMenu = 0x7f0d003d
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat = 0x7f0d003c
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0d0038
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0d0037
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0d0036
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0d0035
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0d0093
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0d004d
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0d0034
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0d0030
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0d002d
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0d002c
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0d002b
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0d0029
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0d0023
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0d0022
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0d0020
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0d001f
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0d001e
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Inverse = 0x7f0d00c6
capacitor.cordova.android.plugins.test:style/Base.V23.Theme.AppCompat.Light = 0x7f0d0059
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Menu = 0x7f0d001d
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0d00f7
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0d0083
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0d001a
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0d0019
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0d0018
capacitor.cordova.android.plugins.test:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0d0052
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Large = 0x7f0d0017
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0d0016
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0d0014
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Button = 0x7f0d000f
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0d000d
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.SearchView = 0x7f0d0097
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat = 0x7f0d000c
capacitor.cordova.android.plugins.test:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0d000b
capacitor.cordova.android.plugins.test:style/Base.Animation.AppCompat.Tooltip = 0x7f0d0009
capacitor.cordova.android.plugins.test:style/Base.Animation.AppCompat.DropDownUp = 0x7f0d0008
capacitor.cordova.android.plugins.test:styleable/FragmentContainerView = 0x7f0e0019
capacitor.cordova.android.plugins.test:style/Base.Animation.AppCompat.Dialog = 0x7f0d0007
capacitor.cordova.android.plugins.test:style/Base.AlertDialog.AppCompat.Light = 0x7f0d0006
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.Button = 0x7f0d00e2
capacitor.cordova.android.plugins.test:style/Base.AlertDialog.AppCompat = 0x7f0d0005
capacitor.cordova.android.plugins.test:style/Animation.AppCompat.Tooltip = 0x7f0d0004
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.Light = 0x7f0d0112
capacitor.cordova.android.plugins.test:style/Animation.AppCompat.DropDownUp = 0x7f0d0003
capacitor.cordova.android.plugins.test:style/AlertDialog.AppCompat.Light = 0x7f0d0001
capacitor.cordova.android.plugins.test:string/status_bar_notification_info_overflow = 0x7f0c0024
capacitor.cordova.android.plugins.test:string/search_menu_title = 0x7f0c0023
capacitor.cordova.android.plugins.test:string/call_notification_ongoing_text = 0x7f0c0021
capacitor.cordova.android.plugins.test:string/call_notification_incoming_text = 0x7f0c0020
capacitor.cordova.android.plugins.test:string/call_notification_decline_action = 0x7f0c001e
capacitor.cordova.android.plugins.test:string/call_notification_answer_video_action = 0x7f0c001d
capacitor.cordova.android.plugins.test:string/abc_shareactionprovider_share_with_application = 0x7f0c0019
capacitor.cordova.android.plugins.test:string/abc_searchview_description_query = 0x7f0c0014
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.DayNight = 0x7f0d010e
capacitor.cordova.android.plugins.test:string/abc_search_hint = 0x7f0c0012
capacitor.cordova.android.plugins.test:string/abc_prepend_shortcut_label = 0x7f0c0011
capacitor.cordova.android.plugins.test:string/abc_menu_shift_shortcut_label = 0x7f0c000e
capacitor.cordova.android.plugins.test:string/abc_menu_meta_shortcut_label = 0x7f0c000d
capacitor.cordova.android.plugins.test:string/abc_menu_delete_shortcut_label = 0x7f0c000a
capacitor.cordova.android.plugins.test:string/abc_capital_off = 0x7f0c0006
capacitor.cordova.android.plugins.test:string/abc_activitychooserview_choose_application = 0x7f0c0005
capacitor.cordova.android.plugins.test:style/Animation.AppCompat.Dialog = 0x7f0d0002
capacitor.cordova.android.plugins.test:string/abc_action_mode_done = 0x7f0c0003
capacitor.cordova.android.plugins.test:string/abc_action_menu_overflow_description = 0x7f0c0002
capacitor.cordova.android.plugins.test:string/abc_action_bar_home_description = 0x7f0c0000
capacitor.cordova.android.plugins.test:layout/select_dialog_singlechoice_material = 0x7f0b0027
capacitor.cordova.android.plugins.test:layout/select_dialog_item_material = 0x7f0b0025
capacitor.cordova.android.plugins.test:layout/notification_template_part_time = 0x7f0b0024
capacitor.cordova.android.plugins.test:layout/notification_template_icon_group = 0x7f0b0022
capacitor.cordova.android.plugins.test:layout/notification_action = 0x7f0b001f
capacitor.cordova.android.plugins.test:layout/ime_base_split_test_activity = 0x7f0b001d
capacitor.cordova.android.plugins.test:layout/custom_dialog = 0x7f0b001c
capacitor.cordova.android.plugins.test:layout/abc_tooltip = 0x7f0b001b
capacitor.cordova.android.plugins.test:layout/abc_select_dialog_material = 0x7f0b001a
capacitor.cordova.android.plugins.test:layout/abc_search_view = 0x7f0b0019
capacitor.cordova.android.plugins.test:layout/abc_screen_toolbar = 0x7f0b0017
capacitor.cordova.android.plugins.test:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
capacitor.cordova.android.plugins.test:layout/abc_screen_simple = 0x7f0b0015
capacitor.cordova.android.plugins.test:layout/abc_list_menu_item_layout = 0x7f0b0010
capacitor.cordova.android.plugins.test:layout/abc_list_menu_item_icon = 0x7f0b000f
capacitor.cordova.android.plugins.test:layout/abc_list_menu_item_checkbox = 0x7f0b000e
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0d00e5
capacitor.cordova.android.plugins.test:layout/abc_expanded_menu_layout = 0x7f0b000d
capacitor.cordova.android.plugins.test:styleable/Spinner = 0x7f0e0026
capacitor.cordova.android.plugins.test:layout/abc_dialog_title_material = 0x7f0b000c
capacitor.cordova.android.plugins.test:layout/abc_cascading_menu_item_layout = 0x7f0b000b
capacitor.cordova.android.plugins.test:layout/abc_alert_dialog_title_material = 0x7f0b000a
capacitor.cordova.android.plugins.test:layout/abc_screen_content_include = 0x7f0b0014
capacitor.cordova.android.plugins.test:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
capacitor.cordova.android.plugins.test:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
capacitor.cordova.android.plugins.test:layout/abc_activity_chooser_view = 0x7f0b0006
capacitor.cordova.android.plugins.test:layout/abc_action_mode_close_item_material = 0x7f0b0005
capacitor.cordova.android.plugins.test:layout/abc_action_mode_bar = 0x7f0b0004
capacitor.cordova.android.plugins.test:layout/abc_action_menu_item_layout = 0x7f0b0002
capacitor.cordova.android.plugins.test:string/abc_activity_chooser_view_see_all = 0x7f0c0004
capacitor.cordova.android.plugins.test:layout/abc_action_bar_up_container = 0x7f0b0001
capacitor.cordova.android.plugins.test:interpolator/fast_out_slow_in = 0x7f0a0006
capacitor.cordova.android.plugins.test:layout/notification_template_custom_big = 0x7f0b0021
capacitor.cordova.android.plugins.test:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
capacitor.cordova.android.plugins.test:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
capacitor.cordova.android.plugins.test:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
capacitor.cordova.android.plugins.test:integer/status_bar_notification_info_maxnum = 0x7f090004
capacitor.cordova.android.plugins.test:styleable/ActionBar = 0x7f0e0000
capacitor.cordova.android.plugins.test:integer/abc_config_activityDefaultDur = 0x7f090000
capacitor.cordova.android.plugins.test:id/withText = 0x7f0800b2
capacitor.cordova.android.plugins.test:id/view_tree_view_model_store_owner = 0x7f0800b0
capacitor.cordova.android.plugins.test:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0800ae
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0d0039
capacitor.cordova.android.plugins.test:id/view_tree_lifecycle_owner = 0x7f0800ad
capacitor.cordova.android.plugins.test:id/useLogo = 0x7f0800ac
capacitor.cordova.android.plugins.test:id/up = 0x7f0800ab
capacitor.cordova.android.plugins.test:id/top = 0x7f0800a7
capacitor.cordova.android.plugins.test:id/titleDividerNoCustom = 0x7f0800a5
capacitor.cordova.android.plugins.test:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0d0054
capacitor.cordova.android.plugins.test:id/time = 0x7f0800a3
capacitor.cordova.android.plugins.test:id/textSpacerNoTitle = 0x7f0800a2
capacitor.cordova.android.plugins.test:id/textSpacerNoButtons = 0x7f0800a1
capacitor.cordova.android.plugins.test:id/text2 = 0x7f0800a0
capacitor.cordova.android.plugins.test:id/text = 0x7f08009f
capacitor.cordova.android.plugins.test:id/tag_window_insets_animation_callback = 0x7f08009e
capacitor.cordova.android.plugins.test:id/tag_state_description = 0x7f08009a
capacitor.cordova.android.plugins.test:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
capacitor.cordova.android.plugins.test:id/tag_screen_reader_focusable = 0x7f080099
capacitor.cordova.android.plugins.test:string/abc_menu_enter_shortcut_label = 0x7f0c000b
capacitor.cordova.android.plugins.test:drawable/abc_list_pressed_holo_dark = 0x7f070028
capacitor.cordova.android.plugins.test:id/tag_accessibility_pane_title = 0x7f080095
capacitor.cordova.android.plugins.test:id/submenuarrow = 0x7f08008f
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0d007e
capacitor.cordova.android.plugins.test:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
capacitor.cordova.android.plugins.test:attr/windowFixedHeightMinor = 0x7f03011b
capacitor.cordova.android.plugins.test:id/spacer = 0x7f080089
capacitor.cordova.android.plugins.test:attr/listPreferredItemHeight = 0x7f0300a8
capacitor.cordova.android.plugins.test:id/showTitle = 0x7f080088
capacitor.cordova.android.plugins.test:color/material_deep_teal_200 = 0x7f050038
capacitor.cordova.android.plugins.test:id/showHome = 0x7f080087
capacitor.cordova.android.plugins.test:id/tag_accessibility_actions = 0x7f080092
capacitor.cordova.android.plugins.test:id/shortcut = 0x7f080085
capacitor.cordova.android.plugins.test:id/search_src_text = 0x7f080082
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar = 0x7f0d012d
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0d0042
capacitor.cordova.android.plugins.test:dimen/abc_progress_bar_height_material = 0x7f060035
capacitor.cordova.android.plugins.test:drawable/notification_icon_background = 0x7f070064
capacitor.cordova.android.plugins.test:id/search_mag_icon = 0x7f080080
capacitor.cordova.android.plugins.test:id/search_edit_frame = 0x7f08007e
capacitor.cordova.android.plugins.test:id/search_button = 0x7f08007c
capacitor.cordova.android.plugins.test:id/search_bar = 0x7f08007b
capacitor.cordova.android.plugins.test:style/Platform.AppCompat = 0x7f0d00a1
capacitor.cordova.android.plugins.test:attr/autoSizeMaxTextSize = 0x7f03002e
capacitor.cordova.android.plugins.test:id/radio = 0x7f080072
capacitor.cordova.android.plugins.test:attr/titleMarginTop = 0x7f030107
capacitor.cordova.android.plugins.test:dimen/tooltip_precise_anchor_threshold = 0x7f060074
capacitor.cordova.android.plugins.test:id/progress_horizontal = 0x7f080071
capacitor.cordova.android.plugins.test:id/buttonPanel = 0x7f080041
capacitor.cordova.android.plugins.test:id/on = 0x7f08006e
capacitor.cordova.android.plugins.test:id/notification_background = 0x7f08006a
capacitor.cordova.android.plugins.test:id/notification_main_column = 0x7f08006b
capacitor.cordova.android.plugins.test:id/never = 0x7f080067
capacitor.cordova.android.plugins.test:id/multiply = 0x7f080066
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light = 0x7f0d0043
capacitor.cordova.android.plugins.test:id/message = 0x7f080064
capacitor.cordova.android.plugins.test:color/foreground_material_light = 0x7f050032
capacitor.cordova.android.plugins.test:id/listMode = 0x7f080062
capacitor.cordova.android.plugins.test:id/italic = 0x7f08005f
capacitor.cordova.android.plugins.test:color/notification_action_color_filter = 0x7f050041
capacitor.cordova.android.plugins.test:attr/colorBackgroundFloating = 0x7f030052
capacitor.cordova.android.plugins.test:id/fragment_container_view_tag = 0x7f080055
capacitor.cordova.android.plugins.test:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
capacitor.cordova.android.plugins.test:attr/actionBarTabStyle = 0x7f030007
capacitor.cordova.android.plugins.test:id/expanded_menu = 0x7f080053
capacitor.cordova.android.plugins.test:id/edit_text_id = 0x7f080050
capacitor.cordova.android.plugins.test:id/dialog_button = 0x7f08004d
capacitor.cordova.android.plugins.test:id/checked = 0x7f080044
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0d0050
capacitor.cordova.android.plugins.test:id/async = 0x7f08003d
capacitor.cordova.android.plugins.test:anim/abc_fade_out = 0x7f010001
capacitor.cordova.android.plugins.test:id/activity_chooser_view_content = 0x7f080039
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0d0125
capacitor.cordova.android.plugins.test:id/action_mode_close_button = 0x7f080036
capacitor.cordova.android.plugins.test:attr/toolbarStyle = 0x7f03010d
capacitor.cordova.android.plugins.test:id/action_mode_bar_stub = 0x7f080035
capacitor.cordova.android.plugins.test:dimen/hint_alpha_material_light = 0x7f06005e
capacitor.cordova.android.plugins.test:drawable/ic_call_decline_low = 0x7f07005c
capacitor.cordova.android.plugins.test:id/list_item = 0x7f080063
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Large = 0x7f0d00c7
capacitor.cordova.android.plugins.test:attr/allowStacking = 0x7f030028
capacitor.cordova.android.plugins.test:id/alertTitle = 0x7f08003b
capacitor.cordova.android.plugins.test:id/icon_group = 0x7f08005b
capacitor.cordova.android.plugins.test:drawable/btn_radio_off_mtrl = 0x7f070053
capacitor.cordova.android.plugins.test:id/action_menu_presenter = 0x7f080033
capacitor.cordova.android.plugins.test:id/action_image = 0x7f080031
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0d0128
capacitor.cordova.android.plugins.test:attr/textAppearanceLargePopupMenu = 0x7f0300ed
capacitor.cordova.android.plugins.test:id/action_divider = 0x7f080030
capacitor.cordova.android.plugins.test:id/action_context_bar = 0x7f08002f
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Caption = 0x7f0d0010
capacitor.cordova.android.plugins.test:string/call_notification_hang_up_action = 0x7f0c001f
capacitor.cordova.android.plugins.test:id/action_container = 0x7f08002e
capacitor.cordova.android.plugins.test:drawable/abc_spinner_mtrl_am_alpha = 0x7f07003d
capacitor.cordova.android.plugins.test:id/action_bar_subtitle = 0x7f08002c
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0d008e
capacitor.cordova.android.plugins.test:id/action_bar_spinner = 0x7f08002b
capacitor.cordova.android.plugins.test:attr/autoSizeTextType = 0x7f030032
capacitor.cordova.android.plugins.test:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f07004c
capacitor.cordova.android.plugins.test:id/expand_activities_button = 0x7f080052
capacitor.cordova.android.plugins.test:id/action_bar = 0x7f080027
capacitor.cordova.android.plugins.test:style/Base.V26.Theme.AppCompat.Light = 0x7f0d005b
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_8 = 0x7f080025
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Medium = 0x7f0d00cd
capacitor.cordova.android.plugins.test:attr/dividerPadding = 0x7f03006c
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_7 = 0x7f080024
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_30 = 0x7f08001f
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0d0085
capacitor.cordova.android.plugins.test:drawable/tooltip_frame_dark = 0x7f07006b
capacitor.cordova.android.plugins.test:id/action_bar_title = 0x7f08002d
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0d0153
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_29 = 0x7f08001d
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_27 = 0x7f08001b
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0d012f
capacitor.cordova.android.plugins.test:color/button_material_dark = 0x7f050027
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_24 = 0x7f080018
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_23 = 0x7f080017
capacitor.cordova.android.plugins.test:id/collapseActionView = 0x7f080046
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_19 = 0x7f080012
capacitor.cordova.android.plugins.test:id/action_bar_container = 0x7f080029
capacitor.cordova.android.plugins.test:drawable/notification_oversize_large_icon_bg = 0x7f070065
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_31 = 0x7f080020
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_18 = 0x7f080011
capacitor.cordova.android.plugins.test:style/TextAppearance.Compat.Notification.Time = 0x7f0d00ef
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_17 = 0x7f080010
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0d010b
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_5 = 0x7f080022
capacitor.cordova.android.plugins.test:style/Base.V21.Theme.AppCompat = 0x7f0d0051
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_16 = 0x7f08000f
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_22 = 0x7f080016
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_15 = 0x7f08000e
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0d00b6
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_25 = 0x7f080019
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DialogWhenLarge = 0x7f0d0100
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0d003a
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_12 = 0x7f08000b
capacitor.cordova.android.plugins.test:id/action_bar_activity_content = 0x7f080028
capacitor.cordova.android.plugins.test:id/SYM = 0x7f080005
capacitor.cordova.android.plugins.test:id/FUNCTION = 0x7f080002
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
capacitor.cordova.android.plugins.test:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07004b
capacitor.cordova.android.plugins.test:drawable/test_level_drawable = 0x7f07006a
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0d0027
capacitor.cordova.android.plugins.test:attr/fontVariationSettings = 0x7f03008b
capacitor.cordova.android.plugins.test:color/abc_tint_edittext = 0x7f050015
capacitor.cordova.android.plugins.test:drawable/notification_bg_normal_pressed = 0x7f070063
capacitor.cordova.android.plugins.test:drawable/notification_bg_low_pressed = 0x7f070061
capacitor.cordova.android.plugins.test:drawable/abc_seekbar_track_material = 0x7f07003c
capacitor.cordova.android.plugins.test:drawable/notification_bg_low_normal = 0x7f070060
capacitor.cordova.android.plugins.test:drawable/notification_bg_low = 0x7f07005f
capacitor.cordova.android.plugins.test:drawable/ic_call_decline = 0x7f07005b
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.TextView = 0x7f0d009d
capacitor.cordova.android.plugins.test:drawable/ic_call_answer_video_low = 0x7f07005a
capacitor.cordova.android.plugins.test:style/Base.V7.Widget.AppCompat.EditText = 0x7f0d0065
capacitor.cordova.android.plugins.test:drawable/ic_call_answer_video = 0x7f070059
capacitor.cordova.android.plugins.test:drawable/btn_checkbox_unchecked_mtrl = 0x7f070051
capacitor.cordova.android.plugins.test:drawable/ic_call_answer_low = 0x7f070058
capacitor.cordova.android.plugins.test:drawable/btn_radio_on_mtrl = 0x7f070055
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0d00e8
capacitor.cordova.android.plugins.test:attr/colorPrimary = 0x7f030058
capacitor.cordova.android.plugins.test:attr/closeItemLayout = 0x7f03004d
capacitor.cordova.android.plugins.test:dimen/abc_dialog_corner_radius_material = 0x7f06001b
capacitor.cordova.android.plugins.test:drawable/notification_template_icon_low_bg = 0x7f070067
capacitor.cordova.android.plugins.test:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070052
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0d0115
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0d00e3
capacitor.cordova.android.plugins.test:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0d005c
capacitor.cordova.android.plugins.test:id/showCustom = 0x7f080086
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0d0049
capacitor.cordova.android.plugins.test:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070050
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0d003f
capacitor.cordova.android.plugins.test:attr/actionLayout = 0x7f03000d
capacitor.cordova.android.plugins.test:drawable/btn_checkbox_checked_mtrl = 0x7f07004f
capacitor.cordova.android.plugins.test:dimen/notification_content_margin_start = 0x7f060064
capacitor.cordova.android.plugins.test:drawable/abc_text_select_handle_middle_mtrl = 0x7f070047
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0d00b4
capacitor.cordova.android.plugins.test:layout/abc_popup_menu_item_layout = 0x7f0b0013
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_1 = 0x7f080008
capacitor.cordova.android.plugins.test:id/submit_area = 0x7f080090
capacitor.cordova.android.plugins.test:drawable/abc_tab_indicator_material = 0x7f070043
capacitor.cordova.android.plugins.test:attr/buttonTintMode = 0x7f030046
capacitor.cordova.android.plugins.test:id/group_divider = 0x7f080056
capacitor.cordova.android.plugins.test:drawable/abc_switch_track_mtrl_alpha = 0x7f070042
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0d0026
capacitor.cordova.android.plugins.test:id/src_atop = 0x7f08008c
capacitor.cordova.android.plugins.test:drawable/abc_star_half_black_48dp = 0x7f070040
capacitor.cordova.android.plugins.test:string/abc_menu_sym_shortcut_label = 0x7f0c0010
capacitor.cordova.android.plugins.test:drawable/abc_seekbar_tick_mark_material = 0x7f07003b
capacitor.cordova.android.plugins.test:dimen/notification_action_text_size = 0x7f060062
capacitor.cordova.android.plugins.test:dimen/notification_media_narrow_margin = 0x7f060068
capacitor.cordova.android.plugins.test:drawable/abc_seekbar_thumb_material = 0x7f07003a
capacitor.cordova.android.plugins.test:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070038
capacitor.cordova.android.plugins.test:dimen/abc_dialog_title_divider_material = 0x7f060026
capacitor.cordova.android.plugins.test:id/progress_circular = 0x7f080070
capacitor.cordova.android.plugins.test:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070037
capacitor.cordova.android.plugins.test:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070036
capacitor.cordova.android.plugins.test:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070035
capacitor.cordova.android.plugins.test:dimen/abc_disabled_alpha_material_dark = 0x7f060027
capacitor.cordova.android.plugins.test:color/secondary_text_disabled_material_light = 0x7f050050
capacitor.cordova.android.plugins.test:drawable/abc_ratingbar_small_material = 0x7f070034
capacitor.cordova.android.plugins.test:attr/closeIcon = 0x7f03004c
capacitor.cordova.android.plugins.test:drawable/abc_ratingbar_material = 0x7f070033
capacitor.cordova.android.plugins.test:drawable/abc_ratingbar_indicator_material = 0x7f070032
capacitor.cordova.android.plugins.test:drawable/abc_list_selector_holo_dark = 0x7f07002e
capacitor.cordova.android.plugins.test:attr/dialogTheme = 0x7f030068
capacitor.cordova.android.plugins.test:id/chronometer = 0x7f080045
capacitor.cordova.android.plugins.test:styleable/ButtonBarLayout = 0x7f0e0010
capacitor.cordova.android.plugins.test:drawable/abc_list_longpressed_holo = 0x7f070027
capacitor.cordova.android.plugins.test:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070049
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0d00b7
capacitor.cordova.android.plugins.test:drawable/abc_list_divider_mtrl_alpha = 0x7f070025
capacitor.cordova.android.plugins.test:id/center_vertical = 0x7f080042
capacitor.cordova.android.plugins.test:attr/submitBackground = 0x7f0300e2
capacitor.cordova.android.plugins.test:attr/ttcIndex = 0x7f030114
capacitor.cordova.android.plugins.test:drawable/abc_list_divider_material = 0x7f070024
capacitor.cordova.android.plugins.test:drawable/abc_item_background_holo_light = 0x7f070023
capacitor.cordova.android.plugins.test:drawable/abc_item_background_holo_dark = 0x7f070022
capacitor.cordova.android.plugins.test:attr/actionBarPopupTheme = 0x7f030002
capacitor.cordova.android.plugins.test:drawable/abc_ic_voice_search_api_material = 0x7f070021
capacitor.cordova.android.plugins.test:id/search_go_btn = 0x7f08007f
capacitor.cordova.android.plugins.test:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f07001f
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0d0013
capacitor.cordova.android.plugins.test:attr/colorAccent = 0x7f030051
capacitor.cordova.android.plugins.test:drawable/abc_list_selector_background_transition_holo_light = 0x7f07002b
capacitor.cordova.android.plugins.test:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001a
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0d00ce
capacitor.cordova.android.plugins.test:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
capacitor.cordova.android.plugins.test:drawable/abc_ic_go_search_api_material = 0x7f070019
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0d00dc
capacitor.cordova.android.plugins.test:drawable/abc_ic_clear_material = 0x7f070017
capacitor.cordova.android.plugins.test:string/abc_searchview_description_voice = 0x7f0c0017
capacitor.cordova.android.plugins.test:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070016
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0d0129
capacitor.cordova.android.plugins.test:drawable/abc_edit_text_material = 0x7f070014
capacitor.cordova.android.plugins.test:drawable/abc_dialog_material_background = 0x7f070013
capacitor.cordova.android.plugins.test:drawable/abc_cab_background_top_material = 0x7f070010
capacitor.cordova.android.plugins.test:styleable/GradientColorItem = 0x7f0e001b
capacitor.cordova.android.plugins.test:id/tag_accessibility_clickable_spans = 0x7f080093
capacitor.cordova.android.plugins.test:drawable/abc_cab_background_internal_bg = 0x7f07000f
capacitor.cordova.android.plugins.test:id/unchecked = 0x7f0800a9
capacitor.cordova.android.plugins.test:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000b
capacitor.cordova.android.plugins.test:drawable/abc_btn_radio_material = 0x7f070009
capacitor.cordova.android.plugins.test:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000e
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Dialog.Alert = 0x7f0d00fe
capacitor.cordova.android.plugins.test:drawable/abc_btn_colored_material = 0x7f070007
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0d00d5
capacitor.cordova.android.plugins.test:attr/tickMarkTintMode = 0x7f0300ff
capacitor.cordova.android.plugins.test:attr/popupMenuStyle = 0x7f0300c2
capacitor.cordova.android.plugins.test:drawable/abc_btn_check_material = 0x7f070003
capacitor.cordova.android.plugins.test:drawable/abc_btn_borderless_material = 0x7f070002
capacitor.cordova.android.plugins.test:attr/actionBarTabBarStyle = 0x7f030006
capacitor.cordova.android.plugins.test:drawable/abc_action_bar_item_background_material = 0x7f070001
capacitor.cordova.android.plugins.test:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
capacitor.cordova.android.plugins.test:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
capacitor.cordova.android.plugins.test:dimen/tooltip_y_offset_non_touch = 0x7f060076
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0d0107
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0d000e
capacitor.cordova.android.plugins.test:id/normal = 0x7f080069
capacitor.cordova.android.plugins.test:dimen/tooltip_vertical_padding = 0x7f060075
capacitor.cordova.android.plugins.test:drawable/abc_btn_default_mtrl_shape = 0x7f070008
capacitor.cordova.android.plugins.test:dimen/tooltip_precise_anchor_extra_offset = 0x7f060073
capacitor.cordova.android.plugins.test:dimen/tooltip_margin = 0x7f060072
capacitor.cordova.android.plugins.test:id/right_side = 0x7f080075
capacitor.cordova.android.plugins.test:dimen/tooltip_corner_radius = 0x7f060070
capacitor.cordova.android.plugins.test:dimen/notification_top_pad = 0x7f06006e
capacitor.cordova.android.plugins.test:id/middle = 0x7f080065
capacitor.cordova.android.plugins.test:dimen/notification_small_icon_background_padding = 0x7f06006b
capacitor.cordova.android.plugins.test:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0d00f3
capacitor.cordova.android.plugins.test:dimen/notification_right_icon_size = 0x7f060069
capacitor.cordova.android.plugins.test:styleable/SwitchCompat = 0x7f0e0029
capacitor.cordova.android.plugins.test:dimen/notification_main_column_padding_top = 0x7f060067
capacitor.cordova.android.plugins.test:dimen/notification_large_icon_width = 0x7f060066
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ListView = 0x7f0d0145
capacitor.cordova.android.plugins.test:attr/actionModeCloseContentDescription = 0x7f030012
capacitor.cordova.android.plugins.test:id/tag_on_receive_content_listener = 0x7f080097
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0d0028
capacitor.cordova.android.plugins.test:dimen/notification_large_icon_height = 0x7f060065
capacitor.cordova.android.plugins.test:color/primary_text_disabled_material_dark = 0x7f050049
capacitor.cordova.android.plugins.test:dimen/notification_action_icon_size = 0x7f060061
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light.Dialog = 0x7f0d0104
capacitor.cordova.android.plugins.test:id/uniform = 0x7f0800aa
capacitor.cordova.android.plugins.test:dimen/hint_pressed_alpha_material_light = 0x7f060060
capacitor.cordova.android.plugins.test:dimen/hint_alpha_material_dark = 0x7f06005d
capacitor.cordova.android.plugins.test:id/tag_unhandled_key_event_manager = 0x7f08009c
capacitor.cordova.android.plugins.test:id/action_mode_bar = 0x7f080034
capacitor.cordova.android.plugins.test:dimen/highlight_alpha_material_dark = 0x7f06005b
capacitor.cordova.android.plugins.test:id/decor_content_parent = 0x7f08004b
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_9 = 0x7f080026
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_28 = 0x7f08001c
capacitor.cordova.android.plugins.test:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070018
capacitor.cordova.android.plugins.test:dimen/notification_right_side_padding_top = 0x7f06006a
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0d0032
capacitor.cordova.android.plugins.test:dimen/compat_notification_large_icon_max_width = 0x7f060057
capacitor.cordova.android.plugins.test:attr/windowMinWidthMinor = 0x7f03011f
capacitor.cordova.android.plugins.test:dimen/compat_notification_large_icon_max_height = 0x7f060056
capacitor.cordova.android.plugins.test:drawable/abc_text_cursor_material = 0x7f070045
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ProgressBar = 0x7f0d014b
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0d0073
capacitor.cordova.android.plugins.test:dimen/compat_control_corner_material = 0x7f060055
capacitor.cordova.android.plugins.test:id/src_in = 0x7f08008d
capacitor.cordova.android.plugins.test:dimen/compat_button_inset_vertical_material = 0x7f060052
capacitor.cordova.android.plugins.test:dimen/abc_text_size_title_material = 0x7f06004f
capacitor.cordova.android.plugins.test:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070054
capacitor.cordova.android.plugins.test:string/abc_searchview_description_submit = 0x7f0c0016
capacitor.cordova.android.plugins.test:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_stacked_max_height = 0x7f060009
capacitor.cordova.android.plugins.test:dimen/abc_text_size_small_material = 0x7f06004c
capacitor.cordova.android.plugins.test:dimen/abc_text_size_menu_material = 0x7f06004b
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0d006b
capacitor.cordova.android.plugins.test:dimen/abc_text_size_medium_material = 0x7f060049
capacitor.cordova.android.plugins.test:styleable/LinearLayoutCompat = 0x7f0e001c
capacitor.cordova.android.plugins.test:layout/ime_secondary_split_test_activity = 0x7f0b001e
capacitor.cordova.android.plugins.test:id/scrollView = 0x7f080079
capacitor.cordova.android.plugins.test:dimen/abc_text_size_large_material = 0x7f060048
capacitor.cordova.android.plugins.test:dimen/abc_text_size_display_4_material = 0x7f060046
capacitor.cordova.android.plugins.test:dimen/abc_text_size_display_1_material = 0x7f060043
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_13 = 0x7f08000c
capacitor.cordova.android.plugins.test:dimen/abc_text_size_button_material = 0x7f060041
capacitor.cordova.android.plugins.test:dimen/abc_text_size_display_2_material = 0x7f060044
capacitor.cordova.android.plugins.test:dimen/abc_text_size_body_2_material = 0x7f060040
capacitor.cordova.android.plugins.test:id/homeAsUp = 0x7f080059
capacitor.cordova.android.plugins.test:attr/track = 0x7f030111
capacitor.cordova.android.plugins.test:dimen/abc_switch_padding = 0x7f06003e
capacitor.cordova.android.plugins.test:styleable/StateListDrawable = 0x7f0e0027
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0d0156
capacitor.cordova.android.plugins.test:dimen/abc_star_medium = 0x7f06003c
capacitor.cordova.android.plugins.test:dimen/notification_subtext_size = 0x7f06006d
capacitor.cordova.android.plugins.test:dimen/abc_star_big = 0x7f06003b
capacitor.cordova.android.plugins.test:dimen/abc_seekbar_track_background_height_material = 0x7f060038
capacitor.cordova.android.plugins.test:id/info = 0x7f08005e
capacitor.cordova.android.plugins.test:dimen/abc_search_view_preferred_width = 0x7f060037
capacitor.cordova.android.plugins.test:dimen/abc_panel_menu_list_width = 0x7f060034
capacitor.cordova.android.plugins.test:id/ALT = 0x7f080000
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0d0096
capacitor.cordova.android.plugins.test:attr/checkboxStyle = 0x7f03004a
capacitor.cordova.android.plugins.test:drawable/abc_spinner_textfield_background_material = 0x7f07003e
capacitor.cordova.android.plugins.test:dimen/abc_list_item_height_small_material = 0x7f060032
capacitor.cordova.android.plugins.test:dimen/abc_list_item_height_material = 0x7f060031
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.SeekBar = 0x7f0d0099
capacitor.cordova.android.plugins.test:id/right_icon = 0x7f080074
capacitor.cordova.android.plugins.test:id/hide_ime_id = 0x7f080057
capacitor.cordova.android.plugins.test:id/report_drawn = 0x7f080073
capacitor.cordova.android.plugins.test:dimen/abc_list_item_height_large_material = 0x7f060030
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.PopupWindow = 0x7f0d014a
capacitor.cordova.android.plugins.test:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
capacitor.cordova.android.plugins.test:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001e
capacitor.cordova.android.plugins.test:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
capacitor.cordova.android.plugins.test:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
capacitor.cordova.android.plugins.test:layout/support_simple_spinner_dropdown_item = 0x7f0b0028
capacitor.cordova.android.plugins.test:dimen/abc_dropdownitem_icon_width = 0x7f060029
capacitor.cordova.android.plugins.test:id/icon = 0x7f08005a
capacitor.cordova.android.plugins.test:id/beginning = 0x7f08003e
capacitor.cordova.android.plugins.test:dimen/abc_disabled_alpha_material_light = 0x7f060028
capacitor.cordova.android.plugins.test:style/AlertDialog.AppCompat = 0x7f0d0000
capacitor.cordova.android.plugins.test:attr/collapseIcon = 0x7f03004f
capacitor.cordova.android.plugins.test:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
capacitor.cordova.android.plugins.test:id/title = 0x7f0800a4
capacitor.cordova.android.plugins.test:dimen/abc_dialog_padding_top_material = 0x7f060025
capacitor.cordova.android.plugins.test:dimen/abc_dialog_min_width_minor = 0x7f060023
capacitor.cordova.android.plugins.test:string/abc_menu_ctrl_shortcut_label = 0x7f0c0009
capacitor.cordova.android.plugins.test:attr/buttonPanelSideLayout = 0x7f030042
capacitor.cordova.android.plugins.test:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
capacitor.cordova.android.plugins.test:id/search_voice_btn = 0x7f080083
capacitor.cordova.android.plugins.test:dimen/abc_control_inset_material = 0x7f060019
capacitor.cordova.android.plugins.test:id/topPanel = 0x7f0800a8
capacitor.cordova.android.plugins.test:dimen/abc_control_corner_material = 0x7f060018
capacitor.cordova.android.plugins.test:drawable/abc_vector_test = 0x7f07004e
capacitor.cordova.android.plugins.test:dimen/abc_config_prefDialogWidth = 0x7f060017
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.NoActionBar = 0x7f0d0109
capacitor.cordova.android.plugins.test:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
capacitor.cordova.android.plugins.test:dimen/abc_button_padding_horizontal_material = 0x7f060014
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0d0024
capacitor.cordova.android.plugins.test:attr/textColorAlertDialogListItem = 0x7f0300f5
capacitor.cordova.android.plugins.test:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0d013d
capacitor.cordova.android.plugins.test:id/search_plate = 0x7f080081
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Tooltip = 0x7f0d00d8
capacitor.cordova.android.plugins.test:dimen/abc_action_button_min_width_material = 0x7f06000e
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
capacitor.cordova.android.plugins.test:style/Platform.ThemeOverlay.AppCompat = 0x7f0d00a3
capacitor.cordova.android.plugins.test:attr/indeterminateProgressStyle = 0x7f030098
capacitor.cordova.android.plugins.test:attr/textColorSearchUrl = 0x7f0300f6
capacitor.cordova.android.plugins.test:id/parentPanel = 0x7f08006f
capacitor.cordova.android.plugins.test:drawable/abc_control_background_material = 0x7f070012
capacitor.cordova.android.plugins.test:id/tag_on_receive_content_mime_types = 0x7f080098
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_default_height_material = 0x7f060002
capacitor.cordova.android.plugins.test:attr/customNavigationLayout = 0x7f030064
capacitor.cordova.android.plugins.test:dimen/abc_text_size_subhead_material = 0x7f06004d
capacitor.cordova.android.plugins.test:color/material_deep_teal_500 = 0x7f050039
capacitor.cordova.android.plugins.test:id/tag_on_apply_window_listener = 0x7f080096
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0d00d9
capacitor.cordova.android.plugins.test:drawable/notification_bg = 0x7f07005e
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
capacitor.cordova.android.plugins.test:color/tooltip_background_light = 0x7f050058
capacitor.cordova.android.plugins.test:drawable/notification_tile_bg = 0x7f070068
capacitor.cordova.android.plugins.test:color/tooltip_background_dark = 0x7f050057
capacitor.cordova.android.plugins.test:attr/alertDialogCenterButtons = 0x7f030025
capacitor.cordova.android.plugins.test:dimen/notification_top_pad_large_text = 0x7f06006f
capacitor.cordova.android.plugins.test:layout/select_dialog_multichoice_material = 0x7f0b0026
capacitor.cordova.android.plugins.test:attr/actionBarItemBackground = 0x7f030001
capacitor.cordova.android.plugins.test:color/switch_thumb_normal_material_light = 0x7f050056
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Medium = 0x7f0d001b
capacitor.cordova.android.plugins.test:color/switch_thumb_normal_material_dark = 0x7f050055
capacitor.cordova.android.plugins.test:attr/iconTint = 0x7f030094
capacitor.cordova.android.plugins.test:color/switch_thumb_material_light = 0x7f050054
capacitor.cordova.android.plugins.test:dimen/abc_text_size_body_1_material = 0x7f06003f
capacitor.cordova.android.plugins.test:style/TextAppearance.Compat.Notification.Info = 0x7f0d00ed
capacitor.cordova.android.plugins.test:color/switch_thumb_material_dark = 0x7f050053
capacitor.cordova.android.plugins.test:color/switch_thumb_disabled_material_light = 0x7f050052
capacitor.cordova.android.plugins.test:drawable/abc_btn_radio_material_anim = 0x7f07000a
capacitor.cordova.android.plugins.test:id/customPanel = 0x7f08004a
capacitor.cordova.android.plugins.test:color/switch_thumb_disabled_material_dark = 0x7f050051
capacitor.cordova.android.plugins.test:drawable/abc_ic_ab_back_material = 0x7f070015
capacitor.cordova.android.plugins.test:color/secondary_text_default_material_light = 0x7f05004e
capacitor.cordova.android.plugins.test:attr/collapseContentDescription = 0x7f03004e
capacitor.cordova.android.plugins.test:attr/seekBarStyle = 0x7f0300d2
capacitor.cordova.android.plugins.test:color/ripple_material_dark = 0x7f05004b
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.EditText = 0x7f0d012b
capacitor.cordova.android.plugins.test:color/primary_text_default_material_light = 0x7f050048
capacitor.cordova.android.plugins.test:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000d
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0d010d
capacitor.cordova.android.plugins.test:attr/actionModeTheme = 0x7f03001d
capacitor.cordova.android.plugins.test:dimen/disabled_alpha_material_dark = 0x7f060058
capacitor.cordova.android.plugins.test:color/bright_foreground_material_dark = 0x7f050025
capacitor.cordova.android.plugins.test:id/disableHome = 0x7f08004e
capacitor.cordova.android.plugins.test:color/primary_dark_material_light = 0x7f050044
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_14 = 0x7f08000d
capacitor.cordova.android.plugins.test:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001d
capacitor.cordova.android.plugins.test:color/material_grey_900 = 0x7f050040
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionButton = 0x7f0d006c
capacitor.cordova.android.plugins.test:attr/switchStyle = 0x7f0300ea
capacitor.cordova.android.plugins.test:color/bright_foreground_inverse_material_dark = 0x7f050023
capacitor.cordova.android.plugins.test:color/material_grey_50 = 0x7f05003c
capacitor.cordova.android.plugins.test:string/call_notification_screening_text = 0x7f0c0022
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_26 = 0x7f08001a
capacitor.cordova.android.plugins.test:id/notification_main_column_container = 0x7f08006c
capacitor.cordova.android.plugins.test:id/always = 0x7f08003c
capacitor.cordova.android.plugins.test:dimen/abc_dialog_padding_material = 0x7f060024
capacitor.cordova.android.plugins.test:color/material_blue_grey_800 = 0x7f050035
capacitor.cordova.android.plugins.test:color/highlighted_text_material_light = 0x7f050034
capacitor.cordova.android.plugins.test:styleable/RecycleListView = 0x7f0e0024
capacitor.cordova.android.plugins.test:styleable/ActivityChooserView = 0x7f0e0005
capacitor.cordova.android.plugins.test:drawable/abc_list_selector_disabled_holo_dark = 0x7f07002c
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_4 = 0x7f080021
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Headline = 0x7f0d0015
capacitor.cordova.android.plugins.test:color/highlighted_text_material_dark = 0x7f050033
capacitor.cordova.android.plugins.test:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070000
capacitor.cordova.android.plugins.test:attr/textAppearanceListItemSmall = 0x7f0300f0
capacitor.cordova.android.plugins.test:color/call_notification_answer_color = 0x7f050029
capacitor.cordova.android.plugins.test:attr/activityChooserViewStyle = 0x7f030023
capacitor.cordova.android.plugins.test:color/error_color_material_light = 0x7f050030
capacitor.cordova.android.plugins.test:drawable/abc_switch_thumb_material = 0x7f070041
capacitor.cordova.android.plugins.test:color/primary_material_dark = 0x7f050045
capacitor.cordova.android.plugins.test:layout/abc_action_menu_layout = 0x7f0b0003
capacitor.cordova.android.plugins.test:drawable/abc_text_select_handle_left_mtrl = 0x7f070046
capacitor.cordova.android.plugins.test:color/button_material_light = 0x7f050028
capacitor.cordova.android.plugins.test:id/view_tree_saved_state_registry_owner = 0x7f0800af
capacitor.cordova.android.plugins.test:color/bright_foreground_disabled_material_light = 0x7f050022
capacitor.cordova.android.plugins.test:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0d0055
capacitor.cordova.android.plugins.test:color/background_material_light = 0x7f050020
capacitor.cordova.android.plugins.test:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_3 = 0x7f08001e
capacitor.cordova.android.plugins.test:color/abc_btn_colored_borderless_text_material = 0x7f050002
capacitor.cordova.android.plugins.test:color/background_floating_material_light = 0x7f05001e
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0d0106
capacitor.cordova.android.plugins.test:attr/actionDropDownStyle = 0x7f03000c
capacitor.cordova.android.plugins.test:attr/subtitleTextColor = 0x7f0300e5
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0d0142
capacitor.cordova.android.plugins.test:color/background_floating_material_dark = 0x7f05001d
capacitor.cordova.android.plugins.test:id/image = 0x7f08005d
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ImageButton = 0x7f0d0081
capacitor.cordova.android.plugins.test:color/dim_foreground_disabled_material_light = 0x7f05002c
capacitor.cordova.android.plugins.test:attr/arrowShaftLength = 0x7f03002c
capacitor.cordova.android.plugins.test:dimen/abc_control_padding_material = 0x7f06001a
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0d00e0
capacitor.cordova.android.plugins.test:color/androidx_core_ripple_material_light = 0x7f05001b
capacitor.cordova.android.plugins.test:dimen/abc_text_size_headline_material = 0x7f060047
capacitor.cordova.android.plugins.test:attr/titleMargins = 0x7f030108
capacitor.cordova.android.plugins.test:animator/fragment_open_exit = 0x7f020005
capacitor.cordova.android.plugins.test:color/accent_material_light = 0x7f05001a
capacitor.cordova.android.plugins.test:id/actions = 0x7f080038
capacitor.cordova.android.plugins.test:color/accent_material_dark = 0x7f050019
capacitor.cordova.android.plugins.test:color/abc_tint_default = 0x7f050014
capacitor.cordova.android.plugins.test:attr/autoSizePresetSizes = 0x7f030030
capacitor.cordova.android.plugins.test:animator/fragment_close_exit = 0x7f020001
capacitor.cordova.android.plugins.test:dimen/notification_small_icon_size_as_large = 0x7f06006c
capacitor.cordova.android.plugins.test:color/abc_tint_btn_checkable = 0x7f050013
capacitor.cordova.android.plugins.test:drawable/abc_btn_check_material_anim = 0x7f070004
capacitor.cordova.android.plugins.test:dimen/abc_button_inset_vertical_material = 0x7f060013
capacitor.cordova.android.plugins.test:color/abc_secondary_text_material_light = 0x7f050012
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0d008d
capacitor.cordova.android.plugins.test:string/abc_toolbar_collapse_description = 0x7f0c001a
capacitor.cordova.android.plugins.test:id/off = 0x7f08006d
capacitor.cordova.android.plugins.test:anim/abc_popup_enter = 0x7f010003
capacitor.cordova.android.plugins.test:color/abc_search_url_text_normal = 0x7f05000e
capacitor.cordova.android.plugins.test:attr/drawableSize = 0x7f030072
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.PopupMenu = 0x7f0d013f
capacitor.cordova.android.plugins.test:attr/menu = 0x7f0300b3
capacitor.cordova.android.plugins.test:color/foreground_material_dark = 0x7f050031
capacitor.cordova.android.plugins.test:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0d009a
capacitor.cordova.android.plugins.test:attr/listItemLayout = 0x7f0300a4
capacitor.cordova.android.plugins.test:id/none = 0x7f080068
capacitor.cordova.android.plugins.test:attr/viewInflaterClass = 0x7f030115
capacitor.cordova.android.plugins.test:color/abc_search_url_text = 0x7f05000d
capacitor.cordova.android.plugins.test:color/abc_primary_text_material_light = 0x7f05000c
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0d00d1
capacitor.cordova.android.plugins.test:attr/actionBarDivider = 0x7f030000
capacitor.cordova.android.plugins.test:attr/showDividers = 0x7f0300d7
capacitor.cordova.android.plugins.test:id/default_activity_button = 0x7f08004c
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0d0087
capacitor.cordova.android.plugins.test:color/abc_primary_text_disable_only_material_light = 0x7f05000a
capacitor.cordova.android.plugins.test:attr/subtitleTextAppearance = 0x7f0300e4
capacitor.cordova.android.plugins.test:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
capacitor.cordova.android.plugins.test:dimen/compat_button_padding_vertical_material = 0x7f060054
capacitor.cordova.android.plugins.test:dimen/abc_text_size_display_3_material = 0x7f060045
capacitor.cordova.android.plugins.test:attr/navigationContentDescription = 0x7f0300b5
capacitor.cordova.android.plugins.test:color/abc_primary_text_disable_only_material_dark = 0x7f050009
capacitor.cordova.android.plugins.test:color/primary_dark_material_dark = 0x7f050043
capacitor.cordova.android.plugins.test:dimen/notification_big_circle_margin = 0x7f060063
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Small = 0x7f0d0021
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_elevation_material = 0x7f060005
capacitor.cordova.android.plugins.test:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
capacitor.cordova.android.plugins.test:color/abc_hint_foreground_material_light = 0x7f050008
capacitor.cordova.android.plugins.test:id/custom = 0x7f080049
capacitor.cordova.android.plugins.test:attr/dividerHorizontal = 0x7f03006b
capacitor.cordova.android.plugins.test:attr/subMenuArrow = 0x7f0300e1
capacitor.cordova.android.plugins.test:color/abc_search_url_text_pressed = 0x7f05000f
capacitor.cordova.android.plugins.test:attr/actionBarSplitStyle = 0x7f030004
capacitor.cordova.android.plugins.test:color/abc_btn_colored_text_material = 0x7f050003
capacitor.cordova.android.plugins.test:attr/textAppearanceListItemSecondary = 0x7f0300ef
capacitor.cordova.android.plugins.test:color/abc_background_cache_hint_selector_material_light = 0x7f050001
capacitor.cordova.android.plugins.test:dimen/abc_alert_dialog_button_dimen = 0x7f060011
capacitor.cordova.android.plugins.test:id/select_dialog_listview = 0x7f080084
capacitor.cordova.android.plugins.test:color/abc_decor_view_status_guard = 0x7f050005
capacitor.cordova.android.plugins.test:string/abc_menu_alt_shortcut_label = 0x7f0c0008
capacitor.cordova.android.plugins.test:attr/listPreferredItemHeightSmall = 0x7f0300aa
capacitor.cordova.android.plugins.test:color/abc_tint_switch_track = 0x7f050018
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0d00ea
capacitor.cordova.android.plugins.test:bool/abc_action_bar_embed_tabs = 0x7f040000
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionButton = 0x7f0d0118
capacitor.cordova.android.plugins.test:color/notification_icon_bg_color = 0x7f050042
capacitor.cordova.android.plugins.test:attr/popupWindowStyle = 0x7f0300c4
capacitor.cordova.android.plugins.test:attr/contentInsetEnd = 0x7f03005d
capacitor.cordova.android.plugins.test:dimen/abc_dialog_fixed_width_major = 0x7f06001e
capacitor.cordova.android.plugins.test:color/material_blue_grey_950 = 0x7f050037
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_0 = 0x7f080007
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0d006a
capacitor.cordova.android.plugins.test:drawable/abc_list_focused_holo = 0x7f070026
capacitor.cordova.android.plugins.test:color/bright_foreground_inverse_material_light = 0x7f050024
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0d00e9
capacitor.cordova.android.plugins.test:attr/goIcon = 0x7f03008e
capacitor.cordova.android.plugins.test:attr/windowMinWidthMajor = 0x7f03011e
capacitor.cordova.android.plugins.test:styleable/ViewStubCompat = 0x7f0e002e
capacitor.cordova.android.plugins.test:attr/actionMenuTextAppearance = 0x7f03000e
capacitor.cordova.android.plugins.test:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
capacitor.cordova.android.plugins.test:attr/searchHintIcon = 0x7f0300cf
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_6 = 0x7f080023
capacitor.cordova.android.plugins.test:attr/queryHint = 0x7f0300c9
capacitor.cordova.android.plugins.test:drawable/abc_ic_search_api_material = 0x7f070020
capacitor.cordova.android.plugins.test:attr/windowActionModeOverlay = 0x7f030119
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_10 = 0x7f080009
capacitor.cordova.android.plugins.test:attr/iconTintMode = 0x7f030095
capacitor.cordova.android.plugins.test:attr/windowActionBarOverlay = 0x7f030118
capacitor.cordova.android.plugins.test:attr/voiceIcon = 0x7f030116
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
capacitor.cordova.android.plugins.test:string/abc_menu_function_shortcut_label = 0x7f0c000c
capacitor.cordova.android.plugins.test:attr/alphabeticModifiers = 0x7f03002a
capacitor.cordova.android.plugins.test:attr/trackTintMode = 0x7f030113
capacitor.cordova.android.plugins.test:attr/drawerArrowStyle = 0x7f030077
capacitor.cordova.android.plugins.test:attr/trackTint = 0x7f030112
capacitor.cordova.android.plugins.test:attr/tooltipText = 0x7f030110
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.PopupWindow = 0x7f0d0091
capacitor.cordova.android.plugins.test:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070030
capacitor.cordova.android.plugins.test:dimen/abc_text_size_title_material_toolbar = 0x7f060050
capacitor.cordova.android.plugins.test:attr/tooltipForegroundColor = 0x7f03010e
capacitor.cordova.android.plugins.test:color/abc_decor_view_status_guard_light = 0x7f050006
capacitor.cordova.android.plugins.test:attr/titleTextStyle = 0x7f03010b
capacitor.cordova.android.plugins.test:id/search_badge = 0x7f08007a
capacitor.cordova.android.plugins.test:id/action_text = 0x7f080037
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Title = 0x7f0d0025
capacitor.cordova.android.plugins.test:attr/colorSwitchThumbNormal = 0x7f03005a
capacitor.cordova.android.plugins.test:color/dim_foreground_material_dark = 0x7f05002d
capacitor.cordova.android.plugins.test:dimen/abc_edit_text_inset_top_material = 0x7f06002e
capacitor.cordova.android.plugins.test:attr/actionOverflowButtonStyle = 0x7f03001f
capacitor.cordova.android.plugins.test:attr/titleTextAppearance = 0x7f030109
capacitor.cordova.android.plugins.test:color/material_grey_100 = 0x7f05003a
capacitor.cordova.android.plugins.test:attr/imageButtonStyle = 0x7f030097
capacitor.cordova.android.plugins.test:attr/titleMarginStart = 0x7f030106
capacitor.cordova.android.plugins.test:attr/switchPadding = 0x7f0300e9
capacitor.cordova.android.plugins.test:color/primary_material_light = 0x7f050046
capacitor.cordova.android.plugins.test:attr/titleMargin = 0x7f030103
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0d002f
capacitor.cordova.android.plugins.test:dimen/tooltip_horizontal_padding = 0x7f060071
capacitor.cordova.android.plugins.test:attr/tickMarkTint = 0x7f0300fe
capacitor.cordova.android.plugins.test:attr/thumbTintMode = 0x7f0300fc
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0d00eb
capacitor.cordova.android.plugins.test:attr/thumbTint = 0x7f0300fb
capacitor.cordova.android.plugins.test:string/call_notification_answer_action = 0x7f0c001c
capacitor.cordova.android.plugins.test:attr/backgroundTintMode = 0x7f030037
capacitor.cordova.android.plugins.test:attr/thumbTextPadding = 0x7f0300fa
capacitor.cordova.android.plugins.test:attr/theme = 0x7f0300f8
capacitor.cordova.android.plugins.test:attr/contentInsetRight = 0x7f030060
capacitor.cordova.android.plugins.test:id/line1 = 0x7f080060
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0d002a
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0d0012
capacitor.cordova.android.plugins.test:id/home = 0x7f080058
capacitor.cordova.android.plugins.test:layout/abc_alert_dialog_material = 0x7f0b0009
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
capacitor.cordova.android.plugins.test:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
capacitor.cordova.android.plugins.test:attr/actionModePopupWindowStyle = 0x7f030018
capacitor.cordova.android.plugins.test:color/material_grey_850 = 0x7f05003f
capacitor.cordova.android.plugins.test:styleable/DrawerArrowToggle = 0x7f0e0015
capacitor.cordova.android.plugins.test:string/abc_searchview_description_search = 0x7f0c0015
capacitor.cordova.android.plugins.test:id/bottom = 0x7f080040
capacitor.cordova.android.plugins.test:attr/tickMark = 0x7f0300fd
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Button.Colored = 0x7f0d0076
capacitor.cordova.android.plugins.test:attr/divider = 0x7f03006a
capacitor.cordova.android.plugins.test:attr/font = 0x7f030081
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0d0134
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0d0103
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0d0033
capacitor.cordova.android.plugins.test:attr/textAppearanceSmallPopupMenu = 0x7f0300f4
capacitor.cordova.android.plugins.test:attr/listChoiceBackgroundIndicator = 0x7f0300a0
capacitor.cordova.android.plugins.test:attr/actionModeCopyDrawable = 0x7f030014
capacitor.cordova.android.plugins.test:attr/textAppearanceSearchResultSubtitle = 0x7f0300f2
capacitor.cordova.android.plugins.test:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
capacitor.cordova.android.plugins.test:drawable/notification_template_icon_bg = 0x7f070066
capacitor.cordova.android.plugins.test:attr/numericModifiers = 0x7f0300b9
capacitor.cordova.android.plugins.test:attr/switchTextAppearance = 0x7f0300eb
capacitor.cordova.android.plugins.test:attr/actionBarTheme = 0x7f030009
capacitor.cordova.android.plugins.test:color/abc_secondary_text_material_dark = 0x7f050011
capacitor.cordova.android.plugins.test:string/abc_searchview_description_clear = 0x7f0c0013
capacitor.cordova.android.plugins.test:color/abc_primary_text_material_dark = 0x7f05000b
capacitor.cordova.android.plugins.test:attr/subtitleTextStyle = 0x7f0300e6
capacitor.cordova.android.plugins.test:style/ThemeOverlay.AppCompat.Dialog = 0x7f0d0110
capacitor.cordova.android.plugins.test:dimen/abc_dialog_fixed_height_major = 0x7f06001c
capacitor.cordova.android.plugins.test:dimen/abc_action_button_min_height_material = 0x7f06000d
capacitor.cordova.android.plugins.test:color/bright_foreground_material_light = 0x7f050026
capacitor.cordova.android.plugins.test:attr/state_above_anchor = 0x7f0300e0
capacitor.cordova.android.plugins.test:id/action_menu_divider = 0x7f080032
capacitor.cordova.android.plugins.test:id/META = 0x7f080003
capacitor.cordova.android.plugins.test:color/primary_text_disabled_material_light = 0x7f05004a
capacitor.cordova.android.plugins.test:attr/srcCompat = 0x7f0300df
capacitor.cordova.android.plugins.test:attr/spinnerStyle = 0x7f0300dd
capacitor.cordova.android.plugins.test:styleable/TextAppearance = 0x7f0e002a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionMode = 0x7f0d011b
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
capacitor.cordova.android.plugins.test:id/src_over = 0x7f08008e
capacitor.cordova.android.plugins.test:anim/fragment_fast_out_extra_slow_in = 0x7f010018
capacitor.cordova.android.plugins.test:id/ifRoom = 0x7f08005c
capacitor.cordova.android.plugins.test:attr/buttonIconDimen = 0x7f030041
capacitor.cordova.android.plugins.test:attr/singleChoiceItemLayout = 0x7f0300da
capacitor.cordova.android.plugins.test:dimen/compat_button_inset_horizontal_material = 0x7f060051
capacitor.cordova.android.plugins.test:attr/showTitle = 0x7f0300d9
capacitor.cordova.android.plugins.test:attr/fontProviderAuthority = 0x7f030083
capacitor.cordova.android.plugins.test:attr/showAsAction = 0x7f0300d6
capacitor.cordova.android.plugins.test:id/tag_unhandled_key_listeners = 0x7f08009d
capacitor.cordova.android.plugins.test:attr/shortcutMatchRequired = 0x7f0300d5
capacitor.cordova.android.plugins.test:attr/selectableItemBackground = 0x7f0300d3
capacitor.cordova.android.plugins.test:attr/fontProviderSystemFontFamily = 0x7f030089
capacitor.cordova.android.plugins.test:color/primary_text_default_material_dark = 0x7f050047
capacitor.cordova.android.plugins.test:drawable/abc_popup_background_mtrl_mult = 0x7f070031
capacitor.cordova.android.plugins.test:anim/abc_slide_out_top = 0x7f010009
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0d00af
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0d0089
capacitor.cordova.android.plugins.test:attr/textAppearanceSearchResultTitle = 0x7f0300f3
capacitor.cordova.android.plugins.test:string/abc_menu_space_shortcut_label = 0x7f0c000f
capacitor.cordova.android.plugins.test:attr/radioButtonStyle = 0x7f0300cb
capacitor.cordova.android.plugins.test:attr/progressBarPadding = 0x7f0300c6
capacitor.cordova.android.plugins.test:drawable/notification_bg_normal = 0x7f070062
capacitor.cordova.android.plugins.test:id/tag_accessibility_heading = 0x7f080094
capacitor.cordova.android.plugins.test:style/Widget.Compat.NotificationActionText = 0x7f0d015d
capacitor.cordova.android.plugins.test:attr/preserveIconSpacing = 0x7f0300c5
capacitor.cordova.android.plugins.test:color/abc_search_url_text_selected = 0x7f050010
capacitor.cordova.android.plugins.test:id/scrollIndicatorUp = 0x7f080078
capacitor.cordova.android.plugins.test:attr/buttonBarStyle = 0x7f03003e
capacitor.cordova.android.plugins.test:attr/panelMenuListWidth = 0x7f0300c1
capacitor.cordova.android.plugins.test:drawable/abc_list_pressed_holo_light = 0x7f070029
capacitor.cordova.android.plugins.test:attr/panelMenuListTheme = 0x7f0300c0
capacitor.cordova.android.plugins.test:attr/dialogCornerRadius = 0x7f030066
capacitor.cordova.android.plugins.test:attr/paddingStart = 0x7f0300bd
capacitor.cordova.android.plugins.test:attr/panelBackground = 0x7f0300bf
capacitor.cordova.android.plugins.test:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
capacitor.cordova.android.plugins.test:style/TextAppearance.Compat.Notification.Line2 = 0x7f0d00ee
capacitor.cordova.android.plugins.test:drawable/abc_list_selector_disabled_holo_light = 0x7f07002d
capacitor.cordova.android.plugins.test:attr/paddingTopNoTitle = 0x7f0300be
capacitor.cordova.android.plugins.test:dimen/compat_button_padding_horizontal_material = 0x7f060053
capacitor.cordova.android.plugins.test:attr/tint = 0x7f030100
capacitor.cordova.android.plugins.test:attr/popupTheme = 0x7f0300c3
capacitor.cordova.android.plugins.test:id/SHIFT = 0x7f080004
capacitor.cordova.android.plugins.test:attr/overlapAnchor = 0x7f0300ba
capacitor.cordova.android.plugins.test:integer/cancel_button_image_alpha = 0x7f090002
capacitor.cordova.android.plugins.test:attr/nestedScrollViewStyle = 0x7f0300b8
capacitor.cordova.android.plugins.test:attr/showText = 0x7f0300d8
capacitor.cordova.android.plugins.test:attr/actionModePasteDrawable = 0x7f030017
capacitor.cordova.android.plugins.test:attr/navigationMode = 0x7f0300b7
capacitor.cordova.android.plugins.test:attr/ratingBarStyleIndicator = 0x7f0300cd
capacitor.cordova.android.plugins.test:attr/navigationIcon = 0x7f0300b6
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ListPopupWindow = 0x7f0d0144
capacitor.cordova.android.plugins.test:id/special_effects_controller_view_tag = 0x7f08008a
capacitor.cordova.android.plugins.test:attr/measureWithLargestChild = 0x7f0300b2
capacitor.cordova.android.plugins.test:attr/splitTrack = 0x7f0300de
capacitor.cordova.android.plugins.test:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070044
capacitor.cordova.android.plugins.test:attr/maxButtonHeight = 0x7f0300b1
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0d002e
capacitor.cordova.android.plugins.test:string/androidx_startup = 0x7f0c001b
capacitor.cordova.android.plugins.test:attr/fontProviderPackage = 0x7f030087
capacitor.cordova.android.plugins.test:attr/listPreferredItemPaddingRight = 0x7f0300ad
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0d0044
capacitor.cordova.android.plugins.test:attr/contentInsetLeft = 0x7f03005f
capacitor.cordova.android.plugins.test:color/abc_tint_spinner = 0x7f050017
capacitor.cordova.android.plugins.test:attr/listPopupWindowStyle = 0x7f0300a7
capacitor.cordova.android.plugins.test:dimen/abc_button_padding_vertical_material = 0x7f060015
capacitor.cordova.android.plugins.test:string/abc_action_bar_up_description = 0x7f0c0001
capacitor.cordova.android.plugins.test:attr/listMenuViewStyle = 0x7f0300a6
capacitor.cordova.android.plugins.test:attr/titleTextColor = 0x7f03010a
capacitor.cordova.android.plugins.test:attr/ratingBarStyleSmall = 0x7f0300ce
capacitor.cordova.android.plugins.test:id/contentPanel = 0x7f080048
capacitor.cordova.android.plugins.test:dimen/abc_text_size_caption_material = 0x7f060042
capacitor.cordova.android.plugins.test:color/material_grey_600 = 0x7f05003d
capacitor.cordova.android.plugins.test:attr/tooltipFrameBackground = 0x7f03010f
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0d00ad
capacitor.cordova.android.plugins.test:attr/alertDialogTheme = 0x7f030027
capacitor.cordova.android.plugins.test:attr/buttonGravity = 0x7f030040
capacitor.cordova.android.plugins.test:attr/listLayout = 0x7f0300a5
capacitor.cordova.android.plugins.test:attr/drawableTint = 0x7f030074
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0d0011
capacitor.cordova.android.plugins.test:color/background_material_dark = 0x7f05001f
capacitor.cordova.android.plugins.test:attr/listChoiceIndicatorMultipleAnimated = 0x7f0300a1
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0d008b
capacitor.cordova.android.plugins.test:dimen/hint_pressed_alpha_material_dark = 0x7f06005f
capacitor.cordova.android.plugins.test:color/secondary_text_disabled_material_dark = 0x7f05004f
capacitor.cordova.android.plugins.test:attr/lineHeight = 0x7f03009f
capacitor.cordova.android.plugins.test:attr/ratingBarStyle = 0x7f0300cc
capacitor.cordova.android.plugins.test:attr/listPreferredItemPaddingStart = 0x7f0300ae
capacitor.cordova.android.plugins.test:animator/fragment_close_enter = 0x7f020000
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ProgressBar = 0x7f0d0092
capacitor.cordova.android.plugins.test:attr/listDividerAlertDialog = 0x7f0300a3
capacitor.cordova.android.plugins.test:attr/suggestionRowLayout = 0x7f0300e7
capacitor.cordova.android.plugins.test:attr/layout = 0x7f03009e
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ActionBar.TabText = 0x7f0d0116
capacitor.cordova.android.plugins.test:attr/contentInsetStartWithNavigation = 0x7f030062
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0d001c
capacitor.cordova.android.plugins.test:attr/lastBaselineToBottomHeight = 0x7f03009d
capacitor.cordova.android.plugins.test:attr/windowFixedWidthMinor = 0x7f03011d
capacitor.cordova.android.plugins.test:attr/queryBackground = 0x7f0300c8
capacitor.cordova.android.plugins.test:color/material_grey_800 = 0x7f05003e
capacitor.cordova.android.plugins.test:attr/lStar = 0x7f03009c
capacitor.cordova.android.plugins.test:id/split_action_bar = 0x7f08008b
capacitor.cordova.android.plugins.test:drawable/abc_textfield_default_mtrl_alpha = 0x7f07004a
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.EditText = 0x7f0d0080
capacitor.cordova.android.plugins.test:attr/initialActivityCount = 0x7f030099
capacitor.cordova.android.plugins.test:attr/defaultQueryHint = 0x7f030065
capacitor.cordova.android.plugins.test:id/wrap_content = 0x7f0800b3
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_2 = 0x7f080013
capacitor.cordova.android.plugins.test:id/checkbox = 0x7f080043
capacitor.cordova.android.plugins.test:attr/commitIcon = 0x7f03005b
capacitor.cordova.android.plugins.test:attr/logoDescription = 0x7f0300b0
capacitor.cordova.android.plugins.test:attr/actionModeCloseDrawable = 0x7f030013
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.RatingBar.Small = 0x7f0d014f
capacitor.cordova.android.plugins.test:id/accessibility_action_clickable_span = 0x7f080006
capacitor.cordova.android.plugins.test:attr/homeLayout = 0x7f030092
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ButtonBar = 0x7f0d0078
capacitor.cordova.android.plugins.test:attr/homeAsUpIndicator = 0x7f030091
capacitor.cordova.android.plugins.test:attr/checkMarkTintMode = 0x7f030049
capacitor.cordova.android.plugins.test:dimen/abc_dialog_min_width_major = 0x7f060022
capacitor.cordova.android.plugins.test:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070006
capacitor.cordova.android.plugins.test:attr/firstBaselineToTopHeight = 0x7f030080
capacitor.cordova.android.plugins.test:attr/controlBackground = 0x7f030063
capacitor.cordova.android.plugins.test:attr/hideOnContentScroll = 0x7f030090
capacitor.cordova.android.plugins.test:id/line3 = 0x7f080061
capacitor.cordova.android.plugins.test:integer/config_tooltipAnimTime = 0x7f090003
capacitor.cordova.android.plugins.test:attr/fontWeight = 0x7f03008c
capacitor.cordova.android.plugins.test:attr/drawableStartCompat = 0x7f030073
capacitor.cordova.android.plugins.test:attr/fontStyle = 0x7f03008a
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0d00f9
capacitor.cordova.android.plugins.test:attr/textAllCaps = 0x7f0300ec
capacitor.cordova.android.plugins.test:id/edit_query = 0x7f08004f
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Title = 0x7f0d00d6
capacitor.cordova.android.plugins.test:attr/emojiCompatEnabled = 0x7f03007e
capacitor.cordova.android.plugins.test:attr/drawableEndCompat = 0x7f03006f
capacitor.cordova.android.plugins.test:attr/editTextColor = 0x7f03007b
capacitor.cordova.android.plugins.test:attr/icon = 0x7f030093
capacitor.cordova.android.plugins.test:id/tabMode = 0x7f080091
capacitor.cordova.android.plugins.test:anim/abc_slide_in_top = 0x7f010007
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ButtonBar = 0x7f0d0124
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Spinner = 0x7f0d009b
capacitor.cordova.android.plugins.test:attr/editTextBackground = 0x7f03007a
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0d0031
capacitor.cordova.android.plugins.test:id/end = 0x7f080051
capacitor.cordova.android.plugins.test:layout/abc_action_bar_title_item = 0x7f0b0000
capacitor.cordova.android.plugins.test:attr/windowActionBar = 0x7f030117
capacitor.cordova.android.plugins.test:attr/tintMode = 0x7f030101
capacitor.cordova.android.plugins.test:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001b
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0d00a0
capacitor.cordova.android.plugins.test:attr/actionBarWidgetTheme = 0x7f03000a
capacitor.cordova.android.plugins.test:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0d0064
capacitor.cordova.android.plugins.test:attr/drawableTopCompat = 0x7f030076
capacitor.cordova.android.plugins.test:color/secondary_text_default_material_dark = 0x7f05004d
capacitor.cordova.android.plugins.test:attr/backgroundStacked = 0x7f030035
capacitor.cordova.android.plugins.test:attr/actionModeBackground = 0x7f030010
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0d00c8
capacitor.cordova.android.plugins.test:attr/paddingEnd = 0x7f0300bc
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_11 = 0x7f08000a
capacitor.cordova.android.plugins.test:attr/drawableTintMode = 0x7f030075
capacitor.cordova.android.plugins.test:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
capacitor.cordova.android.plugins.test:styleable/ActionMenuView = 0x7f0e0003
capacitor.cordova.android.plugins.test:attr/progressBarStyle = 0x7f0300c7
capacitor.cordova.android.plugins.test:attr/buttonCompat = 0x7f03003f
capacitor.cordova.android.plugins.test:attr/actionModeShareDrawable = 0x7f03001a
capacitor.cordova.android.plugins.test:attr/drawableLeftCompat = 0x7f030070
capacitor.cordova.android.plugins.test:attr/colorButtonNormal = 0x7f030053
capacitor.cordova.android.plugins.test:attr/drawableBottomCompat = 0x7f03006e
capacitor.cordova.android.plugins.test:attr/gapBetweenBars = 0x7f03008d
capacitor.cordova.android.plugins.test:attr/elevation = 0x7f03007d
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0d013e
capacitor.cordova.android.plugins.test:anim/abc_fade_in = 0x7f010000
capacitor.cordova.android.plugins.test:attr/paddingBottomNoButtons = 0x7f0300bb
capacitor.cordova.android.plugins.test:attr/contentInsetStart = 0x7f030061
capacitor.cordova.android.plugins.test:attr/dialogPreferredPadding = 0x7f030067
capacitor.cordova.android.plugins.test:color/error_color_material_dark = 0x7f05002f
capacitor.cordova.android.plugins.test:id/tag_transition_group = 0x7f08009b
capacitor.cordova.android.plugins.test:attr/contentDescription = 0x7f03005c
capacitor.cordova.android.plugins.test:layout/notification_action_tombstone = 0x7f0b0020
capacitor.cordova.android.plugins.test:attr/autoSizeMinTextSize = 0x7f03002f
capacitor.cordova.android.plugins.test:color/bright_foreground_disabled_material_dark = 0x7f050021
capacitor.cordova.android.plugins.test:attr/colorPrimaryDark = 0x7f030059
capacitor.cordova.android.plugins.test:id/search_close_btn = 0x7f08007d
capacitor.cordova.android.plugins.test:dimen/highlight_alpha_material_light = 0x7f06005c
capacitor.cordova.android.plugins.test:attr/backgroundSplit = 0x7f030034
capacitor.cordova.android.plugins.test:color/dim_foreground_disabled_material_dark = 0x7f05002b
capacitor.cordova.android.plugins.test:attr/textAppearancePopupMenuHeader = 0x7f0300f1
capacitor.cordova.android.plugins.test:attr/colorControlHighlight = 0x7f030055
capacitor.cordova.android.plugins.test:dimen/highlight_alpha_material_colored = 0x7f06005a
capacitor.cordova.android.plugins.test:id/scrollIndicatorDown = 0x7f080077
capacitor.cordova.android.plugins.test:attr/colorControlActivated = 0x7f030054
capacitor.cordova.android.plugins.test:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Small = 0x7f0d00d2
capacitor.cordova.android.plugins.test:attr/height = 0x7f03008f
capacitor.cordova.android.plugins.test:attr/titleMarginBottom = 0x7f030104
capacitor.cordova.android.plugins.test:integer/abc_config_activityShortDur = 0x7f090001
capacitor.cordova.android.plugins.test:attr/windowFixedWidthMajor = 0x7f03011c
capacitor.cordova.android.plugins.test:attr/dividerVertical = 0x7f03006d
capacitor.cordova.android.plugins.test:attr/checkedTextViewStyle = 0x7f03004b
capacitor.cordova.android.plugins.test:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
capacitor.cordova.android.plugins.test:attr/fontProviderFetchStrategy = 0x7f030085
capacitor.cordova.android.plugins.test:attr/fontProviderQuery = 0x7f030088
capacitor.cordova.android.plugins.test:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
capacitor.cordova.android.plugins.test:attr/buttonStyleSmall = 0x7f030044
capacitor.cordova.android.plugins.test:drawable/abc_textfield_search_material = 0x7f07004d
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Display1 = 0x7f0d00c1
capacitor.cordova.android.plugins.test:attr/buttonStyle = 0x7f030043
capacitor.cordova.android.plugins.test:attr/buttonTint = 0x7f030045
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0d004f
capacitor.cordova.android.plugins.test:attr/itemPadding = 0x7f03009b
capacitor.cordova.android.plugins.test:style/Platform.V25.AppCompat.Light = 0x7f0d00a9
capacitor.cordova.android.plugins.test:attr/windowFixedHeightMajor = 0x7f03011a
capacitor.cordova.android.plugins.test:id/content = 0x7f080047
capacitor.cordova.android.plugins.test:attr/actionModeSelectAllDrawable = 0x7f030019
capacitor.cordova.android.plugins.test:id/add = 0x7f08003a
capacitor.cordova.android.plugins.test:attr/buttonBarPositiveButtonStyle = 0x7f03003d
capacitor.cordova.android.plugins.test:dimen/tooltip_y_offset_touch = 0x7f060077
capacitor.cordova.android.plugins.test:color/abc_color_highlight_material = 0x7f050004
capacitor.cordova.android.plugins.test:color/ripple_material_light = 0x7f05004c
capacitor.cordova.android.plugins.test:attr/thickness = 0x7f0300f9
capacitor.cordova.android.plugins.test:attr/borderlessButtonStyle = 0x7f030039
capacitor.cordova.android.plugins.test:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070005
capacitor.cordova.android.plugins.test:color/abc_tint_seek_thumb = 0x7f050016
capacitor.cordova.android.plugins.test:attr/editTextStyle = 0x7f03007c
capacitor.cordova.android.plugins.test:id/forever = 0x7f080054
capacitor.cordova.android.plugins.test:attr/searchViewStyle = 0x7f0300d1
capacitor.cordova.android.plugins.test:attr/expandActivityOverflowButtonDrawable = 0x7f03007f
capacitor.cordova.android.plugins.test:attr/barLength = 0x7f030038
capacitor.cordova.android.plugins.test:attr/actionBarTabTextStyle = 0x7f030008
capacitor.cordova.android.plugins.test:color/material_grey_300 = 0x7f05003b
capacitor.cordova.android.plugins.test:attr/queryPatterns = 0x7f0300ca
capacitor.cordova.android.plugins.test:attr/background = 0x7f030033
capacitor.cordova.android.plugins.test:attr/buttonBarButtonStyle = 0x7f03003a
capacitor.cordova.android.plugins.test:attr/autoSizeStepGranularity = 0x7f030031
capacitor.cordova.android.plugins.test:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000c
capacitor.cordova.android.plugins.test:drawable/ic_call_answer = 0x7f070057
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Display3 = 0x7f0d00c3
capacitor.cordova.android.plugins.test:attr/titleMarginEnd = 0x7f030105
capacitor.cordova.android.plugins.test:attr/fontProviderCerts = 0x7f030084
capacitor.cordova.android.plugins.test:attr/colorControlNormal = 0x7f030056
capacitor.cordova.android.plugins.test:dimen/abc_text_size_menu_header_material = 0x7f06004a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.ListMenuView = 0x7f0d0143
capacitor.cordova.android.plugins.test:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
capacitor.cordova.android.plugins.test:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07002a
capacitor.cordova.android.plugins.test:attr/selectableItemBackgroundBorderless = 0x7f0300d4
capacitor.cordova.android.plugins.test:styleable/CheckedTextView = 0x7f0e0012
capacitor.cordova.android.plugins.test:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0d003b
capacitor.cordova.android.plugins.test:attr/actionModeCloseButtonStyle = 0x7f030011
capacitor.cordova.android.plugins.test:id/CTRL = 0x7f080001
capacitor.cordova.android.plugins.test:attr/spinBars = 0x7f0300db
capacitor.cordova.android.plugins.test:attr/isLightTheme = 0x7f03009a
capacitor.cordova.android.plugins.test:drawable/tooltip_frame_light = 0x7f07006c
capacitor.cordova.android.plugins.test:dimen/abc_star_small = 0x7f06003d
capacitor.cordova.android.plugins.test:drawable/notify_panel_notification_icon_bg = 0x7f070069
capacitor.cordova.android.plugins.test:attr/actionViewClass = 0x7f030022
capacitor.cordova.android.plugins.test:style/Platform.V25.AppCompat = 0x7f0d00a8
capacitor.cordova.android.plugins.test:attr/drawableRightCompat = 0x7f030071
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0d0069
capacitor.cordova.android.plugins.test:attr/switchMinWidth = 0x7f0300e8
capacitor.cordova.android.plugins.test:style/Theme.AppCompat.Light.NoActionBar = 0x7f0d0108
capacitor.cordova.android.plugins.test:dimen/disabled_alpha_material_light = 0x7f060059
capacitor.cordova.android.plugins.test:attr/listPreferredItemPaddingEnd = 0x7f0300ab
capacitor.cordova.android.plugins.test:styleable/ViewBackgroundHelper = 0x7f0e002d
capacitor.cordova.android.plugins.test:drawable/abc_ic_menu_overflow_material = 0x7f07001c
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_content_inset_material = 0x7f060000
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_21 = 0x7f080015
capacitor.cordova.android.plugins.test:attr/actionProviderClass = 0x7f030021
capacitor.cordova.android.plugins.test:anim/abc_tooltip_enter = 0x7f01000a
capacitor.cordova.android.plugins.test:attr/toolbarNavigationButtonStyle = 0x7f03010c
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0d004e
capacitor.cordova.android.plugins.test:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
capacitor.cordova.android.plugins.test:attr/multiChoiceItemLayout = 0x7f0300b4
capacitor.cordova.android.plugins.test:attr/buttonBarNeutralButtonStyle = 0x7f03003c
capacitor.cordova.android.plugins.test:layout/abc_list_menu_item_radio = 0x7f0b0011
capacitor.cordova.android.plugins.test:anim/abc_slide_in_bottom = 0x7f010006
capacitor.cordova.android.plugins.test:attr/backgroundTint = 0x7f030036
capacitor.cordova.android.plugins.test:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
capacitor.cordova.android.plugins.test:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
capacitor.cordova.android.plugins.test:attr/actionModeStyle = 0x7f03001c
capacitor.cordova.android.plugins.test:attr/title = 0x7f030102
capacitor.cordova.android.plugins.test:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070039
capacitor.cordova.android.plugins.test:attr/actionModeSplitBackground = 0x7f03001b
capacitor.cordova.android.plugins.test:color/material_blue_grey_900 = 0x7f050036
capacitor.cordova.android.plugins.test:attr/colorError = 0x7f030057
capacitor.cordova.android.plugins.test:id/visible_removing_fragment_view_tag = 0x7f0800b1
capacitor.cordova.android.plugins.test:attr/alertDialogStyle = 0x7f030026
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0d007f
capacitor.cordova.android.plugins.test:attr/windowNoTitle = 0x7f030120
capacitor.cordova.android.plugins.test:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070056
capacitor.cordova.android.plugins.test:attr/arrowHeadLength = 0x7f03002b
capacitor.cordova.android.plugins.test:attr/contentInsetEndWithActions = 0x7f03005e
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Spinner = 0x7f0d0154
capacitor.cordova.android.plugins.test:string/abc_capital_on = 0x7f0c0007
capacitor.cordova.android.plugins.test:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
capacitor.cordova.android.plugins.test:attr/textLocale = 0x7f0300f7
capacitor.cordova.android.plugins.test:attr/iconifiedByDefault = 0x7f030096
capacitor.cordova.android.plugins.test:attr/logo = 0x7f0300af
capacitor.cordova.android.plugins.test:attr/actionOverflowMenuStyle = 0x7f030020
capacitor.cordova.android.plugins.test:attr/buttonBarNegativeButtonStyle = 0x7f03003b
capacitor.cordova.android.plugins.test:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070011
capacitor.cordova.android.plugins.test:attr/textAppearanceListItem = 0x7f0300ee
capacitor.cordova.android.plugins.test:attr/subtitle = 0x7f0300e3
capacitor.cordova.android.plugins.test:attr/actionModeCutDrawable = 0x7f030015
capacitor.cordova.android.plugins.test:attr/alertDialogButtonGroupStyle = 0x7f030024
capacitor.cordova.android.plugins.test:animator/fragment_open_enter = 0x7f020004
capacitor.cordova.android.plugins.test:attr/actionButtonStyle = 0x7f03000b
capacitor.cordova.android.plugins.test:dimen/abc_search_view_preferred_height = 0x7f060036
capacitor.cordova.android.plugins.test:attr/checkMarkCompat = 0x7f030047
capacitor.cordova.android.plugins.test:styleable/PopupWindowBackgroundState = 0x7f0e0023
capacitor.cordova.android.plugins.test:id/screen = 0x7f080076
capacitor.cordova.android.plugins.test:attr/actionBarStyle = 0x7f030005
capacitor.cordova.android.plugins.test:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0d004c
capacitor.cordova.android.plugins.test:attr/listPreferredItemPaddingLeft = 0x7f0300ac
capacitor.cordova.android.plugins.test:color/dim_foreground_material_light = 0x7f05002e
capacitor.cordova.android.plugins.test:id/blocking = 0x7f08003f
capacitor.cordova.android.plugins.test:attr/dropdownListPreferredItemHeight = 0x7f030079
capacitor.cordova.android.plugins.test:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0d0048
capacitor.cordova.android.plugins.test:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
capacitor.cordova.android.plugins.test:attr/color = 0x7f030050
capacitor.cordova.android.plugins.test:attr/listPreferredItemHeightLarge = 0x7f0300a9
capacitor.cordova.android.plugins.test:color/call_notification_decline_color = 0x7f05002a
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.PopupMenu = 0x7f0d0148
capacitor.cordova.android.plugins.test:animator/fragment_fade_enter = 0x7f020002
capacitor.cordova.android.plugins.test:attr/alpha = 0x7f030029
capacitor.cordova.android.plugins.test:drawable/abc_list_selector_holo_light = 0x7f07002f
capacitor.cordova.android.plugins.test:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
capacitor.cordova.android.plugins.test:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0d007b
capacitor.cordova.android.plugins.test:attr/fontProviderFetchTimeout = 0x7f030086
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0d0132
capacitor.cordova.android.plugins.test:attr/actionBarSize = 0x7f030003
capacitor.cordova.android.plugins.test:id/accessibility_custom_action_20 = 0x7f080014
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.ActionBar = 0x7f0d0067
capacitor.cordova.android.plugins.test:attr/spinnerDropDownItemStyle = 0x7f0300dc
capacitor.cordova.android.plugins.test:attr/fontFamily = 0x7f030082
capacitor.cordova.android.plugins.test:attr/autoCompleteTextViewStyle = 0x7f03002d
capacitor.cordova.android.plugins.test:id/title_template = 0x7f0800a6
capacitor.cordova.android.plugins.test:animator/fragment_fade_exit = 0x7f020003
capacitor.cordova.android.plugins.test:string/abc_shareactionprovider_share_with = 0x7f0c0018
capacitor.cordova.android.plugins.test:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
capacitor.cordova.android.plugins.test:style/Widget.Compat.NotificationActionContainer = 0x7f0d015c
capacitor.cordova.android.plugins.test:attr/actionMenuTextColor = 0x7f03000f
capacitor.cordova.android.plugins.test:anim/abc_grow_fade_in_from_bottom = 0x7f010002
capacitor.cordova.android.plugins.test:anim/abc_tooltip_exit = 0x7f01000b
capacitor.cordova.android.plugins.test:styleable/MenuGroup = 0x7f0e001f
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Display2 = 0x7f0d00c2
capacitor.cordova.android.plugins.test:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0d0062
capacitor.cordova.android.plugins.test:drawable/abc_text_select_handle_right_mtrl = 0x7f070048
capacitor.cordova.android.plugins.test:attr/actionModeFindDrawable = 0x7f030016
capacitor.cordova.android.plugins.test:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0d00e4
capacitor.cordova.android.plugins.test:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0d00b9
capacitor.cordova.android.plugins.test:attr/checkMarkTint = 0x7f030048
capacitor.cordova.android.plugins.test:style/Base.DialogWindowTitle.AppCompat = 0x7f0d000a
capacitor.cordova.android.plugins.test:color/abc_hint_foreground_material_dark = 0x7f050007
capacitor.cordova.android.plugins.test:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0d00f2
capacitor.cordova.android.plugins.test:attr/searchIcon = 0x7f0300d0
capacitor.cordova.android.plugins.test:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0d0138
capacitor.cordova.android.plugins.test:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0d0075
capacitor.cordova.android.plugins.test:layout/notification_template_part_chronometer = 0x7f0b0023
capacitor.cordova.android.plugins.test:anim/abc_slide_out_bottom = 0x7f010008
capacitor.cordova.android.plugins.test:attr/dropDownListViewStyle = 0x7f030078
capacitor.cordova.android.plugins.test:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
capacitor.cordova.android.plugins.test:drawable/abc_star_black_48dp = 0x7f07003f
capacitor.cordova.android.plugins.test:attr/actionModeWebSearchDrawable = 0x7f03001e
capacitor.cordova.android.plugins.test:dimen/abc_button_inset_horizontal_material = 0x7f060012
capacitor.cordova.android.plugins.test:dimen/abc_floating_window_z = 0x7f06002f
capacitor.cordova.android.plugins.test:attr/displayOptions = 0x7f030069
capacitor.cordova.android.plugins.test:anim/abc_popup_exit = 0x7f010004
capacitor.cordova.android.plugins.test:attr/listChoiceIndicatorSingleAnimated = 0x7f0300a2
capacitor.cordova.android.plugins.test:id/action_bar_root = 0x7f08002a
capacitor.cordova.android.plugins.test:drawable/notification_action_background = 0x7f07005d
capacitor.cordova.android.plugins.test:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
