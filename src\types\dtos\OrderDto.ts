/**
 * 訂單相關的數據傳輸對象 (DTO)
 * 用於 API 請求和響應的標準格式
 */

import { OrderStatus, PaymentStatus } from '../entities/Order.js';
import { ApiStatus } from '../enums/AppEnums.js';

// 創建訂單請求 DTO
export interface CreateOrderDto {
  items: OrderItemDto[];
  userId?: string;
  restaurantId?: string;
  deliveryAddress?: string;
  paymentMethod?: string;
  notes?: string;
  sessionId?: string;
}

// 訂單項目 DTO
export interface OrderItemDto {
  id?: string;
  name: string;
  quantity: number;
  price: number;
  name_en?: string;
  name_zh?: string;
  name_jp?: string;
  image_url?: string;
  description?: string;
  modifiers?: string[];
  category?: string;
}

// 訂單響應 DTO
export interface OrderResponseDto {
  id: string;
  items: OrderItemDto[];
  totalAmount: number;
  status: OrderStatus;
  timestamp: string; // ISO 字符串格式
  userId?: string;
  userName?: string;
  restaurantId?: string;
  restaurantName?: string;
  deliveryAddress?: string;
  deliveryTime?: string;
  paymentMethod?: string;
  paymentStatus?: PaymentStatus;
  notes?: string;
  sessionId?: string;
}

// 訂單列表響應 DTO
export interface OrderListResponseDto {
  orders: OrderResponseDto[];
  pagination: PaginationDto;
  filters: OrderFiltersDto;
}

// 更新訂單狀態請求 DTO
export interface UpdateOrderStatusDto {
  status: OrderStatus;
  notes?: string;
}

// 訂單搜索/過濾 DTO
export interface OrderFiltersDto {
  userId?: string;
  restaurantId?: string;
  status?: OrderStatus[];
  paymentStatus?: PaymentStatus[];
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
}

// 分頁 DTO
export interface PaginationDto {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 通用 API 響應包裝器
export interface ApiResponseDto<T = any> {
  status: ApiStatus;
  data?: T;
  error?: ErrorDto;
  meta?: ResponseMetaDto;
}

// 錯誤響應 DTO
export interface ErrorDto {
  code: string;
  message: string;
  details?: any;
  context?: string;
  timestamp: string;
  requestId?: string;
}

// 響應元數據 DTO
export interface ResponseMetaDto {
  requestId: string;
  timestamp: string;
  processingTime: number;
  version: string;
  language?: string;
}
