import { Router, Request, Response } from 'express';
import { PromptEngine } from '../services/PromptEngine.js';
import { PromptEngineV2Adapter } from '../services/PromptEngineV2Adapter.js';
import { GeminiServiceV2 } from '../services/GeminiServiceV2.js';
import { MenuData } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import * as fs from 'fs';
import * as path from 'path';

// 擴展 Request 類型以包含 promptEngine 屬性
interface ExtendedRequest extends Request {
  promptEngine?: PromptEngine;
}

const router = Router();
// 創建本地 promptEngine 作為後備
const localPromptEngine = new PromptEngine();
const logger = createLogger('PromptRoute');

// 服務選擇緩存
let cachedPromptService: any = null;
let lastPromptHealthCheck = 0;
const PROMPT_HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5分鐘

// 智能 Prompt 服務選擇：優先使用 GeminiServiceV2，降級到舊的 PromptEngine
async function getActivePromptService() {
  const now = Date.now();
  
  // 如果有緩存且在健康檢查間隔內，直接返回
  if (cachedPromptService && (now - lastPromptHealthCheck) < PROMPT_HEALTH_CHECK_INTERVAL) {
    return cachedPromptService;
  }
  
  try {
    // 嘗試使用新的 PromptEngineV2Adapter
    const adapter = new PromptEngineV2Adapter();
    
    // 進行健康檢查
    const healthStatus = await adapter.healthCheck();
    
    if (healthStatus.v2Available || healthStatus.fallbackAvailable) {
      logger.info('使用 PromptEngineV2Adapter (智能適配)', {
        v2Available: healthStatus.v2Available,
        fallbackAvailable: healthStatus.fallbackAvailable
      });
      
      cachedPromptService = {
        service: adapter,
        type: 'adapter',
        healthStatus
      };
      lastPromptHealthCheck = now;
      return cachedPromptService;
    } else {
      throw new Error('適配器健康檢查失敗');
    }
  } catch (error) {
    logger.warn('PromptEngineV2Adapter 不可用，降級到舊的 PromptEngine', error instanceof Error ? error : new Error(String(error)));
    cachedPromptService = {
      service: localPromptEngine,
      type: 'legacy'
    };
    lastPromptHealthCheck = now;
    return cachedPromptService;
  }
}

// 從請求中獲取 promptEngine 實例或使用智能選擇
async function getPromptEngine(req: ExtendedRequest): Promise<any> {
  if (req.promptEngine) {
    return { service: req.promptEngine, type: 'injected' };
  }
  return await getActivePromptService();
}

// 定義處理器函數
/**
 * 驗證 BDD 語法
 */
const validateBDDHandler = async (req: Request, res: Response) => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
      return;
    }
    
    // 使用智能服務選擇
    let validation;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務驗證 BDD 語法', {
        serviceType: activeService.type
      });
      
      validation = activeService.service.validateBDDSyntax(bddText);
      
    } catch (primaryError) {
      logger.warn('主要服務 BDD 驗證失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      validation = localPromptEngine.validateBDDSyntax(bddText);
      logger.info('成功降級驗證 BDD 語法');
    }
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    logger.error('BDD 驗證錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 驗證 AAprompt 語法
 */
const validateAAPromptHandler = async (req: Request, res: Response) => {
  try {
    const { aaText } = req.body;
    
    if (!aaText) {
      res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
      return;
    }
    
    // 使用智能服務選擇
    let validation;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務驗證 AAprompt 語法', {
        serviceType: activeService.type
      });
      
      validation = activeService.service.validateAAPromptSyntax(aaText);
      
    } catch (primaryError) {
      logger.warn('主要服務 AAprompt 驗證失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      validation = localPromptEngine.validateAAPromptSyntax(aaText);
      logger.info('成功降級驗證 AAprompt 語法');
    }
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    logger.error('AAprompt 驗證錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從 BDD 生成 APPprompt
 */
const generateFromBDDHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
      return;
    }

    // 使用智能服務選擇
    let validation;
    let appPrompt;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務從 BDD 生成 APPprompt', {
        serviceType: activeService.type
      });
      
      // 先驗證 BDD 語法
      validation = activeService.service.validateBDDSyntax(bddText);
      
      if (!validation.valid) {
        res.status(400).json({
          success: false,
          error: '無效的 BDD 語法',
          details: validation.errors
        });
        return;
      }
      
      if (!validation.spec) {
        res.status(400).json({
          success: false,
          error: '無法生成有效的 BDD 規範'
        });
        return;
      }
      
      // 生成 APPprompt
      appPrompt = await activeService.service.generateFromBDD(validation.spec);
      
    } catch (primaryError) {
      logger.warn('主要服務 BDD 生成失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      
      // 降級處理
      validation = localPromptEngine.validateBDDSyntax(bddText);
      
      if (!validation.valid) {
        res.status(400).json({
          success: false,
          error: '無效的 BDD 語法',
          details: validation.errors
        });
        return;
      }
      
      if (!validation.spec) {
        res.status(400).json({
          success: false,
          error: '無法生成有效的 BDD 規範'
        });
        return;
      }
      
      appPrompt = await localPromptEngine.generateFromBDD(validation.spec);
      logger.info('成功降級生成 BDD APPprompt');
    }
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    logger.error('APPprompt 生成錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從 AAprompt 生成 APPprompt
 */
const generateFromAAPromptHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { aaText } = req.body;
    
    if (!aaText) {
      res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
      return;
    }

    // 使用智能服務選擇
    let validation;
    let appPrompt;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務從 AAprompt 生成 APPprompt', {
        serviceType: activeService.type
      });
      
      // 先驗證 AAprompt 語法
      validation = activeService.service.validateAAPromptSyntax(aaText);
      
      if (!validation.valid) {
        res.status(400).json({
          success: false,
          error: '無效的 AAprompt 語法',
          details: validation.errors
        });
        return;
      }
      
      if (!validation.prompt) {
        res.status(400).json({
          success: false,
          error: '無法生成有效的 AAprompt'
        });
        return;
      }
      
      // 生成 APPprompt
      appPrompt = await activeService.service.generateFromAA(validation.prompt);
      
    } catch (primaryError) {
      logger.warn('主要服務 AAprompt 生成失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      
      // 降級處理
      validation = localPromptEngine.validateAAPromptSyntax(aaText);
      
      if (!validation.valid) {
        res.status(400).json({
          success: false,
          error: '無效的 AAprompt 語法',
          details: validation.errors
        });
        return;
      }
      
      if (!validation.prompt) {
        res.status(400).json({
          success: false,
          error: '無法生成有效的 AAprompt'
        });
        return;
      }
      
      appPrompt = await localPromptEngine.generateFromAA(validation.prompt);
      logger.info('成功降級生成 AAprompt APPprompt');
    }
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    logger.error('APPprompt 生成錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從自然語言生成 APPprompt
 */
const generateFromNaturalHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { text } = req.body;
    
    if (!text) {
      res.status(400).json({
        success: false,
        error: '請提供自然語言文本'
      });
      return;
    }

    // 使用智能服務選擇
    let appPrompt;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務從自然語言生成 APPprompt', {
        serviceType: activeService.type
      });
      
      // 生成 APPprompt
      appPrompt = await activeService.service.generateFromNaturalLanguage(text);
      
    } catch (primaryError) {
      logger.warn('主要服務自然語言生成失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      
      appPrompt = await localPromptEngine.generateFromNaturalLanguage(text);
      logger.info('成功降級生成自然語言 APPprompt');
    }
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    logger.error('APPprompt 生成錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 儲存 BDD 文本
 */
const saveBDDHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
      return;
    }
    
    // 使用智能服務選擇
    let validation;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務驗證 BDD 文本', {
        serviceType: activeService.type
      });
      
      // 先驗證 BDD 語法
      validation = activeService.service.validateBDDSyntax(bddText);
      
    } catch (primaryError) {
      logger.warn('主要服務 BDD 驗證失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      validation = localPromptEngine.validateBDDSyntax(bddText);
      logger.info('成功降級驗證 BDD 文本');
    }
    
    // 這裡可以添加儲存到資料庫或文件系統的邏輯
    // 目前只是驗證語法並回傳成功
    
    res.json({
      success: true,
      message: 'BDD 已成功儲存',
      isValid: validation.valid
    });
  } catch (error) {
    logger.error('BDD 儲存錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 儲存 AAprompt 文本
 */
const saveAAPromptHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { aaPromptText } = req.body;
    
    if (!aaPromptText) {
      res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
      return;
    }
    
    // 使用智能服務選擇
    let validation;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務驗證 AAprompt 文本', {
        serviceType: activeService.type
      });
      
      // 先驗證 AAprompt 語法
      validation = activeService.service.validateAAPromptSyntax(aaPromptText);
      
    } catch (primaryError) {
      logger.warn('主要服務 AAprompt 驗證失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
      validation = localPromptEngine.validateAAPromptSyntax(aaPromptText);
      logger.info('成功降級驗證 AAprompt 文本');
    }
    
    // 這裡可以添加儲存到資料庫或文件系統的邏輯
    // 目前只是驗證語法並回傳成功
    
    res.json({
      success: true,
      message: 'AAprompt 已成功儲存',
      isValid: validation.valid
    });
  } catch (error) {
    logger.error('AAprompt 儲存錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 處理客戶訂單 - 使用動態生成的 APPprompt（支援會話隔離）
 */
const processOrderHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { customerRequest, language, sessionId, appPrompt } = req.body;

    if (!customerRequest) {
      res.status(400).json({
        success: false,
        error: '請提供客戶訂單請求'
      });
      return;
    }

    // 生成會話ID（如果沒有提供）
    const finalSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    logger.info('處理訂單請求', {
      sessionId: finalSessionId,
      hasCustomerRequest: !!customerRequest,
      language: language || 'zh-TW',
      hasAppPrompt: !!appPrompt
    });

    // 使用智能服務選擇
    let result;
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務處理訂單', {
        serviceType: activeService.type,
        sessionId: finalSessionId
      });
      
      // 使用動態處理方法處理訂單，傳入會話ID和APPprompt
      result = await activeService.service.processOrderWithDynamicPrompt(
        customerRequest,
        appPrompt, // 直接使用前端傳遞的 APPprompt
        language || 'zh-TW',
        finalSessionId
      );
      
    } catch (primaryError) {
      logger.warn('主要服務訂單處理失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError),
        sessionId: finalSessionId
      });
      
      result = await localPromptEngine.processOrderWithDynamicPrompt(
        customerRequest,
        appPrompt,
        language || 'zh-TW',
        finalSessionId
      );
      logger.info('成功降級處理訂單', { sessionId: finalSessionId });
    }

    res.json({
      success: true,
      result,
      sessionId: finalSessionId
    });
  } catch (error) {
    logger.error('動態訂單處理錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '處理訂單時發生錯誤'
    });
  }
};

/**
 * 獲取BDD預設內容
 */
const getBDDDefaultHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { language } = req.query;

    // 根據語言確定文件名
    let fileName = 'BDD_TW.txt'; // 預設為繁體中文

    if (language === 'en-US') {
      fileName = 'BDD_En.txt';
    } else if (language === 'ja-JP') {
      fileName = 'BDD_JP.txt';
    }

    // 構建文件路徑
    const filePath = path.join(process.cwd(), fileName);

    // 檢查文件是否存在
    if (!fs.existsSync(filePath)) {
      logger.warn('找不到 BDD 語言文件', { fileName, filePath });
      res.status(404).json({
        success: false,
        error: `找不到語言文件: ${fileName}`
      });
      return;
    }

    // 讀取文件內容
    const content = fs.readFileSync(filePath, 'utf-8');
    
    logger.info('成功讀取 BDD 預設內容', { fileName, language });

    res.json({
      success: true,
      content,
      language,
      fileName
    });
  } catch (error) {
    logger.error('讀取BDD預設內容錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 獲取AAprompt預設內容
 */
const getAAPromptDefaultHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { language } = req.query;

    // 根據語言確定文件名
    let fileName = 'AAprompt_TW.txt'; // 預設為繁體中文

    if (language === 'en-US') {
      fileName = 'AAprompt_En.txt';
    } else if (language === 'ja-JP') {
      fileName = 'AAprompt_JP.txt';
    }

    // 構建文件路徑
    const filePath = path.join(process.cwd(), fileName);

    // 檢查文件是否存在
    if (!fs.existsSync(filePath)) {
      logger.warn('找不到 AAprompt 語言文件', { fileName, filePath });
      res.status(404).json({
        success: false,
        error: `找不到語言文件: ${fileName}`
      });
      return;
    }

    // 讀取文件內容
    const content = fs.readFileSync(filePath, 'utf-8');
    
    logger.info('成功讀取 AAprompt 預設內容', { fileName, language });

    res.json({
      success: true,
      content,
      language,
      fileName
    });
  } catch (error) {
    logger.error('讀取AAprompt預設內容錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 統一的 APPprompt 生成端點，根據 type 決定使用哪個處理函數（支援會話隔離）
 */
const generatePromptHandler = async (req: any, res: Response): Promise<void> => {
  try {
    const { type, content, language, sessionId } = req.body;
    
    if (!type || !content) {
      res.status(400).json({
        success: false,
        error: '請提供類型 (type) 和內容 (content)'
      });
      return;
    }
    
    if (!['bdd', 'aaprompt', 'natural'].includes(type)) {
      res.status(400).json({
        success: false,
        error: '無效的類型，支援的類型：bdd, aaprompt, natural'
      });
      return;
    }
    
    let appPrompt;
    let menuData;
    
    // 使用智能服務選擇
    try {
      const activeService = await getPromptEngine(req);
      
      logger.info('使用智能服務生成統一 APPprompt', {
        serviceType: activeService.type,
        type,
        sessionId
      });
      
      // 根據類型選擇處理邏輯
      if (type === 'bdd') {
        // 先驗證 BDD 語法
        const validation = activeService.service.validateBDDSyntax(content);
        
        if (!validation.valid) {
          res.status(400).json({
            success: false,
            error: '無效的 BDD 語法',
            details: validation.errors
          });
          return;
        }
        
        if (!validation.spec) {
          res.status(400).json({
            success: false,
            error: '無法生成有效的 BDD 規範'
          });
          return;
        }
        
        appPrompt = await activeService.service.generateFromBDD(validation.spec, language);
      } 
      else if (type === 'aaprompt') {
        // 先驗證 AAprompt 語法
        const validation = activeService.service.validateAAPromptSyntax(content);
        
        if (!validation.valid) {
          res.status(400).json({
            success: false,
            error: '無效的 AAprompt 語法',
            details: validation.errors
          });
          return;
        }
        
        if (!validation.prompt) {
          res.status(400).json({
            success: false,
            error: '無法生成有效的 AAprompt'
          });
          return;
        }
        
        appPrompt = await activeService.service.generateFromAA(validation.prompt, language);
      }
      else { // natural
        // 生成會話ID（如果沒有提供）
        const finalSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        logger.info('生成自然語言 APPprompt', { sessionId: finalSessionId });

        appPrompt = await activeService.service.generateFromNaturalLanguage(content, finalSessionId);
      }
      
      // 取得菜單數據
      menuData = activeService.service.getMenuData();
      
    } catch (primaryError) {
      logger.warn('主要服務生成失敗，降級到傳統引擎', {
        error: primaryError instanceof Error ? primaryError.message : String(primaryError),
        type,
        sessionId
      });
      
      // 降級處理
      if (type === 'bdd') {
        const validation = localPromptEngine.validateBDDSyntax(content);
        
        if (!validation.valid) {
          res.status(400).json({
            success: false,
            error: '無效的 BDD 語法',
            details: validation.errors
          });
          return;
        }
        
        if (!validation.spec) {
          res.status(400).json({
            success: false,
            error: '無法生成有效的 BDD 規範'
          });
          return;
        }
        
        appPrompt = await localPromptEngine.generateFromBDD(validation.spec, language);
      } 
      else if (type === 'aaprompt') {
        const validation = localPromptEngine.validateAAPromptSyntax(content);
        
        if (!validation.valid) {
          res.status(400).json({
            success: false,
            error: '無效的 AAprompt 語法',
            details: validation.errors
          });
          return;
        }
        
        if (!validation.prompt) {
          res.status(400).json({
            success: false,
            error: '無法生成有效的 AAprompt'
          });
          return;
        }
        
        appPrompt = await localPromptEngine.generateFromAA(validation.prompt, language);
      }
      else { // natural
        const finalSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        appPrompt = await localPromptEngine.generateFromNaturalLanguage(content, finalSessionId);
      }
      
      menuData = localPromptEngine.getMenuData();
      logger.info('成功降級生成統一 APPprompt', { type, sessionId });
    }
    
    // 顯示一些調試信息
    logger.info('APPprompt 生成完成', {
      hasMenuData: !!menuData,
      categories: menuData?.categories?.length || 0,
      restaurant: menuData?.restaurant_name
    });
    
    // 將生成的 APPprompt 保存到文件系統
    try {
      // 生成時間戳
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace('T', '').substring(0, 12);
      const filename = `appPrompt_${timestamp}.json`;

      // 確保 appPrompt 目錄存在
      const appPromptDir = path.join(process.cwd(), 'appPrompt');
      if (!fs.existsSync(appPromptDir)) {
        logger.info('創建 appPrompt 目錄');
        fs.mkdirSync(appPromptDir, { recursive: true });
      }

      // 將 APPprompt 寫入文件
      const filePath = path.join(appPromptDir, filename);
      fs.writeFileSync(filePath, JSON.stringify(appPrompt, null, 2));
      logger.info('已將 APPprompt 保存到文件', { filePath });

      // 添加文件路徑到響應
      appPrompt.filePath = path.join('appPrompt', filename);
    } catch (saveError) {
      logger.error('保存 APPprompt 文件時出錯', saveError instanceof Error ? saveError : new Error(String(saveError)));
      // 繼續執行，不因保存失敗而中斷響應
    }
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    logger.error('APPprompt 生成錯誤', error instanceof Error ? error : new Error(String(error)));
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

// 註冊路由
// 使用 Express 應用程式編程介面的正確型式
router.post('/validate-bdd', async function(req, res) {
  await validateBDDHandler(req, res);
});
router.post('/validate-aaprompt', async function(req, res) {
  await validateAAPromptHandler(req, res);
});
router.post('/generate-from-bdd', async function(req, res) {
  await generateFromBDDHandler(req, res);
});
router.post('/generate-from-aaprompt', async function(req, res) {
  await generateFromAAPromptHandler(req, res);
});
router.post('/generate-from-natural', async function(req, res) {
  await generateFromNaturalHandler(req, res);
});
router.post('/generate', async function(req, res) {
  await generatePromptHandler(req, res);
});
router.post('/save-bdd', async function(req, res) {
  await saveBDDHandler(req, res);
});
router.post('/save-aaprompt', async function(req, res) {
  await saveAAPromptHandler(req, res);
});
router.post('/process-order', async function(req, res) {
  await processOrderHandler(req, res);
});
router.get('/bdd-default', async function(req, res) {
  await getBDDDefaultHandler(req, res);
});
router.get('/aaprompt-default', async function(req, res) {
  await getAAPromptDefaultHandler(req, res);
});

export default router;
