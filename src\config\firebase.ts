/**
 * Firebase 配置檔案
 * 用於初始化 Firebase 連接和導出 Firestore 實例
 */

import { initializeApp, FirebaseApp } from 'firebase/app';
import { 
  getFirestore, Firestore, collection, doc, addDoc, getDoc,
  setDoc, updateDoc, deleteDoc, query, where, 
  getDocs, Timestamp
} from 'firebase/firestore';
import dotenv from 'dotenv';

// 確保環境變數已載入
dotenv.config();

// Firebase 配置
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || `${process.env.FIREBASE_PROJECT_ID}.firebaseapp.com`,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
  appId: process.env.FIREBASE_APP_ID || ''
};

// 延遲初始化變數
let app: FirebaseApp | null = null;
let db: Firestore | null = null;
let isInitialized = false;
let initializationError: Error | null = null;

/**
 * 初始化 Firebase（延遲初始化）
 * 在沒有 Google 服務框架的設備上會拋出異常
 */
function initializeFirebase(): void {
  if (isInitialized) {
    if (initializationError) {
      throw initializationError;
    }
    return;
  }

  try {
    app = initializeApp(firebaseConfig);
    db = getFirestore(app);
    isInitialized = true;
  } catch (error) {
    initializationError = error instanceof Error ? error : new Error('Firebase 初始化失敗');
    isInitialized = true;
    throw initializationError;
  }
}

/**
 * 獲取 Firestore 數據庫實例
 * 會觸發 Firebase 初始化（如果尚未初始化）
 */
function getDB(): Firestore {
  initializeFirebase();
  if (!db) {
    throw new Error('Firebase 數據庫未初始化');
  }
  return db;
}

// 導出獲取數據庫的函數而不是直接導出實例
export { getDB as db };

// 匯出常用的 Firestore 函數，方便使用
export {
  collection,
  doc,
  addDoc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  Timestamp
};

export default {
  db: getDB,
  firebaseConfig
};

// Timestamp 已在上面導出，不需要重複導出