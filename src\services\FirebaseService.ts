/**
 * Firebase 資料庫服務
 * 用於管理與 Firebase/Firestore 的連接和資料操作
 */
import { initializeApp } from 'firebase/app';
import {
  getFirestore, collection, doc, addDoc, getDoc,
  setDoc, updateDoc, deleteDoc, query, where, 
  getDocs, Timestamp, DocumentData
} from 'firebase/firestore';
import { MenuData, MenuItem, MenuCategory } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';

/**
 * Firebase 配置接口
 */
interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
}

/**
 * Firebase 資料庫服務類
 * 處理菜單和用戶資料的存儲和檢索
 */
export class FirebaseService {
  private firebaseApp;
  private db;
  private logger = createLogger('FirebaseService');
  
  /**
   * 初始化 Firebase 服務
   * @param config Firebase 配置
   */
  constructor(config: FirebaseConfig) {
    this.firebaseApp = initializeApp(config);
    this.db = getFirestore(this.firebaseApp);
  }
  
  /**
   * 保存菜單資料到 Firestore
   * @param menuData 菜單資料
   * @returns 保存結果
   */
  async saveMenu(menuData: MenuData): Promise<{success: boolean, id?: string, error?: string}> {
    try {
      // 將 Date 對象轉換為 Firestore Timestamp
      const processedData = this.convertDatesToTimestamps(menuData);
      
      // 檢查是否已存在該餐廳的菜單
      const q = query(
        collection(this.db, 'menus'), 
        where('restaurant_id', '==', menuData.restaurant_id)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        // 新增文檔
        const docRef = await addDoc(collection(this.db, 'menus'), processedData);
        return { success: true, id: docRef.id };
      } else {
        // 更新現有文檔
        const docId = querySnapshot.docs[0].id;
        await updateDoc(doc(this.db, 'menus', docId), processedData);
        return { success: true, id: docId };
      }    } catch (error) {
      this.logger.error('保存菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '保存菜單時發生未知錯誤'
      };
    }
  }
  
  /**
   * 獲取菜單資料
   * @param restaurantId 餐廳 ID
   * @returns 菜單資料
   */
  async getMenu(restaurantId: string): Promise<MenuData | null> {
    try {
      const q = query(
        collection(this.db, 'menus'), 
        where('restaurant_id', '==', restaurantId)
      );
      
      const querySnapshot = await getDocs(q);
        if (querySnapshot.empty) {
        this.logger.warn(`找不到餐廳 ID 為 ${restaurantId} 的菜單`);
        return null;
      }
      
      // 獲取文檔數據
      const menuData = querySnapshot.docs[0].data() as MenuData;
      
      // 將 Timestamp 轉換回 Date
      return this.convertTimestampsToDates(menuData);    } catch (error) {
      this.logger.error('獲取菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }
  
  /**
   * 獲取所有可用的菜單
   * @returns 所有菜單數據的列表
   */
  async getAllMenus(): Promise<MenuData[]> {
    try {
      const querySnapshot = await getDocs(collection(this.db, 'menus'));
      const menus: MenuData[] = [];
      
      querySnapshot.forEach(doc => {
        const menuData = doc.data() as MenuData;
        menus.push(this.convertTimestampsToDates(menuData));
      });
      
      return menus;    } catch (error) {
      this.logger.error('獲取所有菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
  
  /**
   * 刪除菜單
   * @param restaurantId 餐廳 ID
   * @returns 刪除操作結果
   */
  async deleteMenu(restaurantId: string): Promise<{success: boolean, error?: string}> {
    try {
      const q = query(
        collection(this.db, 'menus'), 
        where('restaurant_id', '==', restaurantId)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return { 
          success: false, 
          error: `找不到餐廳 ID 為 ${restaurantId} 的菜單` 
        };
      }
      
      // 刪除文檔
      const docId = querySnapshot.docs[0].id;
      await deleteDoc(doc(this.db, 'menus', docId));
      
      return { success: true };    } catch (error) {
      this.logger.error('刪除菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '刪除菜單時發生未知錯誤'
      };
    }
  }
  
  /**
   * 更新菜單項目
   * @param restaurantId 餐廳 ID
   * @param menuItem 菜單項目
   * @param categoryId 分類 ID
   */
  async updateMenuItem(
    restaurantId: string, 
    menuItem: MenuItem, 
    categoryId: string
  ): Promise<{success: boolean, error?: string}> {
    try {
      const menuData = await this.getMenu(restaurantId);
      
      if (!menuData) {
        return { 
          success: false, 
          error: `找不到餐廳 ID 為 ${restaurantId} 的菜單` 
        };
      }
      
      // 尋找並更新項目
      let itemFound = false;
      for (const category of menuData.categories) {
        if (category.id === categoryId) {
          const itemIndex = category.items.findIndex(item => item.id === menuItem.id);
          
          if (itemIndex >= 0) {
            category.items[itemIndex] = menuItem;
            itemFound = true;
            break;
          }
        }
      }
      
      if (!itemFound) {
        return { 
          success: false, 
          error: `找不到要更新的菜單項目 ${menuItem.id}` 
        };
      }
      
      // 保存更新後的菜單
      menuData.last_updated = new Date();
      return await this.saveMenu(menuData);    } catch (error) {
      this.logger.error('更新菜單項目失敗', error instanceof Error ? error : new Error(String(error)));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '更新菜單項目時發生未知錯誤'
      };
    }
  }
  
  /**
   * 將 Date 對象轉換為 Firestore Timestamp
   * @param data 輸入資料
   * @returns 轉換後的資料
   */
  private convertDatesToTimestamps(data: any): any {
    if (data instanceof Date) {
      return Timestamp.fromDate(data);
    }
    
    if (data === null || data === undefined) {
      return data;
    }
    
    if (typeof data === 'object') {
      if (Array.isArray(data)) {
        return data.map(item => this.convertDatesToTimestamps(item));
      }
      
      const result: Record<string, any> = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = this.convertDatesToTimestamps(data[key]);
        }
      }
      
      return result;
    }
    
    return data;
  }
  
  /**
   * 將 Firestore Timestamp 轉換為 Date 對象
   * @param data 輸入資料
   * @returns 轉換後的資料
   */
  private convertTimestampsToDates(data: any): any {
    if (data instanceof Timestamp) {
      return data.toDate();
    }
    
    if (data === null || data === undefined) {
      return data;
    }
    
    if (typeof data === 'object') {
      if (Array.isArray(data)) {
        return data.map(item => this.convertTimestampsToDates(item));
      }
      
      const result: Record<string, any> = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = this.convertTimestampsToDates(data[key]);
        }
      }
      
      return result;
    }
    
    return data;
  }
}
