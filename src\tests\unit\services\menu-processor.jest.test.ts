import { MenuProcessor } from '../../../services/MenuProcessor.js';
import type { MenuProcessingOptions, MenuItem } from '../../../types/menu.js';
import fs from 'node:fs';
import path from 'node:path';

/**
 * MenuProcessor Jest 單元測試
 * 測試菜單處理器的核心功能（只測試公共方法）
 */

describe('MenuProcessor', () => {
  let processor: MenuProcessor;
  let testDir: string;

  // 測試用的 CSV 內容
  const testCSVContent = `id,name_zh,name_en,price,category,description
test-001,測試漢堡,Test Burger,120,漢堡,測試用漢堡
test-002,可樂,Cola,30,飲品,冰涼可樂
test-003,薯條,French Fries,45,配菜,酥脆薯條`;

  const invalidCSVContent = `id,name_zh,price,category
,測試漢堡,120,漢堡
test-002,,30,飲品
test-003,薯條,-10,配菜`;

  const mcdonaldsCSVContent = `id,name_zh,name_en,price,category,description
bm-01,大麥克,Big Mac,110,漢堡,經典大麥克漢堡
bm-02,麥香雞,McC<PERSON>ken,85,漢堡,香嫩雞肉漢堡
dr-01,可口可樂,Coca Cola,35,飲品,經典可樂
dr-02,柳橙汁,Orange Juice,40,飲品,新鮮柳橙汁
sd-01,薯條,French Fries,50,配菜,金黃酥脆薯條`;

  beforeEach(() => {
    processor = new MenuProcessor();
    testDir = path.join(process.cwd(), 'temp-test-jest');
    
    // 確保測試目錄存在
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
  });

  afterEach(() => {
    // 清理測試檔案
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
  });

  // 輔助函數：建立測試檔案
  function createTestFile(content: string, filename: string): string {
    const filePath = path.join(testDir, filename);
    fs.writeFileSync(filePath, content, 'utf-8');
    return filePath;
  }

  describe('initialization', () => {
    test('should initialize MenuProcessor correctly', () => {
      expect(processor).toBeInstanceOf(MenuProcessor);
      expect(processor).toBeDefined();
    });

    test('should have required methods', () => {
      expect(typeof processor.processMenuFile).toBe('function');
    });
  });

  describe('processMenuFile', () => {
    test('should process valid CSV file successfully', async () => {
      const testFile = createTestFile(testCSVContent, 'valid-menu.csv');
      
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
      
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      if (result.data && Array.isArray(result.data)) {
        expect(result.data).toHaveLength(3);
        
        // 檢查第一個項目
        const firstItem = result.data[0] as MenuItem;
        expect(firstItem.id).toBe('test-001');
        expect(firstItem.name_zh).toBe('測試漢堡');
        expect(firstItem.name_en).toBe('Test Burger');
        expect(firstItem.price).toBe(120);
        expect(firstItem.category).toBe('漢堡');
        expect(firstItem.description).toBe('測試用漢堡');
      }
    });

    test('should handle file with validation issues gracefully', async () => {
      const testFile = createTestFile(invalidCSVContent, 'invalid-menu.csv');
      
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
        // 應該回傳結果，但可能有錯誤或警告
      expect(result).toBeDefined();
      // 可能成功或失敗，取決於實作的容錯程度
    });

    test('should handle non-existent file', async () => {
      const nonExistentFile = path.join(testDir, 'non-existent.csv');
      
      const result = await processor.processMenuFile(nonExistentFile, 'test-restaurant');
      // MenuProcessor 可能返回錯誤結果而不是拋出異常
      expect(result).toBeDefined();
      expect(result.success).toBe(false);
    });

    test('should process McDonald\'s style menu', async () => {
      const testFile = createTestFile(mcdonaldsCSVContent, 'mcdonalds-menu.csv');
      
      const result = await processor.processMenuFile(testFile, 'mcdonalds');
      
      expect(result.success).toBe(true);
      
      if (result.data && Array.isArray(result.data)) {
        expect(result.data).toHaveLength(5);
        
        // 檢查漢堡類別
        const burgers = result.data.filter((item: MenuItem) => item.category === '漢堡');
        expect(burgers).toHaveLength(2);
        
        // 檢查飲品類別
        const drinks = result.data.filter((item: MenuItem) => item.category === '飲品');
        expect(drinks).toHaveLength(2);
        
        // 檢查配菜類別
        const sides = result.data.filter((item: MenuItem) => item.category === '配菜');
        expect(sides).toHaveLength(1);
      }
    });

    test('should handle different options', async () => {
      const testFile = createTestFile(testCSVContent, 'test-options.csv');
      
      const options: MenuProcessingOptions = {
        skipValidation: true
      };
      
      const result = await processor.processMenuFile(testFile, 'test-restaurant', options);
      
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });
  });

  describe('error handling', () => {
    test('should handle malformed CSV gracefully', async () => {
      const malformedCSV = `id,name_zh,price
test-001,測試項目,100
test-002,測試項目2`;  // 缺少價格

      const testFile = createTestFile(malformedCSV, 'malformed.csv');
      
      // 應該不會拋出錯誤，而是回傳處理結果
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
      expect(result).toBeDefined();
    });

    test('should handle empty CSV file', async () => {
      const emptyCSV = `id,name_zh,price,category`;

      const testFile = createTestFile(emptyCSV, 'empty.csv');
      
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
      expect(result).toBeDefined();
        if (result.data && Array.isArray(result.data)) {
        expect(result.data).toHaveLength(0);
      }
    });

    test('should handle file permission errors', async () => {
      const testFile = '/root/permission-denied.csv';  // 假設的無權限檔案
      
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
      // MenuProcessor 可能返回錯誤結果而不是拋出異常
      expect(result).toBeDefined();
      expect(result.success).toBe(false);
    });
  });

  describe('performance', () => {
    test('should process large menu efficiently', async () => {
      // 生成大量測試資料
      const largeCSVContent = ['id,name_zh,name_en,price,category,description'];
      for (let i = 1; i <= 500; i++) {
        largeCSVContent.push(`item-${i},項目${i},Item ${i},${50 + i},類別${i % 10},描述${i}`);
      }

      const testFile = createTestFile(largeCSVContent.join('\n'), 'large-menu.csv');
      
      const startTime = Date.now();
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
      const endTime = Date.now();
      
      expect(result.success).toBe(true);
      
      if (result.data && Array.isArray(result.data)) {
        expect(result.data).toHaveLength(500);
      }
      
      expect(endTime - startTime).toBeLessThan(5000); // 應該在 5 秒內完成
    });
  });

  describe('integration scenarios', () => {
    test('should work with realistic restaurant menu', async () => {
      const restaurantCSV = `id,name_zh,name_en,price,category,description
burger-001,經典牛肉堡,Classic Beef Burger,180,主餐,經典牛肉漢堡配薯條
burger-002,雞肉堡,Chicken Burger,160,主餐,香嫩雞肉漢堡
drink-001,可樂,Coca Cola,40,飲品,冰涼可樂
drink-002,咖啡,Coffee,60,飲品,現煮咖啡
side-001,薯條,French Fries,50,配菜,金黃薯條
dessert-001,冰淇淋,Ice Cream,70,甜點,香草冰淇淋`;

      const testFile = createTestFile(restaurantCSV, 'restaurant-menu.csv');
      
      const result = await processor.processMenuFile(testFile, 'my-restaurant');
      
      expect(result.success).toBe(true);
      
      if (result.data && Array.isArray(result.data)) {
        expect(result.data).toHaveLength(6);
        
        // 檢查不同類別
        const categories = [...new Set(result.data.map((item: MenuItem) => item.category))];
        expect(categories).toContain('主餐');
        expect(categories).toContain('飲品');
        expect(categories).toContain('配菜');
        expect(categories).toContain('甜點');
      }
    });

    test('should maintain data integrity', async () => {
      const testFile = createTestFile(testCSVContent, 'integrity-test.csv');
      
      const result = await processor.processMenuFile(testFile, 'test-restaurant');
      
      expect(result.success).toBe(true);
      
      if (result.data && Array.isArray(result.data)) {
        // 確保所有價格都是數字且為正數
        result.data.forEach((item: MenuItem) => {
          expect(typeof item.price).toBe('number');
          expect(item.price).toBeGreaterThan(0);
        });
        
        // 確保所有 ID 都是唯一的
        const ids = result.data.map((item: MenuItem) => item.id);
        const uniqueIds = [...new Set(ids)];
        expect(ids).toHaveLength(uniqueIds.length);
      }
    });
  });
});
