/**
 * IOrderService.ts
 * 統一的訂單服務介面
 * 確保 OrderService、OrderServiceV2 和 MockOrderService 的兼容性
 */

import { Order, OrderItem, OrderStatus } from '../entities/Order.js';

/**
 * 訂單服務統一介面
 */
export interface IOrderService {
  /**
   * 創建新訂單
   * @param items 訂單項目陣列
   * @param userId 用戶ID（可選）
   * @returns 創建的訂單
   */
  createOrder(items: OrderItem[], userId?: string | null): Promise<Order>;

  /**
   * 確認訂單
   * @param orderId 訂單ID
   * @returns 確認後的訂單
   */
  confirmOrder(orderId: string): Promise<Order>;

  /**
   * 獲取訂單詳情
   * @param orderId 訂單ID
   * @returns 訂單詳情或 null
   */
  getOrder(orderId: string): Promise<Order | null>;

  /**
   * 更新訂單狀態
   * @param orderId 訂單ID
   * @param status 新狀態
   */
  updateOrderStatus(orderId: string, status: OrderStatus): Promise<void>;
}

/**
 * 訂單服務工廠
 * 根據環境和配置選擇合適的服務實現
 * 自動檢測Google Services Framework並選擇對應的服務
 */
export class OrderServiceFactory {
  private static instance: IOrderService | null = null;
  private static gmsDetectionResult: any = null;

  /**
   * 獲取訂單服務實例
   * @param preferV2 是否優先使用 V2 版本
   * @param useMock 是否強制使用模擬服務
   * @param forceRefresh 是否強制重新檢測GMS
   */
  static async getOrderService(
    preferV2: boolean = false, 
    useMock: boolean = false,
    forceRefresh: boolean = false
  ): Promise<IOrderService> {
    if (this.instance && !forceRefresh) {
      return this.instance;
    }

    // 如果強制使用Mock服務
    if (useMock) {
      const { default: MockOrderService } = await import('../../services/MockOrderService.js');
      this.instance = MockOrderService;
      return this.instance;
    }

    // 檢測GMS可用性
    if (!this.gmsDetectionResult || forceRefresh) {
      const { default: gmsDetectionService } = await import('../../services/GmsDetectionService.js');
      this.gmsDetectionResult = await gmsDetectionService.detectGmsAvailability(forceRefresh);
    }

    // 根據GMS檢測結果選擇服務
    if (this.gmsDetectionResult.hasGoogleServices && this.gmsDetectionResult.firebaseAvailable) {
      try {
        if (preferV2) {
          const { OrderServiceV2 } = await import('../../services/OrderServiceV2.js');
          this.instance = new OrderServiceV2();
        } else {
          const { default: OrderService } = await import('../../services/OrderService.js');
          this.instance = OrderService;
        }
        
        // 測試服務是否真的可用
        await this.testServiceAvailability(this.instance);
        
        console.log('✅ 使用Firebase訂單服務 (GMS可用)');
        return this.instance;
      } catch (error) {
        console.warn('⚠️ Firebase服務初始化失敗，降級到Mock服務', error);
        // 降級到Mock服務
      }
    } else {
      console.log('ℹ️ GMS不可用，使用Mock訂單服務');
    }

    // 使用Mock服務作為備用方案
    const { default: MockOrderService } = await import('../../services/MockOrderService.js');
    this.instance = MockOrderService;
    return this.instance;
  }

  /**
   * 測試服務可用性
   * @param service 要測試的服務實例
   */
  private static async testServiceAvailability(service: IOrderService): Promise<void> {
    // 這裡可以添加簡單的服務可用性測試
    // 例如嘗試創建一個測試訂單然後立即刪除
    // 目前先簡單返回，實際測試可以在後續添加
    return Promise.resolve();
  }

  /**
   * 獲取當前的GMS檢測結果
   */
  static getGmsDetectionResult(): any {
    return this.gmsDetectionResult;
  }

  /**
   * 重置服務實例（主要用於測試）
   */
  static reset(): void {
    this.instance = null;
    this.gmsDetectionResult = null;
  }
}
