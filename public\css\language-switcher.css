/* 語言切換選單樣式 */
.language-switcher {
    position: relative;
    z-index: 1000;
}

.language-btn {
    background-color: #2196F3;
    color: white;
    border: 2px solid #1976D2;
    border-radius: 20px;
    padding: 10px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 3px 6px rgba(33, 150, 243, 0.4);
    transition: all 0.3s ease;
    height: 44px;
}

.language-btn:hover {
    background-color: #1976D2;
    border-color: #1565C0;
    box-shadow: 0 5px 12px rgba(33, 150, 243, 0.6);
    transform: translateY(-2px);
}

.language-btn .globe-icon {
    margin-right: 5px;
    font-size: 16px;
}

.language-dropdown {
    position: absolute;
    top: 45px;
    right: 0;
    background-color: white;
    border: 2px solid #2196F3;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: none;
    min-width: 140px;
    overflow: hidden;
    z-index: 1001;
}

.language-dropdown.show {
    display: block;
}

.language-option {
    color: #333;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 500;
}

.language-option:hover {
    background-color: #E3F2FD;
    color: #1976D2;
}

.language-option:not(:last-child) {
    border-bottom: 1px solid #E0E0E0;
}