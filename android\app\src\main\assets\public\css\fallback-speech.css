/**
 * 備用語音 UI 樣式
 * 為備用語音服務提供美觀的用戶界面
 */

/* 主容器 */
.fallback-speech-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

.fallback-speech-container.hidden {
  display: none;
}

/* 遮罩層 */
.fallback-speech-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

/* 模態框 */
.fallback-speech-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

/* 標題區域 */
.fallback-speech-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.fallback-speech-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.fallback-speech-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.fallback-speech-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 內容區域 */
.fallback-speech-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 文字輸入區域 */
.fallback-speech-input-section {
  margin-bottom: 32px;
}

.fallback-speech-input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.fallback-speech-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.fallback-speech-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.fallback-speech-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.fallback-speech-send {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.fallback-speech-send:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.fallback-speech-send:active {
  transform: translateY(0);
}

/* 指令區域 */
.fallback-speech-commands-section h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.fallback-speech-commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.fallback-speech-command-btn {
  padding: 12px 16px;
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
  color: #374151;
}

.fallback-speech-command-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.fallback-speech-command-btn:active {
  transform: translateY(0);
  background: #e5e7eb;
}

/* 分類標籤 */
.fallback-speech-categories {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.fallback-speech-category {
  padding: 8px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
}

.fallback-speech-category:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.fallback-speech-category.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

/* 底部區域 */
.fallback-speech-footer {
  padding: 16px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.fallback-speech-tip {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
}

/* 動畫 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .fallback-speech-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .fallback-speech-content {
    padding: 16px;
  }
  
  .fallback-speech-header {
    padding: 16px 20px;
  }
  
  .fallback-speech-commands-grid {
    grid-template-columns: 1fr;
  }
  
  .fallback-speech-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .fallback-speech-send {
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .fallback-speech-modal {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-height: none;
  }
  
  .fallback-speech-content {
    max-height: calc(100vh - 140px);
  }
  
  .fallback-speech-categories {
    justify-content: center;
  }
  
  .fallback-speech-category {
    flex: 1;
    text-align: center;
    min-width: 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .fallback-speech-modal {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .fallback-speech-header {
    border-bottom-color: #374151;
  }
  
  .fallback-speech-input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .fallback-speech-input:focus {
    border-color: #667eea;
  }
  
  .fallback-speech-command-btn {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .fallback-speech-command-btn:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .fallback-speech-category {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .fallback-speech-category:hover {
    background: #4b5563;
  }
  
  .fallback-speech-footer {
    background: #374151;
    border-top-color: #4b5563;
  }
  
  .fallback-speech-tip {
    color: #9ca3af;
  }
}

/* 無障礙支持 */
.fallback-speech-container *:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* 高對比度模式 */
@media (prefers-contrast: high) {
  .fallback-speech-modal {
    border: 2px solid #000;
  }
  
  .fallback-speech-input,
  .fallback-speech-command-btn,
  .fallback-speech-category {
    border-width: 2px;
  }
}

/* 減少動畫模式 */
@media (prefers-reduced-motion: reduce) {
  .fallback-speech-overlay,
  .fallback-speech-modal,
  .fallback-speech-send,
  .fallback-speech-command-btn,
  .fallback-speech-category {
    animation: none;
    transition: none;
  }
}