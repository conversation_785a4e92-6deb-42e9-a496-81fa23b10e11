/**
 * MockFirebaseService.ts
 * 提供模擬的Firebase服務功能，在沒有Google Services Framework的設備上使用
 * 使用localStorage進行數據持久化
 */
import { MenuData, MenuItem, MenuCategory } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';

const logger = createLogger('MockFirebaseService');

/**
 * 本地存儲鍵名常量
 */
const STORAGE_KEYS = {
  MENUS: 'mock_firebase_menus',
  MENU_COUNTER: 'mock_firebase_menu_counter'
} as const;

/**
 * 模擬Firebase服務類
 * 使用localStorage替代Firestore進行數據存儲
 */
export class MockFirebaseService {
  private logger = createLogger('MockFirebaseService');
  
  constructor() {
    this.logger.info('MockFirebaseService 已初始化，使用localStorage進行數據存儲');
  }
  
  /**
   * 保存菜單資料到localStorage
   * @param menuData 菜單資料
   * @returns 保存結果
   */
  async saveMenu(menuData: MenuData): Promise<{success: boolean, id?: string, error?: string}> {
    try {
      // 獲取現有菜單數據
      const existingMenus = this.getStoredMenus();
      
      // 檢查是否已存在該餐廳的菜單
      const existingIndex = existingMenus.findIndex(
        menu => menu.restaurant_id === menuData.restaurant_id
      );
      
      let menuId: string;
      
      if (existingIndex >= 0) {
        // 更新現有菜單
        menuId = existingMenus[existingIndex].id || this.generateMenuId();
        existingMenus[existingIndex] = { ...menuData, id: menuId };
        this.logger.info('更新現有菜單', { restaurantId: menuData.restaurant_id, menuId });
      } else {
        // 新增菜單
        menuId = this.generateMenuId();
        existingMenus.push({ ...menuData, id: menuId });
        this.logger.info('新增菜單', { restaurantId: menuData.restaurant_id, menuId });
      }
      
      // 保存到localStorage
      localStorage.setItem(STORAGE_KEYS.MENUS, JSON.stringify(existingMenus));
      
      return { success: true, id: menuId };
    } catch (error) {
      this.logger.error('保存菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '保存菜單時發生未知錯誤'
      };
    }
  }
  
  /**
   * 獲取菜單資料
   * @param restaurantId 餐廳 ID
   * @returns 菜單資料
   */
  async getMenu(restaurantId: string): Promise<MenuData | null> {
    try {
      const menus = this.getStoredMenus();
      const menu = menus.find(menu => menu.restaurant_id === restaurantId);
      
      if (!menu) {
        this.logger.warn(`找不到餐廳 ID 為 ${restaurantId} 的菜單`);
        return null;
      }
      
      this.logger.debug('成功獲取菜單', { restaurantId });
      return menu;
    } catch (error) {
      this.logger.error('獲取菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }
  
  /**
   * 獲取所有可用的菜單
   * @returns 所有菜單數據的列表
   */
  async getAllMenus(): Promise<MenuData[]> {
    try {
      const menus = this.getStoredMenus();
      this.logger.debug('成功獲取所有菜單', { count: menus.length });
      return menus;
    } catch (error) {
      this.logger.error('獲取所有菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
  
  /**
   * 刪除菜單
   * @param restaurantId 餐廳 ID
   * @returns 刪除操作結果
   */
  async deleteMenu(restaurantId: string): Promise<{success: boolean, error?: string}> {
    try {
      const menus = this.getStoredMenus();
      const menuIndex = menus.findIndex(menu => menu.restaurant_id === restaurantId);
      
      if (menuIndex === -1) {
        return { 
          success: false, 
          error: `找不到餐廳 ID 為 ${restaurantId} 的菜單` 
        };
      }
      
      // 刪除菜單
      menus.splice(menuIndex, 1);
      localStorage.setItem(STORAGE_KEYS.MENUS, JSON.stringify(menus));
      
      this.logger.info('成功刪除菜單', { restaurantId });
      return { success: true };
    } catch (error) {
      this.logger.error('刪除菜單失敗', error instanceof Error ? error : new Error(String(error)));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '刪除菜單時發生未知錯誤'
      };
    }
  }
  
  /**
   * 更新菜單項目
   * @param restaurantId 餐廳 ID
   * @param menuItem 菜單項目
   * @param categoryId 分類 ID
   */
  async updateMenuItem(
    restaurantId: string, 
    menuItem: MenuItem, 
    categoryId: string
  ): Promise<{success: boolean, error?: string}> {
    try {
      const menuData = await this.getMenu(restaurantId);
      
      if (!menuData) {
        return { 
          success: false, 
          error: `找不到餐廳 ID 為 ${restaurantId} 的菜單` 
        };
      }
      
      // 尋找並更新項目
      let itemFound = false;
      for (const category of menuData.categories) {
        if (category.id === categoryId) {
          const itemIndex = category.items.findIndex(item => item.id === menuItem.id);
          
          if (itemIndex >= 0) {
            category.items[itemIndex] = menuItem;
            itemFound = true;
            break;
          }
        }
      }
      
      if (!itemFound) {
        return { 
          success: false, 
          error: `找不到要更新的菜單項目 ${menuItem.id}` 
        };
      }
      
      // 保存更新後的菜單
      menuData.last_updated = new Date();
      return await this.saveMenu(menuData);
    } catch (error) {
      this.logger.error('更新菜單項目失敗', error instanceof Error ? error : new Error(String(error)));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '更新菜單項目時發生未知錯誤'
      };
    }
  }
  
  /**
   * 清除所有本地存儲的數據（用於測試或重置）
   */
  async clearAllData(): Promise<void> {
    try {
      localStorage.removeItem(STORAGE_KEYS.MENUS);
      localStorage.removeItem(STORAGE_KEYS.MENU_COUNTER);
      this.logger.info('已清除所有本地存儲數據');
    } catch (error) {
      this.logger.error('清除數據失敗', error instanceof Error ? error : new Error(String(error)));
    }
  }
  
  /**
   * 獲取存儲的菜單數據
   * @returns 菜單數組
   */
  private getStoredMenus(): MenuData[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.MENUS);
      if (!stored) {
        return [];
      }
      
      const parsed = JSON.parse(stored);
      
      // 確保返回的是數組
      if (!Array.isArray(parsed)) {
        this.logger.warn('存儲的菜單數據格式不正確，重置為空數組');
        return [];
      }
      
      // 轉換日期字符串回Date對象
      return parsed.map(menu => ({
        ...menu,
        created_at: menu.created_at ? new Date(menu.created_at) : new Date(),
        last_updated: menu.last_updated ? new Date(menu.last_updated) : new Date()
      }));
    } catch (error) {
      this.logger.error('解析存儲的菜單數據失敗', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
  
  /**
   * 生成唯一的菜單ID
   * @returns 菜單ID
   */
  private generateMenuId(): string {
    try {
      // 獲取計數器
      const counterStr = localStorage.getItem(STORAGE_KEYS.MENU_COUNTER);
      let counter = counterStr ? parseInt(counterStr, 10) : 0;
      
      // 增加計數器
      counter++;
      localStorage.setItem(STORAGE_KEYS.MENU_COUNTER, counter.toString());
      
      // 生成ID
      const timestamp = Date.now();
      return `mock-menu-${timestamp}-${counter}`;
    } catch (error) {
      // 如果localStorage出錯，使用時間戳作為備用方案
      this.logger.warn('生成菜單ID時localStorage出錯，使用備用方案', error);
      return `mock-menu-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
  }
}

export default new MockFirebaseService();