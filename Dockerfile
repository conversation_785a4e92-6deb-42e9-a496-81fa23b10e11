# 使用官方 Node.js 18 LTS 映像
FROM node:18-alpine

# 設定工作目錄
WORKDIR /app

# 安裝 wget 用於健康檢查
RUN apk add --no-cache wget

# 複製 package.json 和 package-lock.json（如果存在）
COPY package*.json ./

# 安裝所有依賴（包括 devDependencies，用於編譯）
RUN npm ci

# 複製源代碼和配置文件
COPY src/ ./src/
COPY tsconfig.json ./
COPY public/ ./public/

# 確保預設文件存在
COPY BDD_*.txt ./
COPY AAprompt_*.txt ./

# 編譯 TypeScript (使用生產環境配置)
RUN npx tsc -p tsconfig.json

# 移除 devDependencies 以減少映像大小（在編譯後）
RUN npm prune --production && npm cache clean --force

# 創建必要的目錄
RUN mkdir -p uploads appPrompt

# 創建非 root 用戶
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 設定目錄權限
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露端口
EXPOSE 4003

# 設定環境變數
ENV NODE_ENV=production
ENV PORT=4003

# 啟動 GUI 應用程式
CMD ["npm", "run", "start:gui"]