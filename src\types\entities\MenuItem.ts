/**
 * 統一的菜單實體定義
 * 基於現有的 menu.ts 類型，進行標準化和擴展
 */

export interface MenuItem {
  id: string;
  // 多語言名稱支持
  name_zh: string;
  name_en?: string;
  name_jp?: string;
  // 價格信息
  price: number;
  price_jp?: number; // 日文版本可能有不同價格
  // 分類信息
  category: string;
  // 可選的描述信息
  description?: string;
  description_en?: string;
  description_jp?: string;
  // 圖片信息
  image_url?: string;
  image?: string; // 兼容性字段
  // 可用性
  availability?: boolean;
  // 營養信息
  calories?: number;
  allergens?: string[];
  // 尺寸選項 (飲料等)
  size_en?: string;
  size_zh?: string;
  size_jp?: string;
  // 套餐相關
  main_item?: string;
  side_item?: string;
  drink_item?: string;
  // 其他屬性
  isPopular?: boolean;
  isNew?: boolean;
  isSpicy?: boolean;
  isVegetarian?: boolean;
}

export interface MenuCategory {
  id: string;
  // 多語言分類名稱
  name_zh: string;
  name_en?: string;
  name_jp?: string;
  // 分類描述
  description?: string;
  description_en?: string;
  description_jp?: string;
  // 分類項目
  items: MenuItem[];
  // 分類排序
  order?: number;
  // 分類圖片
  image_url?: string;
  // 可用性
  isActive?: boolean;
}

export interface Menu {
  id?: string;
  restaurant_id: string;
  restaurant_name: string;
  restaurant_name_en?: string;
  restaurant_name_jp?: string;
  categories: MenuCategory[];
  // 版本信息
  version: string;
  last_updated: Date;
  // 菜單元數據
  currency?: string;
  timezone?: string;
  // 營業時間
  businessHours?: BusinessHours;
  // 配送信息
  deliveryInfo?: DeliveryInfo;
}

export interface BusinessHours {
  [day: string]: {
    open: string;
    close: string;
    isOpen: boolean;
  };
}

export interface DeliveryInfo {
  isAvailable: boolean;
  minimumOrder?: number;
  deliveryFee?: number;
  estimatedTime?: string;
  coverageAreas?: string[];
}

// 菜單項目搜索結果
export interface MenuSearchResult {
  item: MenuItem;
  category: MenuCategory;
  relevanceScore: number;
  matchType: 'exact' | 'fuzzy' | 'partial';
}

// 菜單驗證結果
export interface MenuValidationResult {
  isValid: boolean;
  errors: MenuValidationError[];
  warnings: MenuValidationWarning[];
}

export interface MenuValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface MenuValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}
