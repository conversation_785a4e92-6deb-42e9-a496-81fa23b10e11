import { FirebaseService } from '../../../services/FirebaseService.js';
import { MockFirebaseService } from '../../helpers/mocks.js';

/**
 * FirebaseService Jest 單元測試
 * 使用 Mock 服務進行測試，避免實際連接 Firebase
 */

// Mock Firebase modules
jest.mock('firebase/app', () => ({
  initializeApp: jest.fn(() => ({ name: 'mock-app' }))
}));

jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(() => ({ name: 'mock-firestore' })),
  collection: jest.fn(),
  doc: jest.fn(),
  addDoc: jest.fn(),
  getDoc: jest.fn(),
  setDoc: jest.fn(),
  updateDoc: jest.fn(),
  deleteDoc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn(),
  Timestamp: {
    now: jest.fn(() => ({ seconds: 1234567890, nanoseconds: 0 }))
  }
}));

describe('FirebaseService', () => {
  let mockService: MockFirebaseService;

  beforeEach(() => {
    mockService = new MockFirebaseService();
  });

  describe('initialization', () => {
    test('should initialize with valid config', () => {
      const config = {
        apiKey: 'test-api-key',
        authDomain: 'test.firebaseapp.com',
        projectId: 'test-project',
        storageBucket: 'test.appspot.com',
        messagingSenderId: '123456789',
        appId: 'test-app-id'
      };

      // 使用實際的 FirebaseService 但 Firebase SDK 已被 mock
      const service = new FirebaseService(config);
      expect(service).toBeInstanceOf(FirebaseService);
    });    test('should handle missing config gracefully', () => {
      // 由於 Firebase SDK 已被 mock，實際的初始化可能不會拋出錯誤
      // 這個測試檢查在 mock 環境下的行為
      try {
        const service = new FirebaseService(null as any);
        expect(service).toBeDefined();
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('menu operations', () => {
    test('should save menu data', async () => {
      const menuData = [
        { id: 'test-001', name_zh: '測試漢堡', price: 120, category: '漢堡' },
        { id: 'test-002', name_zh: '可樂', price: 30, category: '飲品' }
      ];

      const result = await mockService.saveMenu(menuData);
      
      expect(result.success).toBe(true);
      expect(result.id).toBeDefined();
      expect(typeof result.id).toBe('string');
    });

    test('should retrieve menu data', async () => {
      const menuId = 'test-menu-123';
      const result = await mockService.getMenu(menuId);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      
      if (result.data) {
        expect(result.data.length).toBeGreaterThan(0);
        expect(result.data[0]).toHaveProperty('id');
        expect(result.data[0]).toHaveProperty('name_zh');
        expect(result.data[0]).toHaveProperty('price');
      }
    });

    test('should handle non-existent menu', async () => {
      const result = await mockService.getMenu('non-existent-menu');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should update menu item', async () => {
      const menuId = 'test-menu-123';
      const itemId = 'test-001';
      const updateData = {
        name_zh: '更新的漢堡',
        price: 150
      };

      const result = await mockService.updateMenuItem(menuId, itemId, updateData);
      
      expect(result.success).toBe(true);
    });

    test('should delete menu item', async () => {
      const menuId = 'test-menu-123';
      const itemId = 'test-001';

      const result = await mockService.deleteMenuItem(menuId, itemId);
      
      expect(result.success).toBe(true);
    });

    test('should list all menus', async () => {
      const result = await mockService.listMenus();
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      
      if (result.data) {
        result.data.forEach(menu => {
          expect(menu).toHaveProperty('id');
          expect(menu).toHaveProperty('name');
          expect(menu).toHaveProperty('createdAt');
        });
      }
    });
  });

  describe('user operations', () => {
    test('should save user order', async () => {
      const orderData = {
        userId: 'user-123',
        items: [
          { id: 'test-001', name_zh: '測試漢堡', quantity: 2, price: 120 },
          { id: 'test-002', name_zh: '可樂', quantity: 1, price: 30 }
        ],
        total: 270,
        timestamp: new Date()
      };

      const result = await mockService.saveOrder(orderData);
      
      expect(result.success).toBe(true);
      expect(result.orderId).toBeDefined();
    });

    test('should retrieve user orders', async () => {
      const userId = 'user-123';
      const result = await mockService.getUserOrders(userId);
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      
      if (result.data) {
        result.data.forEach(order => {
          expect(order).toHaveProperty('userId');
          expect(order).toHaveProperty('items');
          expect(order).toHaveProperty('total');
          expect(order).toHaveProperty('timestamp');
        });
      }
    });

    test('should handle user with no orders', async () => {
      const result = await mockService.getUserOrders('new-user');
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('error handling', () => {
    test('should handle database connection errors', async () => {
      // 模擬資料庫連接錯誤
      mockService.simulateError = true;
      
      const result = await mockService.saveMenu([]);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error).toContain('連接錯誤');
      
      // 重置錯誤狀態
      mockService.simulateError = false;
    });

    test('should handle invalid data', async () => {
      const invalidMenuData = [
        { id: '', name_zh: '', price: -10 },  // 無效資料
        null,  // null 項目
        undefined  // undefined 項目
      ] as any;

      const result = await mockService.saveMenu(invalidMenuData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle network timeouts', async () => {
      mockService.simulateTimeout = true;
      
      const result = await mockService.getMenu('timeout-test');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('超時');
      
      mockService.simulateTimeout = false;
    });
  });

  describe('performance', () => {
    test('should handle large menu data efficiently', async () => {
      const largeMenuData = [];
      for (let i = 1; i <= 1000; i++) {
        largeMenuData.push({
          id: `item-${i}`,
          name_zh: `項目${i}`,
          price: 50 + i,
          category: `類別${i % 10}`
        });
      }

      const startTime = Date.now();
      const result = await mockService.saveMenu(largeMenuData);
      const endTime = Date.now();
      
      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(1000); // 應該在 1 秒內完成
    });

    test('should handle concurrent operations', async () => {
      const operations = [];
      
      // 同時執行多個操作
      for (let i = 0; i < 10; i++) {
        operations.push(mockService.getMenu(`menu-${i}`));
      }
      
      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(typeof result.success).toBe('boolean');
      });
    });
  });

  describe('data validation', () => {
    test('should validate menu data structure', async () => {
      const validMenuData = [
        {
          id: 'valid-001',
          name_zh: '有效漢堡',
          name_en: 'Valid Burger',
          price: 120,
          category: '漢堡',
          description: '美味漢堡'
        }
      ];

      const result = await mockService.saveMenu(validMenuData);
      
      expect(result.success).toBe(true);
    });

    test('should reject invalid menu structure', async () => {
      const invalidMenuData = [
        {
          // 缺少必要欄位
          name_zh: '無效項目',
          price: 'not-a-number'  // 價格應該是數字
        }
      ] as any;

      const result = await mockService.saveMenu(invalidMenuData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle special characters in data', async () => {
      const specialCharMenuData = [
        {
          id: 'special-001',
          name_zh: '特殊符號測試 !@#$%^&*()',
          name_en: 'Special Chars Test',
          price: 100,
          category: '測試類別 <>&"\'',
          description: 'Testing 中文 & English with symbols 😀🍔'
        }
      ];

      const result = await mockService.saveMenu(specialCharMenuData);
      
      expect(result.success).toBe(true);
    });
  });
});
