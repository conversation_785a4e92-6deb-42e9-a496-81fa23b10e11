/**
 * 重構後的 GeminiService - 示例展示如何應用統一日誌和錯誤處理
 * 這個文件展示如何將現有代碼升級到新的標準
 */

import { GoogleGenerativeAI, GenerationConfig } from '@google/generative-ai';
import { BDDSpec, AAPrompt, APPPromptResult, MenuData } from '../types/menu.js';
import { createLogger } from '../utils/Logger.js';
import { ErrorHandler, createError } from '../utils/ErrorHandler.js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 獲取當前文件的目錄路徑
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加載環境變數
dotenv.config();

/**
 * 重構後的 Gemini AI 服務
 * 使用統一的日誌管理和錯誤處理
 */
export class GeminiServiceRefactored {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private defaultConfig: GenerationConfig;
  private systemPrompt: APPPromptResult | null = null;
  private logger = createLogger('GeminiService');

  constructor() {
    try {
      const apiKey = process.env.GEMINI_API_KEY;
      
      if (!apiKey) {
        throw createError.config('GEMINI_API_KEY environment variable is not set');
      }
      
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ 
        model: process.env.GEMINI_MODEL || 'gemini-1.5-pro' 
      });
      
      this.defaultConfig = {
        temperature: Number(process.env.GEMINI_TEMPERATURE) || 0.1,
        topP: Number(process.env.GEMINI_TOP_P) || 0.8,
        topK: Number(process.env.GEMINI_TOP_K) || 20,
        maxOutputTokens: Number(process.env.GEMINI_MAX_TOKENS) || 2048,
      };
      
      this.logger.info('Gemini service initialized successfully', {
        model: process.env.GEMINI_MODEL || 'gemini-2.0-flash-lite',
        config: this.defaultConfig
      });
      
    } catch (error) {
      const appError = ErrorHandler.handle(error as Error, 'GeminiService constructor');
      this.logger.error('Failed to initialize Gemini service', appError);
      throw appError;
    }
  }

  /**
   * 從 BDD 規範生成 APPprompt - 重構版本
   * 使用統一的錯誤處理和日誌記錄
   */
  async generateFromBDD(
    bddSpec: BDDSpec, 
    menuData?: MenuData | null, 
    language?: string
  ): Promise<APPPromptResult> {
    const startTime = Date.now();
    
    try {
      this.logger.info('Starting BDD to APPprompt generation', {
        feature: bddSpec.feature,
        language: language || 'zh-TW',
        hasMenuData: !!menuData
      });

      // 驗證輸入
      if (!bddSpec.feature || !bddSpec.scenario) {
        throw createError.validation('BDD specification must include feature and scenario');
      }

      // 構建語言指令
      const languageInstruction = this.getLanguageInstruction(language);
      
      // 構建提示詞
      const prompt = this.buildBDDPrompt(bddSpec, menuData, languageInstruction, language);
      
      this.logger.debug('Generated prompt for BDD conversion', {
        promptLength: prompt.length,
        language
      });

      // 調用 Gemini API
      const result = await this.generateContent(prompt);
      
      // 解析結果
      const parsedResult = this.parseAPPPromptResult(result);
      
      const duration = Date.now() - startTime;
      this.logger.performance('BDD to APPprompt generation completed', duration, {
        language,
        success: true,
        resultLength: parsedResult.prompt.length
      });

      this.logger.business('APPprompt generated from BDD', {
        feature: bddSpec.feature,
        language,
        menuItemCount: menuData?.categories?.reduce((sum, cat) => sum + cat.items.length, 0) || 0
      });

      return parsedResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.performance('BDD to APPprompt generation failed', duration, {
        language,
        success: false
      });

      if (error instanceof Error) {
        throw ErrorHandler.handle(error, 'generateFromBDD');
      }
      throw error;
    }
  }

  /**
   * 處理自然語言訂單 - 重構版本
   */
  async processOrder(
    customerInput: string, 
    appPrompt: string, 
    language?: string
  ): Promise<any> {
    const startTime = Date.now();
    
    try {
      this.logger.info('Processing customer order', {
        inputLength: customerInput.length,
        appPromptLength: appPrompt.length,
        language: language || 'auto-detect'
      });

      // 驗證輸入
      if (!customerInput.trim()) {
        throw createError.validation('Customer input cannot be empty');
      }

      if (!appPrompt.trim()) {
        throw createError.validation('APPprompt cannot be empty');
      }

      // 構建完整提示詞
      const fullPrompt = `${appPrompt}\n\n顧客輸入: "${customerInput}"`;
      
      this.logger.debug('Sending request to Gemini API', {
        fullPromptLength: fullPrompt.length,
        language
      });

      // 調用 Gemini API
      const response = await this.generateContent(fullPrompt);
      
      // 解析訂單結果
      const orderResult = this.parseOrderResult(response);
      
      const duration = Date.now() - startTime;
      this.logger.performance('Order processing completed', duration, {
        language,
        success: orderResult.success,
        itemCount: orderResult.data?.matches?.length || 0
      });

      if (orderResult.success) {
        this.logger.business('Order processed successfully', {
          customerInput,
          language,
          totalAmount: orderResult.data?.totalAmount,
          itemCount: orderResult.data?.matches?.length || 0
        });
      } else {
        this.logger.warn('Order processing completed but no valid items found', {
          customerInput,
          language,
          unidentifiedItems: orderResult.data?.unidentified?.length || 0
        });
      }

      return orderResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.performance('Order processing failed', duration, {
        language,
        success: false
      });

      if (error instanceof Error) {
        throw ErrorHandler.handle(error, 'processOrder');
      }
      throw error;
    }
  }

  /**
   * 私有方法：獲取語言指令
   */
  private getLanguageInstruction(language?: string): string {
    switch (language) {
      case 'en-US':
        return 'Please generate the APPprompt content in English. All prompt text, feature descriptions, scenarios, and conditions should be in English.';
      case 'ja-JP':
        return 'APPpromptの内容を日本語で生成してください。プロンプトテキスト、機能説明、シナリオ、条件はすべて日本語で記述してください。';
      default:
        return '請以繁體中文生成APPprompt內容。所有提示詞文字、功能描述、場景和條件都應該使用繁體中文。';
    }
  }

  /**
   * 私有方法：構建 BDD 提示詞
   */
  private buildBDDPrompt(
    bddSpec: BDDSpec,
    menuData: MenuData | null | undefined,
    languageInstruction: string,
    language?: string
  ): string {
    let prompt = `
    ${languageInstruction}
    
    以下是一個 BDD（行為驅動開發）規範，請將其轉換為 APPprompt 格式，用於自然語言點餐系統：

    Feature: ${bddSpec.feature}
    Scenario: ${bddSpec.scenario}
    `;

    if (bddSpec.given && bddSpec.given.length > 0) {
      prompt += `\nGiven: ${bddSpec.given.join('\n')}\n`;
    }
    
    if (bddSpec.when && bddSpec.when.length > 0) {
      prompt += `\nWhen: ${bddSpec.when.join('\n')}\n`;
    }
    
    if (bddSpec.then && bddSpec.then.length > 0) {
      prompt += `\nThen: ${bddSpec.then.join('\n')}\n`;
    }

    // 添加菜單數據（如果有）
    if (menuData && menuData.categories && menuData.categories.length > 0) {
      prompt += this.buildMenuSection(menuData, language);
    }

    return prompt;
  }

  /**
   * 私有方法：構建菜單部分
   */
  private buildMenuSection(menuData: MenuData, language?: string): string {
    let menuSection = `\n當前菜單包含以下項目：\n`;

    menuData.categories.forEach(category => {
      // 根據語言選擇類別名稱
      let categoryName = category.name_zh || '未分類';
      if ((language === 'en-US' || language === 'en') && category.name_en) {
        categoryName = category.name_en;
      } else if (language === 'ja-JP' && category.name_jp) {
        categoryName = category.name_jp;
      }
      
      menuSection += `\n${categoryName}：\n`;
      
      category.items.forEach(item => {
        // 根據語言選擇項目名稱
        let itemName = item.name_zh || '';
        if ((language === 'en-US' || language === 'en') && item.name_en) {
          itemName = item.name_en;
        } else if (language === 'ja-JP' && item.name_jp) {
          itemName = item.name_jp;
        }
        
        menuSection += `- ${itemName} ($${item.price})\n`;
      });
    });

    return menuSection;
  }

  /**
   * 私有方法：調用 Gemini API 生成內容
   */
  private async generateContent(prompt: string): Promise<string> {
    try {
      const result = await this.model.generateContent({
        contents: [{ role: "user", parts: [{ text: prompt }] }],
        generationConfig: this.defaultConfig
      });

      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw createError.geminiApi('Empty response from Gemini API');
      }

      return text;

    } catch (error) {
      if (error instanceof Error) {
        throw createError.geminiApi(error.message, error);
      }
      throw createError.geminiApi('Unknown error occurred while calling Gemini API');
    }
  }

  /**
   * 私有方法：解析 APPprompt 結果
   */
  private parseAPPPromptResult(response: string): APPPromptResult {
    try {      // 這裡應該包含實際的解析邏輯
      // 現在只是一個示例
      return {
        prompt: response,
        parameters: {
          menu: [],
          language: 'zh-TW'
        },
        metadata: {
          source: 'bdd',
          generatedAt: new Date(),
          aiGenerated: true
        }
      };
    } catch (error) {
      throw createError.processing('Failed to parse APPprompt result', { response });
    }
  }

  /**
   * 私有方法：解析訂單結果
   */
  private parseOrderResult(response: string): any {
    try {
      // 嘗試解析 JSON 響應
      const parsed = JSON.parse(response);
      return {
        success: true,
        data: parsed,
        analysis: {
          aiProcessed: true,
          geminiUsed: true
        }
      };
    } catch (error) {
      this.logger.warn('Failed to parse JSON response, treating as text', { response });
      return {
        success: false,
        data: { matches: [], unidentified: [response] },
        analysis: {
          aiProcessed: true,
          geminiUsed: true,
          parseError: true
        }
      };
    }
  }
}
