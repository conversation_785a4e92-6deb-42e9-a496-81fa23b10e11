
/* Toast 訊息樣式 */
#toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

/* 訂單確認模態窗口樣式 */
#order-confirmation-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 9999;
  justify-content: center;
  align-items: center;
}

.toast {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  min-width: 280px;
  max-width: 400px;
  word-wrap: break-word;
  text-align: left;
  opacity: 1;
  animation: fadeInRight 0.5s;
}

.toast-success {
  background-color: #e8f5e9;
  color: #2E7D32;
  border-left: 5px solid #2E7D32;
}

.toast-error {
  background-color: #ffebee;
  color: #C62828;
  border-left: 5px solid #C62828;
}

.toast-info {
  background-color: #e3f2fd;
  color: #0d47a1;
  border-left: 5px solid #0d47a1;
}

.toast-warning {
  background-color: #fff8e1;
  color: #f57f17;
  border-left: 5px solid #f57f17;
}

.toast-fade-out {
  animation: fadeOutRight 0.5s forwards;
}

/* 動畫效果 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(30px);
  }
}

/* 訂單確認對話框樣式 */
.order-confirmation-content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  padding: 30px;
  max-width: 500px;
  width: 90%;
  position: relative;
  border-left: 5px solid #4CAF50;
}

#order-summary-list {
  margin: 15px 0;
  padding-left: 20px;
}

#order-summary-list li {
  margin-bottom: 5px;
  font-size: 16px;
}

#order-total-amount {
  font-weight: bold;
  font-size: 18px;
  color: #4CAF50;
}

.order-complete-button {
  background-color: #FF9800;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
  font-size: 16px;
  margin-top: 20px;
  transition: background-color 0.3s;
}

.order-complete-button:hover {
  background-color: #F57C00;
}
