import { GeminiService } from './GeminiService.js';
import { createLogger } from '../utils/Logger.js';
import fs from 'fs';
import path from 'path';

const logger = createLogger('NLPService');

/**
 * 簡化的自然語言處理服務
 * 完全依賴 Gemini AI 處理，移除所有舊版文字比對和模糊匹配功能
 */
export class NLPService {
  private geminiService: GeminiService | null = null;
  
  constructor() {
    // 初始化 GeminiService（如果啟用 AI）
    if (process.env.USE_GEMINI_AI === 'true') {
      try {
        this.geminiService = new GeminiService();      } catch (error) {
        logger.error('初始化 Gemini AI 服務失敗', error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  /**
   * APPprompt 優先的點餐處理方法
   * 完全依賴 Gemini AI 處理，不再有後備方案
   * @param input 用戶輸入的自然語言文本
   * @param language 語言設定（可選）
   */
  async processOrderWithAppPromptFirst(input: string, language?: string): Promise<{
    success: boolean;
    useAI: boolean;
    data: any;
    analysis?: any;
    error?: string;
  }> {
    logger.info('開始 Gemini AI 處理', { inputLength: input.length });
    
    try {
      // 1. 檢查是否有可用的 APPprompt
      const appPromptAvailable = await this.checkAppPromptAvailability();
      
      if (!appPromptAvailable) {
        return {
          success: false,
          useAI: false,
          data: { matches: [], unidentified: [input] },
          error: '沒有可用的 APPprompt，無法處理點餐請求',
          analysis: {
            aiProcessed: false,
            appPromptUsed: false,
            method: 'no_appprompt'
          }
        };
      }

      logger.info('發現可用的 APPprompt，使用 AI 處理');
      
      // 2. 使用 GeminiService 處理 APPprompt
      if (!this.geminiService) {
        throw new Error('Gemini AI 服務未初始化');
      }

      // 加載並使用 APPprompt
      const appPromptData = await this.loadLatestAppPrompt(language);
      if (!appPromptData) {
        throw new Error('無法加載 APPprompt 數據');
      }

      const aiResult = await this.geminiService.processNaturalLanguageOrder(input, null, appPromptData, undefined, language);
      
      // 🎯 解析 AI 回應中的 ORDER_JSON 並轉換為前端期望的格式
      logger.debug('開始解析 ORDER_JSON');
      const orderJson = this.extractOrderJson(aiResult);
      logger.debug('extractOrderJson 結果', { hasOrderJson: !!orderJson });
      let matches: any[] = [];
      let totalPrice = 0;
      
      if (orderJson && orderJson.items && Array.isArray(orderJson.items)) {
        // 將 ORDER_JSON 格式轉換為前端期望的 matches 格式
        matches = orderJson.items.map((item: any) => ({
          name: item.name,
          name_zh: item.name, // 使用相同的名稱
          price: item.price,
          quantity: item.quantity || 1,
          image: item.image_url || '',
          image_url: item.image_url || '', // 添加 image_url 字段
          description: item.description || ''
        }));
        totalPrice = orderJson.total || 0;        logger.info('成功解析訂單數據', { 
          matchCount: matches.length, 
          totalPrice 
        });
      } else {
        logger.debug('未找到有效的訂單JSON，僅返回自然語言回應');
        logger.debug('orderJson 詳情', { orderJson });
      }
      
      return {
        success: true,
        useAI: true,
        data: {
          matches: matches, // 提供結構化數據給前端顯示
          unidentified: [],
          aiResponse: aiResult, // 保存原始 AI 回應
          totalPrice: totalPrice
        },
        analysis: {
          entities: [],
          intent: "order",
          response: aiResult, // 前端期望的欄位名稱
          aiResponse: aiResult, // 保持向後相容
          aiProcessed: true,
          appPromptUsed: true,
          method: 'gemini_with_json_parsing',
          message: orderJson ? 'Gemini AI 回應已解析為結構化數據' : 'Gemini AI 自然語言回應 - 無JSON數據'
        }
      };
      
    } catch (aiError: any) {
      logger.error('AI 處理失敗', aiError instanceof Error ? aiError : new Error(String(aiError)));
      
      return {
        success: false,
        useAI: true,
        data: { matches: [], unidentified: [input] },
        error: `AI 處理失敗: ${aiError.message}`,
        analysis: {
          aiProcessed: false,
          appPromptUsed: true,
          error: aiError.message
        }
      };
    }
  }

  /**
   * 從 Gemini 回應中提取 ORDER_JSON
   */
  private extractOrderJson(response: string): any | null {
    try {
      const startMarker = 'ORDER_JSON_START';
      const endMarker = 'ORDER_JSON_END';
      
      const startIndex = response.indexOf(startMarker);
      const endIndex = response.indexOf(endMarker);
      
      if (startIndex === -1 || endIndex === -1) {
        logger.debug('未找到 ORDER_JSON 標記');
        return null;
      }
      
      const jsonStr = response.substring(startIndex + startMarker.length, endIndex).trim();
      logger.debug('提取的 JSON 字符串', { jsonLength: jsonStr.length });
      
      return JSON.parse(jsonStr);
    } catch (error) {
      logger.error('JSON 解析失敗', error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * 檢查 APPprompt 是否可用
   */
  private async checkAppPromptAvailability(): Promise<boolean> {
    try {
      // 檢查最新的 APPprompt 文件
      const appPromptPattern = /^appPrompt_\d{12}\.json$/;
      const appPromptDir = path.join(process.cwd(), 'appPrompt');
        // 檢查 appPrompt 目錄是否存在
      if (!fs.existsSync(appPromptDir)) {
        logger.debug('appPrompt 目錄不存在');
        return false;
      }
      
      const files = fs.readdirSync(appPromptDir);
      const appPromptFiles = files.filter(file => appPromptPattern.test(file));
      
      if (appPromptFiles.length > 0) {
        // 找到最新的 APPprompt 文件
        const latestFile = appPromptFiles.sort().pop();
        const filePath = path.join(appPromptDir, latestFile!);
        
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf8');
          const appPromptData = JSON.parse(content);
          
          // 檢查 APPprompt 是否包含菜單數據
          const hasMenuData = (appPromptData.menuData && 
                              appPromptData.menuData.categories && 
                              appPromptData.menuData.categories.length > 0) ||
                             (appPromptData.parameters && 
                              appPromptData.parameters.menu && 
                              appPromptData.parameters.menu.length > 0);
          
          logger.debug('APPprompt 檢查結果', { 
            file: latestFile, 
            hasMenuData 
          });
          return hasMenuData;
        }
      }
      
      logger.debug('未找到有效的 APPprompt 文件');
      return false;
    } catch (error: any) {
      logger.error('APPprompt 檢查失敗', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 載入最新的 APPprompt 數據
   */
  private async loadLatestAppPrompt(language?: string): Promise<string | null> {
    try {
      const appPromptDir = path.join(process.cwd(), 'appPrompt');
      // 檢查 appPrompt 目錄是否存在
      if (!fs.existsSync(appPromptDir)) {
        logger.debug('appPrompt 目錄不存在 (loadLatestAPPprompt)');
        return null;
      }
      
      // 根據語言選擇對應的提示詞文件
      let targetFile: string | null = null;
      
      if (language) {
        switch (language.toLowerCase()) {
          case 'en':
          case 'en-us':
          case 'english':
            targetFile = 'appPrompt_English.json';
            break;
          case 'ja':
          case 'ja-jp':
          case 'japanese':
            targetFile = 'appPrompt_Japanese.json';
            break;
          case 'zh':
          case 'zh-tw':
          case 'zh-cn':
          case 'chinese':
          default:
            // 中文或預設情況，使用時間戳版本
            break;
        }
      }
      
      // 如果指定了特定語言文件，先嘗試載入
      if (targetFile) {
        const targetPath = path.join(appPromptDir, targetFile);
        if (fs.existsSync(targetPath)) {
          logger.info('載入指定語言的 APPprompt 文件', { filename: targetFile, language });
          console.log(`[NLPService] 成功載入語言檔案: ${targetFile} (語言: ${language})`);
          const content = fs.readFileSync(targetPath, 'utf8');
          const appPromptData = JSON.parse(content);
          console.log(`[NLPService] 檔案內容載入完成，prompt 長度: ${appPromptData.prompt ? appPromptData.prompt.length : 0} 字元`);
          return JSON.stringify(appPromptData);
        } else {
          logger.warn('指定語言的 APPprompt 文件不存在，回退到預設文件', { filename: targetFile, language });
          console.log(`[NLPService] 警告: 找不到語言檔案 ${targetFile}，將使用預設檔案`);
        }
      }
      
      // 回退邏輯：尋找最新的時間戳 APPprompt 文件
      const appPromptPattern = /^appPrompt_\d{12}\.json$/;
      const files = fs.readdirSync(appPromptDir);
      const appPromptFiles = files.filter(file => appPromptPattern.test(file));
      
      if (appPromptFiles.length === 0) {
        logger.debug('未找到任何 APPprompt 文件');
        return null;
      }
      
      // 獲取最新的文件（按文件名排序，最新的會在最後）
      const latestFile = appPromptFiles.sort().pop();
      const filePath = path.join(appPromptDir, latestFile!);
      
      logger.info('載入預設 APPprompt 文件', { file: latestFile });
      
      const content = fs.readFileSync(filePath, 'utf8');
      const appPromptData = JSON.parse(content);
      
      // 將 APPprompt 對象轉換為字符串，包含完整的系統提示和菜單信息
      return JSON.stringify(appPromptData);
    } catch (error: any) {
      logger.error('載入 APPprompt 失敗', error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }
}
