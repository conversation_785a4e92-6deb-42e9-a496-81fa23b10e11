/**
 * test-runner.ts
 * 簡化的測試執行器 - 主要委託給 Jest 測試框架
 * 保留向後兼容性，但建議直接使用 Jest
 */

import { createLogger } from '../../utils/Logger.js';
import { spawn } from 'child_process';
import { promisify } from 'util';

const logger = createLogger('TestRunner');

class TestRunner {
  
  /**
   * 執行 Jest 測試套件
   */
  async runJestTests(testPattern?: string): Promise<boolean> {
    logger.info('🚀 執行 Jest 測試套件');
    
    try {
      const args = ['run', 'test:jest'];
      if (testPattern) {
        args.push('--', '--testNamePattern', testPattern);
      }
      
      const result = await this.runCommand('npm', args);
      
      if (result.success) {
        logger.info('✅ Jest 測試全部通過');
        return true;
      } else {
        logger.error('❌ Jest 測試失敗');
        return false;
      }    } catch (error) {
      logger.error('執行 Jest 測試時發生錯誤', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 執行 Jest 覆蓋率測試
   */
  async runCoverageTests(): Promise<boolean> {
    logger.info('📊 執行覆蓋率測試');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:coverage']);
      
      if (result.success) {
        logger.info('✅ 覆蓋率測試完成');
        return true;
      } else {
        logger.error('❌ 覆蓋率測試失敗');
        return false;
      }    } catch (error) {
      logger.error('執行覆蓋率測試時發生錯誤', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 運行指定的 npm 命令
   */
  private async runCommand(command: string, args: string[]): Promise<{ success: boolean; output: string }> {
    return new Promise((resolve) => {
      const process = spawn(command, args, { 
        stdio: 'inherit',
        shell: true 
      });
      
      process.on('close', (code) => {
        resolve({
          success: code === 0,
          output: ''
        });
      });
      
      process.on('error', (error) => {
        logger.error(`命令執行失敗: ${command} ${args.join(' ')}`, error);
        resolve({
          success: false,
          output: error.message
        });
      });
    });
  }

  /**
   * 執行所有測試（向後兼容）
   */
  async runAllTests(): Promise<void> {
    logger.info('📝 注意：建議直接使用 "npm run test:jest" 執行測試');
    const success = await this.runJestTests();
    if (!success) {
      process.exit(1);
    }
  }

  /**
   * 執行單元測試（向後兼容）
   */
  async runUnitTests(): Promise<void> {
    logger.info('🧪 執行 Jest 單元測試');
    const success = await this.runJestTests('unit');
    if (!success) {
      process.exit(1);
    }
  }

  /**
   * 執行整合測試（向後兼容）
   */
  async runIntegrationTests(): Promise<void> {
    logger.info('🔗 執行 Jest 整合測試');
    const success = await this.runJestTests('integration');
    if (!success) {
      process.exit(1);
    }
  }

  /**
   * 執行端到端測試（向後兼容）
   */
  async runE2ETests(): Promise<void> {
    logger.info('🎯 執行 Jest E2E 測試');
    const success = await this.runJestTests('e2e');
    if (!success) {
      process.exit(1);
    }
  }
}

// CLI 介面
async function main() {
  const runner = new TestRunner();
  const args = process.argv.slice(2);
  
  logger.info('🚀 簡化的測試執行器 - 建議直接使用 npm run test:jest');
  
  if (args.includes('--unit')) {
    await runner.runUnitTests();
  } else if (args.includes('--integration')) {
    await runner.runIntegrationTests();
  } else if (args.includes('--e2e')) {
    await runner.runE2ETests();
  } else if (args.includes('--coverage')) {
    const success = await runner.runCoverageTests();
    if (!success) process.exit(1);
  } else {
    await runner.runAllTests();
  }
}

// 如果直接執行此檔案
if (import.meta.url.endsWith(process.argv[1]) || process.argv[1].includes('test-runner.ts')) {
  main().catch((error) => {
    logger.error('測試執行器失敗', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  });
}

export { TestRunner };
